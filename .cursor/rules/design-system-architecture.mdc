---
description: 
globs: 
alwaysApply: true
---
# Design System & Architecture Guidelines

This document outlines the core design principles, UI components, and styling architecture used in this application. Adhering to these guidelines ensures visual consistency across related applications.

## Styling Framework

- **Tailwind CSS**: The primary utility-first CSS framework used for styling. Configuration can be found in [`tailwind.config.ts`](mdc:tailwind.config.ts).
- **Shadcn UI**: The component library, built on top of Tailwind CSS and Radix UI. Components are typically located in [`src/components/ui`](mdc:src/components/ui). Customizations and base theme variables are defined in [`src/app/globals.css`](mdc:src/app/globals.css). 

## Color Palette

The color system is defined using CSS variables in [`src/app/globals.css`](mdc:src/app/globals.css) and mapped in [`tailwind.config.ts`](mdc:tailwind.config.ts). Dark mode should be the only option (No light mode even though it is supported).

### Base Colors
- **Primary**: `hsl(var(--primary))` (Teal/Green focus, e.g., `#00ba7c`, `#10B981`)
- **Primary Foreground**: `hsl(var(--primary-foreground))` (Usually White)
- **Secondary**: `hsl(var(--secondary))`
- **Secondary Foreground**: `hsl(var(--secondary-foreground))`
- **Secondary Hover**: `hsl(217 23% 24% / 1)`
- **Secondary Darker**: `hsl(var(--secondary-darker))`
- **Background**: `hsl(var(--background))`
- **Background Dark**: `#141823` (Secondary dark variant background color)
- **Foreground**: `hsl(var(--foreground))`

### Component Colors
- **Card**: `hsl(var(--card))`
- **Card Foreground**: `hsl(var(--card-foreground))`
- **Popover**: `hsl(var(--popover))`
- **Popover Foreground**: `hsl(var(--popover-foreground))`
- **Muted**: `hsl(var(--muted))`
- **Muted Foreground**: `hsl(var(--muted-foreground))`
- **Accent**: `#34D399` (Often a lighter green)
- **Accent Foreground**: `hsl(var(--accent-foreground))`
- **Destructive**: `#EF5350` (Red)
- **Destructive Foreground**: `#FFFFFF`
- **Border**: `hsl(var(--border))`
- **Border Bright**: `hsl(var(--border-bright))`
- **Input**: `hsl(var(--input))`
- **Ring**: `hsl(var(--ring))` (Often matches Primary)
- **Link**: `hsl(var(--link))` (Often matches Primary)

### Semantic Colors
- **Bright**: `#10B981` (Bright green)
- **Dark**: `#0D9488` (Darker teal)
- **Price Up**: `#10B981` (Green for positive price changes)
- **Price Down**: `#EF5350` (Red for negative price changes)
- **Buy Button**: `#2DD68C` (Green button for buy actions)
- **Sell Button**: `#EF5350` (Red button for sell actions)
- **Subtext**: `hsl(var(--subtext))` (Subtle text color)
- **Chat**: `hsl(var(--chat))` (Chat-related elements)

### UI Navigation Colors
- **Dropdown**: `hsl(var(--dropdown))`
- **Dropdown Hover**: `hsl(var(--dropdown-hover))`
- **Dropdown Active**: `hsl(var(--dropdown-active))`

### Teal Color Scale
- **Teal 100-900**: Color scale from light to dark teal, e.g., `#cff1e6` to `#03251a`

### Gradients and Charts
- **Gradient**: Defined from `gradient.start` (`#10B981`) to `gradient.end` (`#0D9488`) in [`tailwind.config.ts`](mdc:tailwind.config.ts). Used for elements like `.gradient-text` and `.gradient-border`.
- **Chart Colors**: `chart.1` through `chart.5` for data visualization.

## Typography

- **Primary Font (Sans Serif)**: Montserrat (`--font-montserrat`). Defined in [`tailwind.config.ts`](mdc:tailwind.config.ts).
- **Monospace Font**: JetBrains Mono.
- **Specialty Fonts**: Riosark (`--font-riosark`), Press Start (`--font-press-start`). Use sparingly for specific branding elements.
- **Base Text**: Applied via `@apply text-foreground` on `body` in [`src/app/globals.css`](mdc:src/app/globals.css).
- **Font Smoothing**: Enabled (`-webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;`).
- **Custom Sizes**: Several custom font sizes are defined under `theme.extend.fontSize` in [`tailwind.config.ts`](mdc:tailwind.config.ts) (e.g., `amount-input`, `currency-lg`).

## Layout & Spacing

- **Container**: Centered, `2rem` padding by default, max-width `1400px` (`2xl` screen). Defined in [`tailwind.config.ts`](mdc:tailwind.config.ts).
- **Spacing Scale**: Uses Tailwind's default spacing scale unless overridden.
- **Border Radius**: Defined via CSS variable `--radius` (`0.5rem`) in [`src/app/globals.css`](mdc:src/app/globals.css) and extended in [`tailwind.config.ts`](mdc:tailwind.config.ts) (`xs`, `sm`, `md`, `lg`). Default Shadcn UI components use `--radius`.

## Component Styling

- **Shadcn UI Components**: Utilize components from [`src/components/ui`](mdc:src/components/ui) for common elements like Buttons, Inputs, Cards, Popovers, etc. These components are styled using Tailwind utilities and adhere to the defined color palette and border-radius.
- **Custom Utilities**:
    - `.gradient-border`, `.gradient-border-bottom`, `.gradient-border-transparent`: Apply gradient borders using primary colors. Defined in [`src/app/globals.css`](mdc:src/app/globals.css).
    - `.glow-sm`, `.glow`, `.glow-lg`: Apply box shadows with the primary color for a glow effect. Defined in [`src/app/globals.css`](mdc:src/app/globals.css) and [`tailwind.config.ts`](mdc:tailwind.config.ts).
    - `.glass`, `.background-glass`: Apply backdrop blur and semi-transparent background effects. Defined in [`tailwind.config.ts`](mdc:tailwind.config.ts) plugin.
    - `.gradient-text`: Applies the primary gradient as text color. Defined in [`tailwind.config.ts`](mdc:tailwind.config.ts) plugin.
- **Animations**: Uses `tailwindcss-animate` plugin and custom keyframes/animations defined in [`tailwind.config.ts`](mdc:tailwind.config.ts) (e.g., `accordion-down`, `spin-slow`, `glow`).

## General Structure

- **Next.js App Router**: The application uses the Next.js App Router structure ([`src/app`](mdc:src/app)).
- **UI Components**: Reusable UI elements are primarily located in [`src/components/ui`](mdc:src/components/ui) (Shadcn) and potentially [`src/components/shared`](mdc:src/components/shared) for more complex, application-specific shared components.
- **Utility Functions**: General utility functions should be placed in [`src/lib/utils.ts`](mdc:src/lib/utils.ts). Refer to the `.cursorrules` file for specific instructions on using this file.
- **State Management**: Zustand is used for global and feature-specific state management. Store definitions are located in the [`src/stores`](mdc:src/stores) directory.
