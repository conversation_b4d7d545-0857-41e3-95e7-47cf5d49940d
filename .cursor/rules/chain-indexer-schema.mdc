---
description: 
globs: 
alwaysApply: true
---
# Chain Indexer Database Schema

This document describes the schema for the core tables used by the chain indexer: `transactions`, `receipts`, and `logs`.

---

## Table: transactions

| Column             | Type                       | Nullable | Default | Description                       |
|--------------------|----------------------------|----------|---------|-----------------------------------|
| hash               | character varying(66)      | NO       |         | Primary key, transaction hash     |
| block_number       | bigint                     | NO       |         | Block number                      |
| block_hash         | character varying(66)      | NO       |         | Hash of the block                 |
| transaction_index  | integer                    | NO       |         | Index of transaction in block     |
| from_address       | character varying(42)      | NO       |         | Sender address                    |
| to_address         | character varying(42)      | YES      |         | Recipient address (nullable)      |
| value              | text                       | NO       |         | Value transferred (as string)     |
| gas                | bigint                     | NO       |         | Gas provided                      |
| gas_price          | text                       | NO       |         | Gas price (as string)             |
| nonce              | bigint                     | NO       |         | Sender nonce                      |
| input_data         | text                       | YES      |         | Input data (nullable)             |
| timestamp          | timestamp                  | NO       |         | Block timestamp                   |
| created_at         | timestamp                  | NO       | now()   | Row creation time                 |

**Indexes:**
- PRIMARY KEY: (hash)
- block_number
- from_address
- to_address (where not null)
- value
- timestamp
- input_data prefix (first 256 chars)

**Foreign Keys:**
- block_number → blocks(number) (ON DELETE CASCADE)

**Referenced by:**
- logs.transaction_hash (ON DELETE CASCADE)
- receipts.transaction_hash (ON DELETE CASCADE)

---

## Table: receipts

| Column              | Type                       | Nullable | Default | Description                       |
|---------------------|----------------------------|----------|---------|-----------------------------------|
| transaction_hash    | character varying(66)      | NO       |         | Primary key, references tx hash   |
| status              | bigint                     | NO       |         | Transaction status                |
| cumulative_gas_used | bigint                     | NO       |         | Cumulative gas used in block      |
| gas_used            | bigint                     | NO       |         | Gas used by this transaction      |
| contract_address    | character varying(42)      | YES      |         | Created contract address (nullable)|
| logs_bloom          | text                       | NO       |         | Logs bloom filter                 |
| block_number        | bigint                     | NO       |         | Block number                      |
| created_at          | timestamp                  | NO       | now()   | Row creation time                 |

**Indexes:**
- PRIMARY KEY: (transaction_hash)
- block_number
- contract_address (where not null)

**Foreign Keys:**
- transaction_hash → transactions(hash) (ON DELETE CASCADE)

---

## Table: logs

| Column            | Type                       | Nullable | Default | Description                       |
|-------------------|----------------------------|----------|---------|-----------------------------------|
| id                | integer                    | NO       | nextval('logs_id_seq') | Primary key |
| transaction_hash  | character varying(66)      | NO       |         | References transactions(hash)     |
| log_index         | integer                    | NO       |         | Log index in transaction          |
| address           | character varying(42)      | NO       |         | Contract address emitting log     |
| topics            | jsonb                      | NO       |         | Array of indexed topics           |
| data              | text                       | YES      |         | Log data (nullable)               |
| removed           | boolean                    | NO       |         | Log removed flag                  |
| block_number      | bigint                     | NO       |         | Block number                      |
| created_at        | timestamp                  | NO       | now()   | Row creation time                 |

**Indexes:**
- PRIMARY KEY: (id)
- address
- block_number
- topics (gin, jsonb_path_ops)
- (transaction_hash, log_index) (unique)

**Foreign Keys:**
- transaction_hash → transactions(hash) (ON DELETE CASCADE)

---

This schema is used for EVM-compatible chain indexing and is referenced by backend and API services for querying on-chain activity.
