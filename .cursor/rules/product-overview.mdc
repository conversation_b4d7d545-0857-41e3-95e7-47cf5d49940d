---
description: 
globs: 
alwaysApply: true
---
# LiquidLaunch Product Overview

LiquidLaunch is a cryptocurrency fair launch platform built on the HyperEVM chain. The platform enables users to fairly launch and seed liquidity for new tokens on HyperEVM with transparency, speed, and minimal friction.

## Core Features

### Token Launch Capabilities
- **Zero-Cost Token Creation**: Launch new tokens without upfront costs
- **Real-Time Token Deployment**: Create tokens with immediate availability
- **Automated Liquidity Bonding**: Tokens automatically deploy to DEXs when reaching 100% on bonding curve
- **DEX Integration**: Support for HyperSwapV2 and KittenSwapV2 for liquidity deployment
- **Rapid Trading Availability**: Tokens become tradeable within 1-5 minutes after completing bonding

### Trading & Analytics
- **Real-Time Chart Data**: Live price charts and trading information
- **High-Speed Trading**: Fast transaction execution on HyperEVM
- **Portfolio Tracking**: Transparent portfolio management for users
- **Performance Transparency**: View your own and other users' trading activities
- **Platform Statistics**: Overview of total users, trades, and volume

### Community Features
- **Token-Specific Chat Rooms**: Dedicated chat space for each token
- **Global Chat**: Platform-wide discussion for general market and platform topics
- **Leaderboard System**: Transparent ranking based on trading volume
- **User Status Tiers**: Status assignments based on trading activity

## Technical Architecture

The platform leverages HyperEVM for high-performance execution and integration with the broader Hyperliquid ecosystem:

- **HyperEVM Chain**: The underlying blockchain for token creation and trading
- **Real-Time Data Processing**: For charts and trading information
- **Automated DEX Deployment**: Smart contract integration for liquidity bonding

## User Flows

1. **Token Creation**:
   - Configure token parameters (Token name, symbol, description, social links)
   - Bonding curve is automatically set no user intervention
   - Select target DEX for liquidity (HyperSwapV2 or KittenSwapV2)
   - Launch token

2. **Token Participation**:
   - Contribute to bonding curve
   - Participate in discussions via token chat
   - Monitor launch progress in real-time

3. **Trading & Portfolio Management**:
   - Trade tokens on integrated DEXs
   - Track portfolio performance
   - View personal and platform-wide statistics

4. **Community Engagement**:
   - Participate in token and global chats
   - Track status on leaderboard
   - View transparent platform metrics

This frontend repository contains the user interface for all these features, connecting to backend services for blockchain interaction, real-time data, and user management.
