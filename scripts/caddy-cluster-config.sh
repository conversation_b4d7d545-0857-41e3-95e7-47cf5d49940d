#!/bin/bash

# Caddy Cluster Configuration Script for PM2 Cluster Mode
# Usage: ./scripts/caddy-cluster-config.sh [environment] [color] [instances]
# Example: ./scripts/caddy-cluster-config.sh production blue 2

set -e

ENVIRONMENT=${1:-production}
COLOR=${2:-blue}
INSTANCES=${3:-2}

# Configuration
CADDY_API="http://localhost:2019"

if [ "$ENVIRONMENT" = "production" ]; then
    DOMAIN="liquidlaunch.app"
    BASE_PORT=7500
    if [ "$COLOR" = "green" ]; then
        BASE_PORT=7520
    fi
else
    DOMAIN="dev.liquidlaunch.app"
    BASE_PORT=7600
    if [ "$COLOR" = "green" ]; then
        BASE_PORT=7610
    fi
fi

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Generate upstream configuration for cluster instances
generate_upstreams() {
    local upstreams=""
    for ((i=0; i<INSTANCES; i++)); do
        local port=$((BASE_PORT + i))
        if [ $i -eq 0 ]; then
            upstreams="\"dial\": \"localhost:$port\""
        else
            upstreams="$upstreams, \"dial\": \"localhost:$port\""
        fi
    done
    echo "[$upstreams]"
}

# Create Caddy configuration for cluster
create_cluster_config() {
    local upstreams=$(generate_upstreams)
    
    cat > /tmp/caddy-cluster-config.json << EOF
{
  "@id": "$DOMAIN",
  "handle": [
    {
      "handler": "reverse_proxy",
      "load_balancing": {
        "selection_policy": {
          "policy": "round_robin"
        },
        "health_checks": {
          "active": {
            "path": "/api/health",
            "interval": "30s",
            "timeout": "5s",
            "max_size": 0
          }
        }
      },
      "upstreams": [
        $(echo $upstreams | sed 's/\[//g' | sed 's/\]//g' | sed 's/"dial"/{&/g' | sed 's/localhost:[0-9]*"/&}/g')
      ]
    }
  ]
}
EOF
}

# Apply configuration to Caddy
apply_config() {
    log "Applying cluster configuration for $ENVIRONMENT $COLOR with $INSTANCES instances"
    
    create_cluster_config
    
    # Apply the configuration
    curl -X PUT "$CADDY_API/config/apps/http/servers/srv0/routes/0" \
        -H "Content-Type: application/json" \
        -d @/tmp/caddy-cluster-config.json || error "Failed to apply Caddy configuration"
    
    # Clean up
    rm -f /tmp/caddy-cluster-config.json
    
    log "Successfully applied cluster configuration"
}

# Validate that all instances are healthy
validate_cluster() {
    log "Validating cluster health..."
    
    local failed=0
    for ((i=0; i<INSTANCES; i++)); do
        local port=$((BASE_PORT + i))
        if ! curl -sf "http://localhost:$port/api/health" > /dev/null; then
            warn "Instance on port $port is not healthy"
            failed=$((failed + 1))
        else
            log "Instance on port $port is healthy"
        fi
    done
    
    if [ $failed -gt 0 ]; then
        error "$failed out of $INSTANCES instances are not healthy"
    fi
    
    log "All $INSTANCES instances are healthy"
}

# Main execution
case "${4:-apply}" in
    "apply")
        apply_config
        ;;
    "validate")
        validate_cluster
        ;;
    "both")
        apply_config
        sleep 5
        validate_cluster
        ;;
    *)
        echo "Usage: $0 [environment] [color] [instances] [action]"
        echo ""
        echo "Environment: production, development (default: production)"
        echo "Color: blue, green (default: blue)"
        echo "Instances: number of cluster instances (default: 2)"
        echo "Action: apply, validate, both (default: apply)"
        echo ""
        echo "Examples:"
        echo "  $0 production blue 2 apply"
        echo "  $0 development green 4 both"
        echo "  $0 production blue 2 validate"
        exit 1
        ;;
esac

log "Cluster configuration completed successfully"
