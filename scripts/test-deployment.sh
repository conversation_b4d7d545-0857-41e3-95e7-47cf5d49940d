#!/bin/bash

# Test Deployment Script
# This script helps test the deployment process locally

set -e

# Configuration
APP_NAME="liquidlaunch"
BLUE_PORT=3001
GREEN_PORT=3002
HEALTH_ENDPOINT="/api/health"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v bun &> /dev/null; then
        log_error "Bun is not installed. Please install Bun first."
        exit 1
    fi
    
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed. Please install PM2 first."
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl is not installed. Please install curl first."
        exit 1
    fi
    
    log_info "All dependencies are available."
}

# Build the application
build_app() {
    log_info "Building application..."
    bun install
    bun run build
    log_info "Build completed successfully."
}

# Test health endpoint
test_health() {
    local port=$1
    local max_attempts=30
    local attempt=1
    
    log_info "Testing health endpoint on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -sf "http://localhost:$port$HEALTH_ENDPOINT" > /dev/null 2>&1; then
            log_info "Health check passed on port $port"
            return 0
        fi
        
        log_warn "Health check attempt $attempt/$max_attempts failed, retrying in 2 seconds..."
        sleep 2
        ((attempt++))
    done
    
    log_error "Health check failed on port $port after $max_attempts attempts"
    return 1
}

# Start application on specific port
start_app() {
    local color=$1
    local port=$2
    
    log_info "Starting $color deployment on port $port..."
    
    # Stop existing process if running
    pm2 delete "$APP_NAME-$color" 2>/dev/null || true
    
    # Start new process
    PORT=$port NODE_ENV=production pm2 start "bun start" --name "$APP_NAME-$color"
    
    # Wait for startup
    sleep 5
    
    # Test health
    if test_health $port; then
        log_info "$color deployment started successfully on port $port"
        return 0
    else
        log_error "Failed to start $color deployment on port $port"
        return 1
    fi
}

# Simulate blue-green deployment
simulate_deployment() {
    log_info "Simulating blue-green deployment..."
    
    # Check current active deployment
    if [ -f "active_color.txt" ]; then
        ACTIVE_COLOR=$(cat active_color.txt)
    else
        ACTIVE_COLOR="blue"
    fi
    
    log_info "Current active deployment: $ACTIVE_COLOR"
    
    # Determine deployment target
    if [ "$ACTIVE_COLOR" = "blue" ]; then
        DEPLOY_COLOR="green"
        DEPLOY_PORT=$GREEN_PORT
    else
        DEPLOY_COLOR="blue"
        DEPLOY_PORT=$BLUE_PORT
    fi
    
    log_info "Deploying to: $DEPLOY_COLOR (port $DEPLOY_PORT)"
    
    # Start new deployment
    if start_app $DEPLOY_COLOR $DEPLOY_PORT; then
        # Update active deployment
        echo "$DEPLOY_COLOR" > active_color.txt
        log_info "Deployment switched to $DEPLOY_COLOR"
        
        # Test the new deployment
        log_info "Testing new deployment..."
        curl -s "http://localhost:$DEPLOY_PORT$HEALTH_ENDPOINT" | jq '.' || echo "Health check response received"
        
        return 0
    else
        log_error "Deployment failed"
        return 1
    fi
}

# Clean up function
cleanup() {
    log_info "Cleaning up test deployments..."
    pm2 delete "$APP_NAME-blue" 2>/dev/null || true
    pm2 delete "$APP_NAME-green" 2>/dev/null || true
    rm -f active_color.txt
    log_info "Cleanup completed."
}

# Show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  check       - Check dependencies"
    echo "  build       - Build the application"
    echo "  deploy      - Simulate blue-green deployment"
    echo "  cleanup     - Clean up test deployments"
    echo "  health      - Test health endpoints"
    echo "  help        - Show this help message"
    echo ""
}

# Main script logic
case "${1:-help}" in
    check)
        check_dependencies
        ;;
    build)
        check_dependencies
        build_app
        ;;
    deploy)
        check_dependencies
        build_app
        simulate_deployment
        ;;
    cleanup)
        cleanup
        ;;
    health)
        log_info "Testing health endpoints..."
        test_health $BLUE_PORT || log_warn "Blue deployment not responding"
        test_health $GREEN_PORT || log_warn "Green deployment not responding"
        ;;
    help)
        show_usage
        ;;
    *)
        log_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
