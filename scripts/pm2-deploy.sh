#!/bin/bash

# PM2 Deployment Management Script for LiquidLaunch
# Usage: ./scripts/pm2-deploy.sh [environment] [action] [color]
# Example: ./scripts/pm2-deploy.sh production start blue

set -e

ENVIRONMENT=${1:-production}
ACTION=${2:-start}
COLOR=${3:-blue}

# Configuration
if [ "$ENVIRONMENT" = "production" ]; then
    CONFIG_FILE="ecosystem.config.js"
    APP_PREFIX="liquidlaunch"
else
    CONFIG_FILE="ecosystem.dev.config.js"
    APP_PREFIX="liquidlaunch-dev"
fi

APP_NAME="$APP_PREFIX-$COLOR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Validate inputs
if [[ ! "$ENVIRONMENT" =~ ^(production|development)$ ]]; then
    error "Environment must be 'production' or 'development'"
fi

if [[ ! "$COLOR" =~ ^(blue|green)$ ]]; then
    error "Color must be 'blue' or 'green'"
fi

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    error "Config file $CONFIG_FILE not found"
fi

case $ACTION in
    "start")
        log "Starting $APP_NAME using $CONFIG_FILE"
        pm2 start "$CONFIG_FILE" --only "$APP_NAME"
        ;;
    "stop")
        log "Stopping $APP_NAME"
        pm2 stop "$APP_NAME" || warn "App $APP_NAME was not running"
        ;;
    "restart")
        log "Restarting $APP_NAME"
        pm2 restart "$APP_NAME" || error "Failed to restart $APP_NAME"
        ;;
    "reload")
        log "Reloading $APP_NAME (zero-downtime)"
        pm2 reload "$APP_NAME" || error "Failed to reload $APP_NAME"
        ;;
    "delete")
        log "Deleting $APP_NAME"
        pm2 delete "$APP_NAME" || warn "App $APP_NAME was not found"
        ;;
    "status")
        log "Status of $APP_NAME"
        pm2 show "$APP_NAME"
        ;;
    "logs")
        log "Showing logs for $APP_NAME"
        pm2 logs "$APP_NAME" --lines 50
        ;;
    "switch")
        # Switch active deployment
        OTHER_COLOR=$([ "$COLOR" = "blue" ] && echo "green" || echo "blue")
        OTHER_APP="$APP_PREFIX-$OTHER_COLOR"
        
        log "Switching from $OTHER_APP to $APP_NAME"
        
        # Start new deployment
        pm2 start "$CONFIG_FILE" --only "$APP_NAME" || error "Failed to start $APP_NAME"
        
        # Wait for health check
        sleep 10
        
        # Stop old deployment
        pm2 stop "$OTHER_APP" || warn "Old app $OTHER_APP was not running"
        
        log "Successfully switched to $APP_NAME"
        ;;
    "health")
        log "Checking health of $APP_NAME cluster"

        # Get the number of instances from PM2
        INSTANCES=$(pm2 show "$APP_NAME" | grep "instances" | awk '{print $3}' || echo "1")

        if [ "$ENVIRONMENT" = "production" ]; then
            if [ "$COLOR" = "blue" ]; then
                BASE_PORT=7500
            else
                BASE_PORT=7510
            fi
        else
            if [ "$COLOR" = "blue" ]; then
                BASE_PORT=7500
            else
                BASE_PORT=7501
            fi
        fi

        # Check health of all instances
        FAILED=0
        for ((i=0; i<INSTANCES; i++)); do
            PORT=$((BASE_PORT + i))
            if ! curl -sf "http://localhost:$PORT/api/health" > /dev/null; then
                warn "Instance on port $PORT is not healthy"
                FAILED=$((FAILED + 1))
            else
                log "Instance on port $PORT is healthy"
            fi
        done

        if [ $FAILED -gt 0 ]; then
            error "$FAILED out of $INSTANCES instances are not healthy"
        fi

        log "All $INSTANCES instances of $APP_NAME are healthy"
        ;;
    "scale")
        SCALE_TO=${5:-2}
        log "Scaling $APP_NAME to $SCALE_TO instances"
        pm2 scale "$APP_NAME" "$SCALE_TO" || error "Failed to scale $APP_NAME"

        # Update Caddy configuration for new instance count
        if [ -f "scripts/caddy-cluster-config.sh" ]; then
            log "Updating Caddy configuration for $SCALE_TO instances"
            ./scripts/caddy-cluster-config.sh "$ENVIRONMENT" "$COLOR" "$SCALE_TO" both
        fi

        log "Successfully scaled $APP_NAME to $SCALE_TO instances"
        ;;
    *)
        echo "Usage: $0 [environment] [action] [color] [instances]"
        echo ""
        echo "Environment: production, development (default: production)"
        echo "Actions:"
        echo "  start    - Start the application"
        echo "  stop     - Stop the application"
        echo "  restart  - Restart the application"
        echo "  reload   - Zero-downtime reload"
        echo "  delete   - Delete the application from PM2"
        echo "  status   - Show application status"
        echo "  logs     - Show application logs"
        echo "  switch   - Switch active deployment (blue/green)"
        echo "  health   - Check application health"
        echo "  scale    - Scale cluster instances (requires instances parameter)"
        echo ""
        echo "Color: blue, green (default: blue)"
        echo "Instances: number of instances for scale action (default: 2)"
        echo ""
        echo "Examples:"
        echo "  $0 production start blue"
        echo "  $0 development switch green"
        echo "  $0 production health blue"
        echo "  $0 production scale blue 4"
        exit 1
        ;;
esac

log "Action '$ACTION' completed for $APP_NAME"
