#!/bin/bash

# Script to set up environment variables on the server
# Usage: ./scripts/setup-env.sh [environment]

ENVIRONMENT=${1:-development}

if [ "$ENVIRONMENT" = "production" ]; then
    ENV_FILE="/home/<USER>/.env.production"
    USER="liquidlaunch-web-prod"
else
    ENV_FILE="/home/<USER>/.env.development"
    USER="liquidlaunch-web-dev"
fi

echo "Setting up environment variables for $ENVIRONMENT"
echo "Environment file: $ENV_FILE"
echo ""

# Check if file exists
if [ -f "$ENV_FILE" ]; then
    echo "Environment file already exists. Current contents:"
    echo "----------------------------------------"
    cat "$ENV_FILE"
    echo "----------------------------------------"
    echo ""
    read -p "Do you want to edit the existing file? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Exiting without changes."
        exit 0
    fi
fi

# Create or update environment file
echo "Creating/updating environment file..."

# Create backup if file exists
if [ -f "$ENV_FILE" ]; then
    cp "$ENV_FILE" "$ENV_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    echo "Backup created: $ENV_FILE.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Create the environment file
cat > "$ENV_FILE" << EOF
# LiquidLaunch Environment Variables - $ENVIRONMENT
# Generated on $(date)

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret-here-change-this
NEXTAUTH_URL=https://$([ "$ENVIRONMENT" = "production" ] && echo "liquidlaunch.app" || echo "alpha.liquidlaunch.app")

# API Configuration
NEXT_PUBLIC_API_CHAT_SERVER_URL=https://$([ "$ENVIRONMENT" = "production" ] && echo "api.liquidlaunch.app" || echo "api-alpha.liquidlaunch.app")
NEXT_PUBLIC_CHAT_WEBSOCKET_URL=wss://$([ "$ENVIRONMENT" = "production" ] && echo "api.liquidlaunch.app" || echo "api-alpha.liquidlaunch.app")

# Blockchain Configuration
NEXT_PUBLIC_BLOCK_EXPLORER_URL=https://etherscan.io

# Database (if needed)
# DATABASE_URL=postgresql://user:password@localhost:5432/liquidlaunch_$([ "$ENVIRONMENT" = "production" ] && echo "prod" || echo "dev")

# External APIs (add your API keys here)
# COINMARKETCAP_API_KEY=your-api-key-here
# ALCHEMY_API_KEY=your-alchemy-key-here
# INFURA_PROJECT_ID=your-infura-project-id

# Logging
LOG_LEVEL=$([ "$ENVIRONMENT" = "production" ] && echo "info" || echo "debug")

# Add your custom environment variables below:
# CUSTOM_VAR=value
EOF

echo ""
echo "Environment file created at: $ENV_FILE"
echo ""
echo "⚠️  IMPORTANT: Please edit the file and update the following:"
echo "   1. NEXTAUTH_SECRET - Generate a secure random string"
echo "   2. Add any API keys you need"
echo "   3. Update database URLs if using a database"
echo ""
echo "To edit the file:"
echo "   nano $ENV_FILE"
echo ""
echo "To generate a secure NEXTAUTH_SECRET:"
echo "   openssl rand -base64 32"
echo ""

# Set proper permissions
chmod 600 "$ENV_FILE"
echo "File permissions set to 600 (owner read/write only)"

# Show next steps
echo ""
echo "Next steps:"
echo "1. Edit the environment file with your actual values"
echo "2. Restart your PM2 processes to load new environment variables:"
echo "   \$(./scripts/find-pm2.sh) restart liquidlaunch-$([ "$ENVIRONMENT" = "production" ] && echo "" || echo "dev-")blue"
echo "   \$(./scripts/find-pm2.sh) restart liquidlaunch-$([ "$ENVIRONMENT" = "production" ] && echo "" || echo "dev-")green"
echo ""
echo "To verify environment variables are loaded:"
echo "   \$(./scripts/find-pm2.sh) show liquidlaunch-$([ "$ENVIRONMENT" = "production" ] && echo "" || echo "dev-")blue | grep -A 20 'env:'"
