#!/bin/bash

# PWA Icon Generator Script
# This script generates all required PWA icons from the main logo
# Requires ImageMagick (install with: sudo apt-get install imagemagick)

SOURCE_LOGO="public/logos/pwa.png"
ICONS_DIR="public/icons"

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "ImageMagick is not installed. Please install it first:"
    echo "Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "macOS: brew install imagemagick"
    echo "Or use an online PWA icon generator with your logo"
    exit 1
fi

# Check if source logo exists
if [ ! -f "$SOURCE_LOGO" ]; then
    echo "Source logo not found at $SOURCE_LOGO"
    exit 1
fi

# Create icons directory if it doesn't exist
mkdir -p "$ICONS_DIR"

# Define the required icon sizes
sizes=(72 96 128 144 152 192 384 512)

echo "Generating PWA icons from $SOURCE_LOGO..."

# Generate icons for each size
for size in "${sizes[@]}"; do
    output_file="$ICONS_DIR/icon-${size}x${size}.png"
    echo "Generating ${size}x${size} icon..."
    convert "$SOURCE_LOGO" -resize "${size}x${size}" -background transparent -gravity center -extent "${size}x${size}" "$output_file"
done

# Generate shortcut icon
echo "Generating shortcut icon..."
convert "$SOURCE_LOGO" -resize "96x96" -background transparent -gravity center -extent "96x96" "$ICONS_DIR/shortcut-create.png"



echo "PWA icons generated successfully!"
echo "Icons created in $ICONS_DIR/"
echo ""
echo "To use this script:"
echo "1. Make it executable: chmod +x scripts/generate-pwa-icons.sh"
echo "2. Run it: ./scripts/generate-pwa-icons.sh" 