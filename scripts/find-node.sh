#!/bin/bash

# <PERSON>ript to find the correct Node.js path for NVM
# Usage: ./scripts/find-node.sh

# Try to find Node.js in NVM
if [ -d "$HOME/.nvm/versions/node" ]; then
    # Find the latest or current Node.js version
    NODE_VERSION=$(ls -1 "$HOME/.nvm/versions/node" | sort -V | tail -1)
    if [ -n "$NODE_VERSION" ] && [ -f "$HOME/.nvm/versions/node/$NODE_VERSION/bin/node" ]; then
        echo "$HOME/.nvm/versions/node/$NODE_VERSION/bin"
        exit 0
    fi
fi

# Fallback to system Node.js
if command -v node >/dev/null 2>&1; then
    NODE_PATH=$(dirname "$(which node)")
    echo "$NODE_PATH"
    exit 0
fi

echo "Node.js not found" >&2
exit 1
