module.exports = {
  apps: [
    {
      name: 'liquidlaunch-dev-blue',
      script: 'bun',
      args: 'start',
      cwd: '/home/<USER>/app/blue',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 7600,
        HOSTNAME: '0.0.0.0',
        // Development environment variables
        NEXT_PUBLIC_API_SERVER_URL: process.env.NEXT_PUBLIC_API_SERVER_URL || 'https://api-dev.liquidlaunch.app',
        NEXT_PUBLIC_CHAT_WEBSOCKET_URL: process.env.NEXT_PUBLIC_CHAT_WEBSOCKET_URL || 'wss://api-dev.liquidlaunch.app',
        NEXT_PUBLIC_BLOCK_EXPLORER_URL: process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'https://dev.liquidlaunch.app',
      },
      error_file: '/home/<USER>/logs/blue-error.log',
      out_file: '/home/<USER>/logs/blue-out.log',
      log_file: '/home/<USER>/logs/blue-combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      health_check_grace_period: 3000,
    },
    {
      name: 'liquidlaunch-dev-green',
      script: 'bun',
      args: 'start',
      cwd: '/home/<USER>/app/green',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 7610,
        HOSTNAME: '0.0.0.0',
        // Development environment variables
        NEXT_PUBLIC_API_SERVER_URL: process.env.NEXT_PUBLIC_API_SERVER_URL || 'https://api-dev.liquidlaunch.app',
        NEXT_PUBLIC_CHAT_WEBSOCKET_URL: process.env.NEXT_PUBLIC_CHAT_WEBSOCKET_URL || 'wss://api-dev.liquidlaunch.app',
        NEXT_PUBLIC_BLOCK_EXPLORER_URL: process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'https://dev.liquidlaunch.app',
      },
      error_file: '/home/<USER>/logs/green-error.log',
      out_file: '/home/<USER>/logs/green-out.log',
      log_file: '/home/<USER>/logs/green-combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      health_check_grace_period: 3000,
    }
  ]
};
