# Deployment Guide

This document outlines the deployment setup for LiquidLaunch using GitHub Actions with blue-green deployments and Caddy for load balancing.

## Overview

The deployment system uses:
- **Blue-Green Deployments**: Zero-downtime deployments with two identical environments
- **Caddy Web Server**: Reverse proxy with API-based configuration for traffic management
- **PM2**: Process manager for Node.js applications
- **GitHub Actions**: CI/CD pipeline automation

## Infrastructure Requirements

### Server Setup

#### Development Server
- **Domain**: `alpha.liquidlaunch.app`
- **Host**: `ssh.web.seipex.fi`
- **User**: `liquidlaunch-web-dev`
- **Ports**: 7600 (blue), 7610 (green)

#### Production Server
- **Domain**: `liquidlaunch.app`
- **Host**: `ssh.web.seipex.fi`
- **User**: `liquidlaunch-web-prod`
- **Ports**: 7500-750X (blue cluster), 7520-752X (green cluster)

### Required Software

1. **Bun** - JavaScript runtime and package manager
2. **PM2** - Process manager
3. **Caddy** - Web server with Admin API enabled
4. **SSH** - For deployment access

## GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

### Required Secrets
- `DEV_SSH_KEY`: SSH private key for `<EMAIL>`
- `PROD_SSH_KEY`: SSH private key for `<EMAIL>`

### Optional Secrets (for enhanced security)
- `SSH_KNOWN_HOSTS`: SSH host key for `ssh.web.seipex.fi` (see below)

**Note**: The host `ssh.web.seipex.fi` is now hardcoded in the workflows.

### SSH Host Key Verification

The workflows automatically handle SSH host key verification using `ssh-keyscan`. For enhanced security, you can optionally add the server's SSH host key as a GitHub secret:

1. **Get the server's SSH host key**:
```bash
ssh-keyscan ssh.web.seipex.fi
```

2. **Add as GitHub secret**:
   - Go to your repository Settings → Secrets and variables → Actions
   - Add a new secret named `SSH_KNOWN_HOSTS`
   - Paste the output from the ssh-keyscan command

3. **Update workflows** (optional):
   If you add the `SSH_KNOWN_HOSTS` secret, you can replace the ssh-keyscan step with:
   ```yaml
   - name: Add SSH host key
     run: |
       mkdir -p ~/.ssh
       echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
       chmod 644 ~/.ssh/known_hosts
   ```

## Server Setup Requirements

### Prerequisites

1. **Install jq** (required for safe Caddy configuration):
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install jq

# CentOS/RHEL
sudo yum install jq
```

2. **Caddy Configuration**:
Your existing Caddyfile should include routes for the domains:

```caddy
{
    admin localhost:2019  # Required for API access
}

alpha.liquidlaunch.app {
    reverse_proxy localhost:7600
    # Your existing configuration...
}

liquidlaunch.app {
    reverse_proxy localhost:7500
    # Your existing configuration...
}

# Your other virtual hosts remain unchanged
other-site.com {
    # Other configurations...
}
```

The deployment scripts will automatically update only the `reverse_proxy` upstreams for the LiquidLaunch domains without affecting other sites.

## Caddy Configuration

### Basic Caddyfile
```caddy
{
    admin localhost:2019
}

alpha.liquidlaunch.app {
    reverse_proxy localhost:7600
}

liquidlaunch.app {
    reverse_proxy localhost:7500
}
```

### Enable Admin API
The Caddy Admin API must be enabled on port 2019 for the deployment scripts to work. This is configured in the global options block.

## Server Directory Structure

```
# Production Server
/home/<USER>/
├── app/
│   ├── blue/                    # Blue deployment
│   │   ├── .next/              # Built Next.js application
│   │   ├── public/             # Static assets
│   │   ├── package.json        # Dependencies
│   │   └── ecosystem.config.js # PM2 configuration
│   └── green/                  # Green deployment
│       ├── .next/
│       ├── public/
│       ├── package.json
│       └── ecosystem.config.js
├── logs/                       # PM2 log files
│   ├── blue-error.log
│   ├── blue-out.log
│   ├── green-error.log
│   └── green-out.log
└── active_color.txt           # Tracks which deployment is active

# Development Server
/home/<USER>/
├── app/
│   ├── blue/
│   └── green/
├── logs/
└── active_color.txt
```

## PM2 Ecosystem Configuration

The project includes PM2 ecosystem files for better process management:

- `ecosystem.config.js` - Production configuration
- `ecosystem.dev.config.js` - Development configuration
- `scripts/pm2-deploy.sh` - Deployment management script

### Ecosystem Features

- **Cluster Mode**: Production uses PM2 cluster mode for better performance
- **Environment-specific configurations**
- **Automatic log rotation**
- **Memory limits and restart policies**
- **Health checks and graceful shutdowns**
- **Load balancing across multiple instances**
- **Auto-scaling capabilities**

### Cluster Configuration

**Production**:
- Blue deployment: Ports 7500-750X (based on instance count)
- Green deployment: Ports 7520-752X (based on instance count)
- Default: 2 instances per deployment

**Development**:
- Blue deployment: Port 7600
- Green deployment: Port 7610
- Default: 1 instance per deployment (fork mode)

## Deployment Process

### Development Deployment
- **Trigger**: Push to `dev` branch
- **Target**: `alpha.liquidlaunch.app`
- **Process**: Simple blue-green switch

### Production Deployment
- **Trigger**: Push to `release` branch
- **Target**: `liquidlaunch.app`
- **Process**: Gradual traffic shifting (10% → 50% → 100%)

## Deployment Steps

1. **Build & Test**: Application is built and tested
2. **Deploy**: Code is deployed to inactive environment
3. **Health Check**: Application health is verified
4. **Traffic Shift**: Traffic is gradually moved to new deployment
5. **Verification**: Final health checks ensure deployment success
6. **Rollback**: Automatic rollback on failure (production only)

## Health Check Endpoint

The deployment system uses `/api/health` for monitoring:

```json
{
  "status": "ok",
  "environment": "production",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "0.1.0",
  "uptime": 3600,
  "memory": {
    "used": 128,
    "total": 256
  },
  "deploymentColor": "blue"
}
```

## Manual Operations

### Using PM2 Ecosystem Files

```bash
# Start production applications
pm2 start ecosystem.config.js

# Start only blue deployment
pm2 start ecosystem.config.js --only liquidlaunch-blue

# Start development applications
pm2 start ecosystem.dev.config.js

# Restart with zero downtime
pm2 reload ecosystem.config.js

# Stop all applications
pm2 stop all

# Delete all applications
pm2 delete all
```

### Using the Deployment Script

```bash
# Start production blue deployment
./scripts/pm2-deploy.sh production start blue

# Switch to green deployment (zero-downtime)
./scripts/pm2-deploy.sh production switch green

# Check health of blue deployment
./scripts/pm2-deploy.sh production health blue

# View logs for green deployment
./scripts/pm2-deploy.sh production logs green

# Restart development blue
./scripts/pm2-deploy.sh development restart blue

# Scale production blue to 4 instances
./scripts/pm2-deploy.sh production scale blue 4
```

### Check Current Deployment
```bash
# Production
ssh <EMAIL> "cat /home/<USER>/active_color.txt"

# Development
ssh <EMAIL> "cat /home/<USER>/active_color.txt"
```

### Manual Rollback
```bash
# Using the deployment script
./scripts/pm2-deploy.sh production switch blue  # if green is active

# Or manually
ssh <EMAIL> "echo 'blue' > /home/<USER>/active_color.txt"
curl -X PUT http://localhost:2019/config/apps/http/servers/srv0/routes/0 \
  -H "Content-Type: application/json" \
  -d '{"@id": "liquidlaunch.app", "handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "localhost:7500"}]}]}'
```

### View Application Logs
```bash
# PM2 logs
pm2 logs liquidlaunch-blue
pm2 logs liquidlaunch-green

# Or using the script
./scripts/pm2-deploy.sh production logs blue

# View all logs
pm2 logs

# Monitor in real-time
pm2 monit

### Cluster Management

```bash
# Safe Caddy configuration (recommended for multi-site setups)
./scripts/caddy-safe-config.sh production blue 2 apply

# Validate cluster health
./scripts/caddy-safe-config.sh production blue 2 validate

# Apply configuration and validate
./scripts/caddy-safe-config.sh production blue 2 both

# Show current configuration
./scripts/caddy-safe-config.sh production blue 2 show

# Scale cluster and update load balancer
./scripts/pm2-deploy.sh production scale blue 4
```

### Caddy Configuration Scripts

**Two scripts are provided:**

1. **`caddy-safe-config.sh`** (Recommended):
   - Works with existing multi-site Caddy configurations
   - Finds routes by domain name and updates only the specific upstreams
   - Safe for production environments with multiple virtual hosts
   - Requires `jq` to be installed

2. **`caddy-cluster-config.sh`** (Legacy):
   - Assumes simple Caddy configuration structure
   - May interfere with other virtual hosts
   - Use only if you have a dedicated Caddy instance
```

## Monitoring

- **Application Health**: `/api/health` endpoint
- **Process Status**: `pm2 status`
- **Server Logs**: `pm2 logs`
- **Caddy Status**: `curl http://localhost:2019/config/`

## Troubleshooting

### Deployment Fails
1. Check GitHub Actions logs
2. Verify SSH connectivity
3. Check server disk space and permissions
4. Verify Caddy Admin API is accessible

### SSH Connection Issues

**Host Key Verification Failed**:
```bash
# Test SSH connection manually
ssh <EMAIL>

# Get server's SSH host key
ssh-keyscan ssh.web.seipex.fi

# Add to GitHub secrets as SSH_KNOWN_HOSTS (optional)
```

**Permission Denied**:
1. Verify SSH key is correct in GitHub secrets
2. Check that the key has proper permissions on the server
3. Ensure the user exists and has proper access

**Connection Timeout**:
1. Verify server is accessible from GitHub Actions IPs
2. Check firewall settings
3. Verify SSH service is running on the server

**Bun Command Not Found**:
The ecosystem files use full paths to bun to avoid PATH issues:
```bash
# Check bun installation
which bun
ls -la ~/.bun/bin/bun

# If bun is in a different location, update ecosystem files
# Production: /home/<USER>/.bun/bin/bun
# Development: /home/<USER>/.bun/bin/bun
```

### Application Won't Start
1. Check PM2 logs: `pm2 logs`
2. Verify port availability: `netstat -tlnp | grep :7[56][0-9][0-9]`
3. Check application dependencies

### Traffic Not Switching
1. Verify Caddy Admin API response
2. Check Caddy configuration: `curl http://localhost:2019/config/`
3. Test endpoints directly: `curl http://localhost:7500/api/health` (prod) or `curl http://localhost:7600/api/health` (dev)

## Security Considerations

- SSH keys should be properly secured and rotated regularly
- Caddy Admin API should only be accessible locally
- Environment variables should be properly configured
- Regular security updates should be applied to the server
