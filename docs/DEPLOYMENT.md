# Deployment Guide

This document outlines the deployment setup for LiquidLaunch using GitHub Actions with blue-green deployments and Caddy for load balancing.

## Overview

The deployment system uses:
- **Blue-Green Deployments**: Zero-downtime deployments with two identical environments
- **Caddy Web Server**: Reverse proxy with API-based configuration for traffic management
- **PM2**: Process manager for Node.js applications
- **GitHub Actions**: CI/CD pipeline automation

## Infrastructure Requirements

### Server Setup

#### Development Server
- **Domain**: `dev.liquidlaunch.app`
- **User**: `liquidlaunch-dev`
- **Ports**: 3001 (blue), 3002 (green)

#### Production Server
- **Domain**: `liquidlaunch.app`
- **User**: `liquidlaunch`
- **Ports**: 3001 (blue), 3002 (green)

### Required Software

1. **Bun** - JavaScript runtime and package manager
2. **PM2** - Process manager
3. **Caddy** - Web server with Admin API enabled
4. **SSH** - For deployment access

## GitHub Secrets Configuration

Add the following secrets to your GitHub repository:

### Development Environment
- `DEV_HOST`: Development server hostname/IP
- `DEV_SSH_KEY`: SSH private key for development server access

### Production Environment
- `PROD_HOST`: Production server hostname/IP
- `PROD_SSH_KEY`: SSH private key for production server access

## Caddy Configuration

### Basic Caddyfile
```caddy
{
    admin localhost:2019
}

dev.liquidlaunch.app {
    reverse_proxy localhost:3001
}

liquidlaunch.app {
    reverse_proxy localhost:3001
}
```

### Enable Admin API
The Caddy Admin API must be enabled on port 2019 for the deployment scripts to work. This is configured in the global options block.

## Server Directory Structure

```
/home/<USER>/
├── app/
│   ├── blue/                    # Blue deployment
│   │   ├── .next/              # Built Next.js application
│   │   ├── public/             # Static assets
│   │   ├── package.json        # Dependencies
│   │   └── ecosystem.config.js # PM2 configuration
│   └── green/                  # Green deployment
│       ├── .next/
│       ├── public/
│       ├── package.json
│       └── ecosystem.config.js
├── logs/                       # PM2 log files
│   ├── blue-error.log
│   ├── blue-out.log
│   ├── green-error.log
│   └── green-out.log
└── active_color.txt           # Tracks which deployment is active
```

## PM2 Ecosystem Configuration

The project includes PM2 ecosystem files for better process management:

- `ecosystem.config.js` - Production configuration
- `ecosystem.dev.config.js` - Development configuration
- `scripts/pm2-deploy.sh` - Deployment management script

### Ecosystem Features

- **Environment-specific configurations**
- **Automatic log rotation**
- **Memory limits and restart policies**
- **Health checks and graceful shutdowns**
- **Process monitoring and clustering**

## Deployment Process

### Development Deployment
- **Trigger**: Push to `dev` branch
- **Target**: `dev.liquidlaunch.app`
- **Process**: Simple blue-green switch

### Production Deployment
- **Trigger**: Push to `release` branch
- **Target**: `liquidlaunch.app`
- **Process**: Gradual traffic shifting (10% → 50% → 100%)

## Deployment Steps

1. **Build & Test**: Application is built and tested
2. **Deploy**: Code is deployed to inactive environment
3. **Health Check**: Application health is verified
4. **Traffic Shift**: Traffic is gradually moved to new deployment
5. **Verification**: Final health checks ensure deployment success
6. **Rollback**: Automatic rollback on failure (production only)

## Health Check Endpoint

The deployment system uses `/api/health` for monitoring:

```json
{
  "status": "ok",
  "environment": "production",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "0.1.0",
  "uptime": 3600,
  "memory": {
    "used": 128,
    "total": 256
  },
  "deploymentColor": "blue"
}
```

## Manual Operations

### Using PM2 Ecosystem Files

```bash
# Start production applications
pm2 start ecosystem.config.js

# Start only blue deployment
pm2 start ecosystem.config.js --only liquidlaunch-blue

# Start development applications
pm2 start ecosystem.dev.config.js

# Restart with zero downtime
pm2 reload ecosystem.config.js

# Stop all applications
pm2 stop all

# Delete all applications
pm2 delete all
```

### Using the Deployment Script

```bash
# Start production blue deployment
./scripts/pm2-deploy.sh production start blue

# Switch to green deployment (zero-downtime)
./scripts/pm2-deploy.sh production switch green

# Check health of blue deployment
./scripts/pm2-deploy.sh production health blue

# View logs for green deployment
./scripts/pm2-deploy.sh production logs green

# Restart development blue
./scripts/pm2-deploy.sh development restart blue
```

### Check Current Deployment
```bash
ssh user@server "cat /home/<USER>/active_color.txt"
```

### Manual Rollback
```bash
# Using the deployment script
./scripts/pm2-deploy.sh production switch blue  # if green is active

# Or manually
ssh user@server "echo 'blue' > /home/<USER>/active_color.txt"
curl -X PUT http://localhost:2019/config/apps/http/servers/srv0/routes/0 \
  -H "Content-Type: application/json" \
  -d '{"@id": "liquidlaunch.app", "handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "localhost:3001"}]}]}'
```

### View Application Logs
```bash
# PM2 logs
pm2 logs liquidlaunch-blue
pm2 logs liquidlaunch-green

# Or using the script
./scripts/pm2-deploy.sh production logs blue

# View all logs
pm2 logs

# Monitor in real-time
pm2 monit
```

## Monitoring

- **Application Health**: `/api/health` endpoint
- **Process Status**: `pm2 status`
- **Server Logs**: `pm2 logs`
- **Caddy Status**: `curl http://localhost:2019/config/`

## Troubleshooting

### Deployment Fails
1. Check GitHub Actions logs
2. Verify SSH connectivity
3. Check server disk space and permissions
4. Verify Caddy Admin API is accessible

### Application Won't Start
1. Check PM2 logs: `pm2 logs`
2. Verify port availability: `netstat -tlnp | grep :300[12]`
3. Check application dependencies

### Traffic Not Switching
1. Verify Caddy Admin API response
2. Check Caddy configuration: `curl http://localhost:2019/config/`
3. Test endpoints directly: `curl http://localhost:3001/api/health`

## Security Considerations

- SSH keys should be properly secured and rotated regularly
- Caddy Admin API should only be accessible locally
- Environment variables should be properly configured
- Regular security updates should be applied to the server
