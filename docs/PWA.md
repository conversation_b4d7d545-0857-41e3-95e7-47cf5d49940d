# Progressive Web App (PWA) Setup

LiquidLaunch is configured as a Progressive Web App (PWA) following the [official Next.js PWA documentation](https://nextjs.org/docs/app/guides/progressive-web-apps), providing users with an app-like experience that can be installed on their devices.

## Features

### 🚀 **App Installation**
- Install LiquidLaunch directly from your browser
- Works on desktop and mobile devices
- Appears in app drawer/start menu like native apps
- Automatic install prompts when criteria are met

### 📱 **Mobile App Experience**
- Standalone display mode (no browser UI)
- Custom splash screen
- Theme color integration
- App shortcuts for quick actions

### ⚡ **Performance & Caching**
- Service Worker for offline functionality
- Automatic caching of critical assets
- Network-first strategy for API calls
- Cache-first strategy for static assets
- Fast loading even on slow connections

### 🎨 **Native Integration**
- Custom app icons for all platforms
- iOS Safari integration
- Android Chrome install banners
- Desktop app installation support

## Technical Implementation

### Core Components

1. **Web App Manifest** (`public/manifest.json`)
   - Defines app metadata and installation behavior
   - Specifies icons, colors, and display modes
   - Includes app shortcuts and categories

2. **Service Worker** (`public/sw.js`)
   - Handles caching strategies with cache-first for static assets
   - Network-first for API routes for real-time data
   - Enables offline functionality with fallback to `/offline` page
   - Push notification support
   - Background sync capabilities

3. **Service Worker Registration** (`src/components/pwa/ServiceWorkerRegistration.tsx`)
   - Registers the service worker on app load
   - Handles service worker updates with user notifications
   - Provides update prompts when new versions are available

4. **PWA Install Prompt** (`src/components/pwa/PWAInstallPrompt.tsx`)
   - Custom install prompt component
   - Detects installation eligibility
   - Provides user-friendly install experience

5. **Offline Page** (`src/app/offline/page.tsx`)
   - Fallback page for offline users
   - Helpful messaging about available features
   - Retry functionality

### Configuration

The PWA is configured using native Next.js features in `next.config.ts`:

```typescript
const nextConfig = {
  // ... other config
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript; charset=utf-8',
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self'",
          },
        ],
      },
    ];
  },
};
```

### Security Headers

Following Next.js PWA best practices, we implement:

- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-Frame-Options**: Protects against clickjacking attacks
- **Referrer-Policy**: Controls referrer information sharing
- **Content-Security-Policy**: Strict CSP for service worker
- **Cache-Control**: Prevents service worker caching for updates

## Icon Generation

### Automatic Generation

Use the provided script to generate all required PWA icons:

```bash
# Make the script executable
chmod +x scripts/generate-pwa-icons.sh

# Run the script (requires ImageMagick)
./scripts/generate-pwa-icons.sh
```

### Manual Generation

If you prefer to generate icons manually or use online tools:

**Required Sizes:**
- 72x72px
- 96x96px  
- 128x128px
- 144x144px
- 152x152px
- 192x192px
- 384x384px
- 512x512px

**Recommended Tools:**
- [PWA Asset Generator](https://www.pwabuilder.com/imageGenerator)
- [Favicon.io](https://favicon.io/favicon-converter/)
- [RealFaviconGenerator](https://realfavicongenerator.net/)

### Icon Guidelines

- Use PNG format with transparency
- Ensure icons are square (1:1 aspect ratio)
- Design for both light and dark backgrounds
- Include sufficient padding for maskable icons
- Test icons at different sizes for clarity

## Caching Strategy

### Cache-First Strategy (Static Assets)
- HTML pages (except API routes)
- CSS, JS, and image files
- Icons and manifest files
- Fast loading from cache with network fallback

### Network-First Strategy (Dynamic Content)
- API routes (`/api/*`)
- Real-time data that needs to be fresh
- Falls back to cache if network fails

### Offline Fallback
- Navigation requests fall back to `/offline` page
- API requests fall back to cached responses
- Graceful degradation of functionality

## Testing PWA Features

### Local Testing

1. **Build the production app:**
   ```bash
   bun run build
   bun run start
   ```

2. **Test with HTTPS locally:**
   ```bash
   # For testing PWA features locally
   bun run dev --experimental-https
   ```

3. **Test in Chrome DevTools:**
   - Open DevTools > Application tab
   - Check "Manifest" section for errors
   - Test "Service Workers" functionality
   - Use "Lighthouse" for PWA audit

4. **Install Locally:**
   - Look for install icon in browser address bar
   - Test custom install prompt
   - Verify standalone mode works

### Mobile Testing

1. **Android Chrome:**
   - Visit site in Chrome
   - Look for "Add to Home Screen" prompt
   - Test installed app experience

2. **iOS Safari:**
   - Visit site in Safari  
   - Use "Share" > "Add to Home Screen"
   - Test web app capabilities

### Production Testing

1. **PWA Requirements:**
   - ✅ Served over HTTPS
   - ✅ Has a web app manifest
   - ✅ Has a service worker
   - ✅ Has at least 192x192px icon

2. **Lighthouse PWA Audit:**
   - Run Lighthouse audit
   - Aim for 90+ PWA score
   - Address any flagged issues

## Service Worker Features

### Caching
- Automatic caching of critical app shell
- Intelligent cache invalidation on updates
- Separate caching strategies for different content types

### Push Notifications
- Support for push notifications
- Notification actions (Open, Close)
- Custom notification icons and badges
- Click handling to focus/open app

### Background Sync
- Background sync event handling
- Periodic sync capabilities (where supported)
- Offline action queuing

### Updates
- Automatic detection of service worker updates
- User-friendly update notifications
- Seamless app refresh after updates

## Best Practices

### Performance
- Keep app shell minimal
- Cache critical resources
- Use appropriate caching strategies
- Optimize images and assets
- Implement proper cache invalidation

### User Experience  
- Provide clear install prompts
- Handle offline states gracefully
- Show loading states appropriately
- Respect user install preferences
- Implement update notifications

### Accessibility
- Ensure keyboard navigation works
- Provide proper ARIA labels
- Test with screen readers
- Maintain high contrast ratios

## Troubleshooting

### Common Issues

1. **Install prompt not showing:**
   - Check HTTPS requirement
   - Verify manifest is valid
   - Ensure service worker registers
   - Clear browser cache

2. **Service worker not updating:**
   - Hard refresh the page (Ctrl+Shift+R)
   - Clear application data in DevTools
   - Check service worker console for errors
   - Verify cache-control headers are correct

3. **Icons not displaying:**
   - Verify file paths in manifest
   - Check icon file formats and sizes
   - Ensure proper aspect ratios
   - Test icon visibility in different contexts

4. **Offline functionality not working:**
   - Check service worker registration
   - Verify caching strategy implementation
   - Test network disconnection scenarios
   - Check offline page routing

### Debug Tools

- Chrome DevTools > Application tab
- Service Worker console logs
- Network tab for cache behavior
- Lighthouse PWA audit
- `chrome://webapps-internals/` (Chrome)

## Deployment Considerations

### Production Checklist

- [ ] HTTPS enabled
- [ ] All PWA icons generated and optimized
- [ ] Manifest validates without errors
- [ ] Service worker registers successfully
- [ ] Offline page works correctly
- [ ] Install prompt functions properly
- [ ] Lighthouse PWA score > 90
- [ ] Security headers configured
- [ ] Cache strategies tested
- [ ] Update mechanism tested

### CDN Configuration

Ensure your CDN properly serves:
- `manifest.json` with correct MIME type (`application/manifest+json`)
- Service worker files without caching
- PWA icons with appropriate headers
- Security headers for all routes

### Performance Monitoring

Track PWA metrics:
- Service worker registration success rate
- Cache hit/miss ratios
- Install prompt acceptance rate
- Offline usage patterns
- Update adoption rates

## Updates and Maintenance

### Service Worker Updates

The PWA automatically detects and handles updates:

1. **Detection**: Service worker checks for updates on navigation
2. **Notification**: Users see update notification via toast
3. **Installation**: Users can refresh to get the latest version
4. **Fallback**: Graceful handling if update fails

### Manifest Updates

When updating the manifest:
- Test changes in multiple browsers
- Verify icon paths and sizes
- Check installability after changes
- Update app store listings if applicable

### Cache Management

- Monitor cache size and performance
- Implement cache cleanup strategies
- Version cache names for updates
- Handle cache storage quota limits

## Future Enhancements

Potential PWA features to consider:

1. **Web Share API**: Share content from the app
2. **File System Access**: Direct file operations
3. **Web Bluetooth**: Hardware integration
4. **Background Fetch**: Large file downloads
5. **Payment Request**: Native payment flows
6. **Contact Picker**: Access device contacts
7. **Badging API**: App icon badges

## References

- [Next.js PWA Documentation](https://nextjs.org/docs/app/guides/progressive-web-apps)
- [MDN PWA Guide](https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps)
- [Web App Manifest Specification](https://www.w3.org/TR/appmanifest/)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [What PWA Can Do Today](https://whatpwacando.today/) 