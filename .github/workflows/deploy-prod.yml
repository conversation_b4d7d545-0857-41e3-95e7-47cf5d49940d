name: Deploy to Production

on:
  push:
    branches:
      - release
  workflow_dispatch:

env:
  APP_NAME: liquidlaunch
  DEPLOY_USER: liquidlaunch-web-prod
  DEPLOY_HOST: ssh.web.seipex.fi
  DEPLOY_PATH: /home/<USER>/app
  BLUE_PORT: 7500
  GREEN_PORT: 7510
  CADDY_API: "http://localhost:2019"

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run tests
        run: bun test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build project
        run: bun run build

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Check active deployment
        id: check-active
        run: |
          ACTIVE_COLOR=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cat /home/<USER>/active_color.txt || echo 'blue'")
          echo "ACTIVE_COLOR=$ACTIVE_COLOR" >> $GITHUB_ENV
          if [ "$ACTIVE_COLOR" = "blue" ]; then
            echo "DEPLOY_COLOR=green" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$GREEN_PORT" >> $GITHUB_ENV
          else
            echo "DEPLOY_COLOR=blue" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$BLUE_PORT" >> $GITHUB_ENV
          fi

      - name: Create deployment directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR"

      - name: Deploy application
        run: |
          tar -czf deploy.tar.gz .next public package.json bun.lockb ecosystem.config.js scripts/
          scp deploy.tar.gz $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/$DEPLOY_COLOR/
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && tar -xzf deploy.tar.gz && rm deploy.tar.gz"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && bun install --production"
          ssh $DEPLOY_USER@$DEPLOY_HOST "chmod +x $DEPLOY_PATH/$DEPLOY_COLOR/scripts/*.sh"

      - name: Create health check endpoint
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo 'export async function GET() { return new Response(JSON.stringify({ status: \"ok\", environment: \"production\", color: \"$DEPLOY_COLOR\", timestamp: new Date().toISOString() }), { status: 200, headers: { \"Content-Type\": \"application/json\" } }); }' > $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health/route.ts"

      - name: Create logs directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p /home/<USER>/logs"

      - name: Start application with PM2 ecosystem
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && pm2 delete liquidlaunch-$DEPLOY_COLOR || true"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && pm2 start ecosystem.config.js --only liquidlaunch-$DEPLOY_COLOR"

      - name: Wait for cluster to start and validate health
        run: |
          # Wait for cluster instances to start
          sleep 15

          # Validate cluster health using our script
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && ./scripts/caddy-cluster-config.sh production $DEPLOY_COLOR 2 validate"

      - name: Run comprehensive smoke tests
        run: |
          # Test multiple critical endpoints
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/ | grep -q 'LiquidLaunch'"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/manifest.json | grep -q 'LiquidLaunch'"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/api/tokens/specific-balances?wallet=******************************************&tokens=******************************************"

          # Test PWA assets
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/manifest.json"

          # Add more critical endpoint tests as needed

      - name: Switch traffic to new cluster deployment
        run: |
          # Apply cluster configuration for the new deployment
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && ./scripts/caddy-cluster-config.sh production $DEPLOY_COLOR 2 both"

          # Wait for traffic to stabilize
          sleep 10

      - name: Switch active deployment
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo '$DEPLOY_COLOR' > /home/<USER>/active_color.txt"

      - name: Final health check
        run: |
          sleep 5
          curl -sf https://liquidlaunch.app/api/health || exit 1

      - name: Notify on success
        if: success()
        run: |
          echo "Successfully deployed to production environment ($DEPLOY_COLOR)"
          # Add notification logic here (Slack, Discord, etc.)

  rollback:
    needs: deploy
    if: failure()
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Rollback to previous deployment
        run: |
          ACTIVE_COLOR=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cat /home/<USER>/active_color.txt")
          ROLLBACK_COLOR=$([ "$ACTIVE_COLOR" = "blue" ] && echo "green" || echo "blue")
          ROLLBACK_PORT=$([ "$ROLLBACK_COLOR" = "blue" ] && echo "$BLUE_PORT" || echo "$GREEN_PORT")

          # Check if rollback deployment exists and is running
          ROLLBACK_RUNNING=$(ssh $DEPLOY_USER@$DEPLOY_HOST "pm2 list | grep liquidlaunch-$ROLLBACK_COLOR | grep -c online || echo 0")

          if [ "$ROLLBACK_RUNNING" -eq "0" ]; then
            echo "No previous deployment available for rollback. Manual intervention required."
            exit 1
          fi

          # Update Caddy to point to the rollback deployment
          ssh $DEPLOY_USER@$DEPLOY_HOST "cat > /tmp/caddy-rollback.json << EOF
          {
            \"@id\": \"liquidlaunch.app\",
            \"handle\": [
              {
                \"handler\": \"reverse_proxy\",
                \"upstreams\": [
                  {
                    \"dial\": \"localhost:$ROLLBACK_PORT\"
                  }
                ]
              }
            ]
          }
          EOF"

          # Apply rollback config
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -X PUT $CADDY_API/config/apps/http/servers/srv0/routes/0 -H \"Content-Type: application/json\" -d @/tmp/caddy-rollback.json"

          # Update active color
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo '$ROLLBACK_COLOR' > /home/<USER>/active_color.txt"

          echo "Rolled back to $ROLLBACK_COLOR deployment"
