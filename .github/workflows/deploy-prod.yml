name: Deploy to Production

on:
  push:
    branches:
      - release
  workflow_dispatch:

env:
  APP_NAME: liquidlaunch
  DEPLOY_USER: liquidlaunch-web-prod
  DEPLOY_HOST: ssh.web.seipex.fi
  DEPLOY_PATH: /home/<USER>/app
  BLUE_PORT: 7500
  GREEN_PORT: 7520
  CADDY_API: "http://localhost:2019"

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run tests
        run: bun test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build project
        env:
          NEXT_PUBLIC_APP_URL: https://liquidlaunch.app
          NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: c1ee6f914b92bb42cb19838831f31200
          NEXT_PUBLIC_BLOCK_EXPLORER_URL: ${{ secrets.BLOCK_EXPLORER_URL }}
          NEXT_PUBLIC_RPC_URL: https://rpc.hyperliquid.xyz/evm
          NEXT_PUBLIC_PRIVY_APP_ID: cmbu1z7fs004rl80nufssjauw
          NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS: "******************************************"
          NEXT_PUBLIC_CHAT_WEBSOCKET_URL: wss://api-dev.liquidlaunch.app
          NEXT_PUBLIC_API_CHAT_SERVER_URL: https://api-dev.liquidlaunch.app
          EXTERNAL_SIGNATURE_API_URL: https://ace.liquidlaunch.app/api/swap/sign
          API_LIQUID_LAUNCH_URL=https://api.liquidlaunch.app
          LIQD_AG_API_URL: https://api.liqd.ag
          QUOTE_API_BASE_URL: "http://**************:10000"
          DEPLOYMENT_COLOR: blue
          NEXTAUTH_URL: https://liquidlaunch.app
          NEXTAUTH_SECRET: ${{ secrets.PROD_NEXTAUTH_SECRET }}
        run: bun run build

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Add SSH host key
        run: |
          mkdir -p ~/.ssh
          # Add the server's SSH host key to known_hosts
          ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || {
            echo "Failed to get host key via ssh-keyscan, trying direct connection..."
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $DEPLOY_USER@$DEPLOY_HOST "echo 'Host key added'" || true
            ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || true
          }
          chmod 644 ~/.ssh/known_hosts

      - name: Check active deployment
        id: check-active
        run: |
          ACTIVE_COLOR=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cat /home/<USER>/active_color.txt || echo 'blue'")
          echo "ACTIVE_COLOR=$ACTIVE_COLOR" >> $GITHUB_ENV
          if [ "$ACTIVE_COLOR" = "blue" ]; then
            echo "DEPLOY_COLOR=green" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$GREEN_PORT" >> $GITHUB_ENV
          else
            echo "DEPLOY_COLOR=blue" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$BLUE_PORT" >> $GITHUB_ENV
          fi

      - name: Create deployment directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR"

      - name: Deploy application
        run: |
          tar -czf deploy.tar.gz .next public package.json ecosystem.config.js scripts/
          scp deploy.tar.gz $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/$DEPLOY_COLOR/
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && tar -xzf deploy.tar.gz && rm deploy.tar.gz"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && /home/<USER>/.bun/bin/bun install --production"
          ssh $DEPLOY_USER@$DEPLOY_HOST "chmod +x $DEPLOY_PATH/$DEPLOY_COLOR/scripts/*.sh"

      - name: Update environment variables
        run: |
          # Create/update environment file with secrets
          ssh $DEPLOY_USER@$DEPLOY_HOST "cat > /home/<USER>/.env.production << 'EOF'
          NEXTAUTH_SECRET=${{ secrets.PROD_NEXTAUTH_SECRET }}
          NEXTAUTH_URL=https://liquidlaunch.app
          NEXT_PUBLIC_APP_URL=https://liquidlaunch.app
          NEXT_PUBLIC_CHAT_WEBSOCKET_URL: wss://api-dev.liquidlaunch.app
          NEXT_PUBLIC_API_CHAT_SERVER_URL: https://api-dev.liquidlaunch.app
          NEXT_PUBLIC_BLOCK_EXPLORER_URL=${{ secrets.BLOCK_EXPLORER_URL }}
          NEXT_PUBLIC_RPC_URL=https://rpc.hyperliquid.xyz/evm
          NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=c1ee6f914b92bb42cb19838831f31200
          NEXT_PUBLIC_PRIVY_APP_ID=cmbu1z7fs004rl80nufssjauw
          NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS=******************************************
          EXTERNAL_SIGNATURE_API_URL=https://ace.liquidlaunch.app/api/swap/sign
          API_LIQUID_LAUNCH_URL=https://api.liquidlaunch.app
          LIQD_AG_API_URL=https://api.liqd.ag
          QUOTE_API_BASE_URL=http://**************:10000
          DEPLOYMENT_COLOR=\$DEPLOY_COLOR
          LOG_LEVEL=info
          EOF"
          ssh $DEPLOY_USER@$DEPLOY_HOST "chmod 600 /home/<USER>/.env.production"

      - name: Create health check endpoint
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo 'export async function GET() { return new Response(JSON.stringify({ status: \"ok\", environment: \"production\", color: \"$DEPLOY_COLOR\", timestamp: new Date().toISOString() }), { status: 200, headers: { \"Content-Type\": \"application/json\" } }); }' > $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health/route.ts"

      - name: Create logs directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p /home/<USER>/logs"

      - name: Start application with PM2 ecosystem
        run: |
          PM2_PATH=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && ./scripts/find-pm2.sh")
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && $PM2_PATH delete liquidlaunch-$DEPLOY_COLOR || true"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && $PM2_PATH start ecosystem.config.js --only liquidlaunch-$DEPLOY_COLOR"

      - name: Wait for cluster to start and validate health
        run: |
          # Wait for cluster instances to start
          sleep 15

          # Validate cluster health using our script
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && PATH=/home/<USER>/.nvm/versions/node/v22.16.0/bin:\$PATH ./scripts/caddy-safe-config.sh production $DEPLOY_COLOR 2 validate"

      - name: Run comprehensive smoke tests
        run: |
          # Test multiple critical endpoints
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/ | grep -q 'LiquidLaunch'"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/manifest.json | grep -q 'LiquidLaunch'"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/api/tokens/specific-balances?wallet=******************************************&tokens=******************************************"

          # Test PWA assets
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/manifest.json"

          # Add more critical endpoint tests as needed

      - name: Switch traffic to new cluster deployment
        run: |
          # Apply safe cluster configuration for the new deployment
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && PATH=/home/<USER>/.nvm/versions/node/v22.16.0/bin:\$PATH ./scripts/caddy-safe-config.sh production $DEPLOY_COLOR 2 both"

          # Wait for traffic to stabilize
          sleep 10

      - name: Switch active deployment
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo '$DEPLOY_COLOR' > /home/<USER>/active_color.txt"

      - name: Final health check
        run: |
          sleep 5
          curl -sf https://liquidlaunch.app/api/health || exit 1

      - name: Notify on success
        if: success()
        run: |
          echo "Successfully deployed to production environment ($DEPLOY_COLOR)"
          # Add notification logic here (Slack, Discord, etc.)

  rollback:
    needs: deploy
    if: failure()
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Add SSH host key
        run: |
          mkdir -p ~/.ssh
          # Add the server's SSH host key to known_hosts
          ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || {
            echo "Failed to get host key via ssh-keyscan, trying direct connection..."
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $DEPLOY_USER@$DEPLOY_HOST "echo 'Host key added'" || true
            ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || true
          }
          chmod 644 ~/.ssh/known_hosts

      - name: Rollback to previous deployment
        run: |
          ACTIVE_COLOR=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cat /home/<USER>/active_color.txt")
          ROLLBACK_COLOR=$([ "$ACTIVE_COLOR" = "blue" ] && echo "green" || echo "blue")
          ROLLBACK_PORT=$([ "$ROLLBACK_COLOR" = "blue" ] && echo "$BLUE_PORT" || echo "$GREEN_PORT")

          # Check if rollback deployment exists and is running
          PM2_PATH=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH && ./scripts/find-pm2.sh")
          ROLLBACK_RUNNING=$(ssh $DEPLOY_USER@$DEPLOY_HOST "$PM2_PATH list | grep liquidlaunch-$ROLLBACK_COLOR | grep -c online || echo 0")

          if [ "$ROLLBACK_RUNNING" -eq "0" ]; then
            echo "No previous deployment available for rollback. Manual intervention required."
            exit 1
          fi

          # Update Caddy to point to the rollback deployment
          ssh $DEPLOY_USER@$DEPLOY_HOST "cat > /tmp/caddy-rollback.json << EOF
          {
            \"@id\": \"liquidlaunch.app\",
            \"handle\": [
              {
                \"handler\": \"reverse_proxy\",
                \"upstreams\": [
                  {
                    \"dial\": \"localhost:$ROLLBACK_PORT\"
                  }
                ]
              }
            ]
          }
          EOF"

          # Apply rollback config
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -X PUT $CADDY_API/config/apps/http/servers/srv0/routes/0 -H \"Content-Type: application/json\" -d @/tmp/caddy-rollback.json"

          # Update active color
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo '$ROLLBACK_COLOR' > /home/<USER>/active_color.txt"

          echo "Rolled back to $ROLLBACK_COLOR deployment"
