name: Deploy to Development

on:
  push:
    branches:
      - dev
  workflow_dispatch:

env:
  APP_NAME: liquidlaunch
  DEPLOY_USER: liquidlaunch-web-dev
  DEPLOY_HOST: ssh.web.seipex.fi
  DEPLOY_PATH: /home/<USER>/app
  BLUE_PORT: 7600
  GREEN_PORT: 7610
  CADDY_API: "http://localhost:2019"

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run tests
        run: bun test

      - name: Build project
        env:
          NEXT_PUBLIC_APP_URL: https://alpha.liquidlaunch.app
          NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: c1ee6f914b92bb42cb19838831f31200
          NEXT_PUBLIC_BLOCK_EXPLORER_URL: ${{ secrets.BLOCK_EXPLORER_URL }}
          NEXT_PUBLIC_RPC_URL: https://rpc.hyperliquid.xyz/evm
          NEXT_PUBLIC_PRIVY_APP_ID: cmbu1z7fs004rl80nufssjauw
          NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS: "******************************************"
          NEXT_PUBLIC_CHAT_WEBSOCKET_URL: wss://api-dev.liquidlaunch.app
          NEXT_PUBLIC_API_CHAT_SERVER_URL: https://api-dev.liquidlaunch.app
          EXTERNAL_SIGNATURE_API_URL: https://ace.liquidlaunch.app/api/swap/sign
          API_LIQUID_LAUNCH_URL=https://api.liquidlaunch.app
          LIQD_AG_API_URL: https://api.liqd.ag
          QUOTE_API_BASE_URL: "http://**************:10000"
          DEPLOYMENT_COLOR: blue
          NEXTAUTH_URL: https://alpha.liquidlaunch.app
          NEXTAUTH_SECRET: ${{ secrets.DEV_NEXTAUTH_SECRET }}
        run: bun run build

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEV_SSH_KEY }}

      - name: Add SSH host key
        run: |
          mkdir -p ~/.ssh
          # Add the server's SSH host key to known_hosts
          ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || {
            echo "Failed to get host key via ssh-keyscan, trying direct connection..."
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $DEPLOY_USER@$DEPLOY_HOST "echo 'Host key added'" || true
            ssh-keyscan -H ssh.web.seipex.fi >> ~/.ssh/known_hosts 2>/dev/null || true
          }
          chmod 644 ~/.ssh/known_hosts

      - name: Check active deployment
        id: check-active
        run: |
          ACTIVE_COLOR=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cat /home/<USER>/active_color.txt || echo 'blue'")
          echo "ACTIVE_COLOR=$ACTIVE_COLOR" >> $GITHUB_ENV
          if [ "$ACTIVE_COLOR" = "blue" ]; then
            echo "DEPLOY_COLOR=green" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$GREEN_PORT" >> $GITHUB_ENV
          else
            echo "DEPLOY_COLOR=blue" >> $GITHUB_ENV
            echo "DEPLOY_PORT=$BLUE_PORT" >> $GITHUB_ENV
          fi

      - name: Create deployment directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR"

      - name: Deploy application
        run: |
          tar -czf deploy.tar.gz .next public package.json ecosystem.dev.config.js scripts/
          scp deploy.tar.gz $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/$DEPLOY_COLOR/
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && tar -xzf deploy.tar.gz && rm deploy.tar.gz"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && /home/<USER>/.bun/bin/bun install --production"
          ssh $DEPLOY_USER@$DEPLOY_HOST "chmod +x $DEPLOY_PATH/$DEPLOY_COLOR/scripts/*.sh"

      - name: Update environment variables
        run: |
          # Create/update environment file with secrets
          ssh $DEPLOY_USER@$DEPLOY_HOST "cat > /home/<USER>/.env.development << 'EOF'
          NEXTAUTH_SECRET=${{ secrets.DEV_NEXTAUTH_SECRET }}
          NEXTAUTH_URL=https://alpha.liquidlaunch.app
          NEXT_PUBLIC_APP_URL=https://alpha.liquidlaunch.app
          NEXT_PUBLIC_CHAT_WEBSOCKET_URL: wss://api-dev.liquidlaunch.app
          NEXT_PUBLIC_API_CHAT_SERVER_URL: https://api-dev.liquidlaunch.app
          NEXT_PUBLIC_BLOCK_EXPLORER_URL=${{ secrets.BLOCK_EXPLORER_URL }}
          NEXT_PUBLIC_RPC_URL=https://rpc.hyperliquid.xyz/evm
          NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=c1ee6f914b92bb42cb19838831f31200
          NEXT_PUBLIC_PRIVY_APP_ID=cmbu1z7fs004rl80nufssjauw
          NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS=******************************************
          EXTERNAL_SIGNATURE_API_URL=https://ace.liquidlaunch.app/api/swap/sign
          API_LIQUID_LAUNCH_URL=https://api.liquidlaunch.app
          LIQD_AG_API_URL=https://api.liqd.ag
          QUOTE_API_BASE_URL=http://**************:10000
          DEPLOYMENT_COLOR=\$DEPLOY_COLOR
          LOG_LEVEL=debug
          EOF"
          ssh $DEPLOY_USER@$DEPLOY_HOST "chmod 600 /home/<USER>/.env.development"

      - name: Create health check endpoint
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo 'export async function GET() { return new Response(JSON.stringify({ status: \"ok\", environment: \"dev\", color: \"$DEPLOY_COLOR\", timestamp: new Date().toISOString() }), { status: 200, headers: { \"Content-Type\": \"application/json\" } }); }' > $DEPLOY_PATH/$DEPLOY_COLOR/src/app/api/health/route.ts"

      - name: Create logs directory
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "mkdir -p /home/<USER>/logs"

      - name: Start application with PM2 ecosystem
        run: |
          PM2_PATH=$(ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && ./scripts/find-pm2.sh")
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && $PM2_PATH delete liquidlaunch-dev-$DEPLOY_COLOR || true"
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && $PM2_PATH start ecosystem.dev.config.js --only liquidlaunch-dev-$DEPLOY_COLOR"

      - name: Wait for application to start
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "for i in {1..30}; do if curl -s http://localhost:$DEPLOY_PORT/api/health | grep -q 'ok'; then exit 0; fi; sleep 2; done; exit 1"

      - name: Run smoke tests
        run: |
          # Test multiple endpoints to ensure the app is working correctly
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/api/health"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/ | grep -q 'LiquidLaunch'"
          ssh $DEPLOY_USER@$DEPLOY_HOST "curl -sf http://localhost:$DEPLOY_PORT/manifest.json | grep -q 'LiquidLaunch'"

      - name: Update Caddy configuration for development cluster
        run: |
          # Apply safe cluster configuration for development
          ssh $DEPLOY_USER@$DEPLOY_HOST "cd $DEPLOY_PATH/$DEPLOY_COLOR && PATH=/home/<USER>/.nvm/versions/node/v22.16.0/bin:\$PATH ./scripts/caddy-safe-config.sh development $DEPLOY_COLOR 1 both"

      - name: Switch active deployment
        run: |
          ssh $DEPLOY_USER@$DEPLOY_HOST "echo '$DEPLOY_COLOR' > /home/<USER>/active_color.txt"

      - name: Final health check
        run: |
          sleep 5
          curl -sf https://alpha.liquidlaunch.app/api/health || exit 1

      - name: Notify on success
        if: success()
        run: |
          echo "Successfully deployed to dev environment ($DEPLOY_COLOR)"
          # Add notification logic here (Slack, Discord, etc.)
