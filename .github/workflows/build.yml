name: Build and Test Check

on:
  push:
    branches:
      - '**'  # Run on commits to any branch
  pull_request:  # Also run on pull requests

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run linting
        run: bun run lint

      - name: Run tests
        run: bun test

      - name: Build project
        run: bun run build

      - name: Check build artifacts
        run: |
          # Verify that essential build artifacts exist
          test -d .next
          test -f .next/BUILD_ID
          test -d .next/static
          echo "Build artifacts verified successfully"
