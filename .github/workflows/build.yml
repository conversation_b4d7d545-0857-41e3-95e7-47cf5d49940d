name: Build and Test Check

on:
  push:
    branches:
      - '**'  # Run on commits to any branch
  pull_request:  # Also run on pull requests

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run linting
        run: bun run lint

      - name: Run tests
        run: bun test

      - name: Build project
        env:
          NEXT_PUBLIC_APP_URL: http://localhost:3000
          NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: c1ee6f914b92bb42cb19838831f31200
          NEXT_PUBLIC_BLOCK_EXPLORER_URL: https://www.hyperscan.com
          NEXT_PUBLIC_RPC_URL: https://rpc.hyperliquid.xyz/evm
          NEXT_PUBLIC_PRIVY_APP_ID: cmbu1z7fs004rl80nufssjauw
          NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS: "******************************************"
          NEXT_PUBLIC_CHAT_WEBSOCKET_URL: wss://api-dev.liquidlaunch.app
          NEXT_PUBLIC_API_CHAT_SERVER_URL: https://api-dev.liquidlaunch.app
          EXTERNAL_SIGNATURE_API_URL: https://ace.liquidlaunch.app/api/swap/sign
          LIQD_AG_API_URL: https://api.liqd.ag
          QUOTE_API_BASE_URL: "http://**************:10000"
          DEPLOYMENT_COLOR: blue
          NEXTAUTH_URL: http://localhost:3000
          NEXTAUTH_SECRET: test-secret-for-build
        run: bun run build

      - name: Check build artifacts
        run: |
          # Verify that essential build artifacts exist
          test -d .next
          test -f .next/BUILD_ID
          test -d .next/static
          echo "Build artifacts verified successfully"
