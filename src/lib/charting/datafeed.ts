import { OhlcvDataPoint, GetOhlcvParams, TokenDetails } from "@/lib/api/types";
import { IDatafeedChartApi, TvBar, TvHistoryMetadata, TvSymbolInfo, DatafeedConfiguration } from "@/types/charting";
import { getOhlcvAction } from "@/app/token/[tokenAddress]/actions/ohlcv.actions";
import { getNativeHypePrice } from "@/services/dexscreenerService";

const SUPPORTED_RESOLUTIONS = ["1", "5", "15", "30", "60", "240", "1D", "1W"];
const HARDCODED_TOTAL_SUPPLY = 1_000_000_000;
const DECIMALS = 6;

// Standard data loading configuration
const INITIAL_LOAD_DAYS = 7; // Load 7 days of 1-minute data initially
const PAGINATION_DAYS = 7; // Load 7 more days when scrolling back
const CACHE_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

// Subscription interface for real-time updates
interface RealtimeSubscription {
  symbolInfo: TvSymbolInfo;
  resolution: string;
  onRealtimeCallback: (bar: TvBar) => void;
  subscriberUID: string;
  lastBarTime: number;
}

// Token data store - holds all 1-minute data for a token
interface TokenDataStore {
  bars: TvBar[]; // All 1-minute bars, sorted by time
  earliestTime: number; // Earliest timestamp we have (seconds)
  latestTime: number; // Latest timestamp we have (seconds)
  lastUpdated: number; // When this data was last updated
  isComplete: boolean; // Whether we've loaded all available historical data
}

// Pending request interface
interface PendingRequest {
  promise: Promise<TvBar[]>;
  startTime: number;
  endTime: number;
}

export class LiquidLaunchDatafeed implements IDatafeedChartApi {
  private tokens: Map<string, TokenDetails> = new Map();
  private _showMarketCap: boolean = true;
  private _hypePriceUsd: number | null = null;
  private _hypePricePromise: Promise<number> | null = null;

  // Real-time subscription management
  private _subscriptions: Map<string, RealtimeSubscription> = new Map();
  private _updateInterval: NodeJS.Timeout | null = null;
  private _isRealTimeEnabled: boolean = false;

  // Data storage - one store per token per mode (price/mcap)
  private _tokenDataStores: Map<string, TokenDataStore> = new Map();
  private _pendingRequests: Map<string, PendingRequest> = new Map();

  constructor() {}

  /**
   * Singleton pattern for development hot reloads
   */
  public static getInstance(): LiquidLaunchDatafeed {
    if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
      if (!window.__liquidLaunchDatafeed) {
        window.__liquidLaunchDatafeed = new LiquidLaunchDatafeed();
      }
      return window.__liquidLaunchDatafeed;
    }
    return new LiquidLaunchDatafeed();
  }

  /**
   * Add token information for symbol resolution
   */
  public addTokenInfo(tokenAddress: string, tokenInfo: TokenDetails): void {
    this.tokens.set(tokenAddress.toLowerCase(), tokenInfo);
  }

  /**
   * Update chart display configuration
   */
  public updateChartDataConfig(showMarketCap: boolean): void {
    const wasMarketCap = this._showMarketCap;
    this._showMarketCap = showMarketCap;

    // If mode changed, clear all data to refresh with new scaling
    if (wasMarketCap !== showMarketCap) {
      console.log(
        `[Datafeed] Switching from ${wasMarketCap ? "MCap" : "Price"} to ${showMarketCap ? "MCap" : "Price"} mode`,
      );
      this.clearPriceCache();
      this.clearDataCache();
    }
  }

  /**
   * Clear cached HYPE price
   */
  public clearPriceCache(): void {
    this._hypePriceUsd = null;
    this._hypePricePromise = null;
  }

  /**
   * Clear all cached data
   */
  public clearDataCache(): void {
    this._tokenDataStores.clear();
    this._pendingRequests.clear();
  }

  /**
   * Debug time alignment for a given timestamp and resolution
   */
  public debugTimeAlignment(timestamp: string | number, resolution: string): object {
    const timeMs = typeof timestamp === "string" ? new Date(timestamp).getTime() : timestamp;
    const intervalMs = this.getIntervalMs(resolution);

    if (!intervalMs) {
      return { error: `Unknown resolution: ${resolution}` };
    }

    const alignedTime = this.alignToInterval(timeMs, intervalMs);
    const nextIntervalTime = alignedTime + intervalMs;
    const previousIntervalTime = alignedTime - intervalMs;

    return {
      originalTime: timeMs,
      originalTimeFormatted: new Date(timeMs).toISOString(),
      resolution,
      intervalMs,
      intervalDescription: this.getIntervalDescription(resolution),
      alignedTime,
      alignedTimeFormatted: new Date(alignedTime).toISOString(),
      nextIntervalTime,
      nextIntervalTimeFormatted: new Date(nextIntervalTime).toISOString(),
      previousIntervalTime,
      previousIntervalTimeFormatted: new Date(previousIntervalTime).toISOString(),
      difference: timeMs - alignedTime,
      gapThreshold: intervalMs * 1.5,
      wouldTriggerGapFill: timeMs - alignedTime > intervalMs * 1.5,
    };
  }

  private getIntervalDescription(resolution: string): string {
    const intervalMs = this.getIntervalMs(resolution);
    if (!intervalMs) return "Unknown";

    const minutes = intervalMs / (60 * 1000);
    const hours = intervalMs / (60 * 60 * 1000);
    const days = intervalMs / (24 * 60 * 60 * 1000);

    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? "s" : ""}`;
    } else if (hours < 24) {
      return `${hours} hour${hours !== 1 ? "s" : ""}`;
    } else {
      return `${days} day${days !== 1 ? "s" : ""}`;
    }
  }

  /**
   * Debug method to inspect current state
   */
  public getDebugInfo(tokenAddress?: string): object {
    const info: Record<string, unknown> = {
      isRealTimeEnabled: this._isRealTimeEnabled,
      subscriptionsCount: this._subscriptions.size,
      subscriptions: Array.from(this._subscriptions.entries()).map(([id, sub]) => ({
        id,
        ticker: sub.symbolInfo.ticker,
        resolution: sub.resolution,
        lastBarTime: sub.lastBarTime,
        lastBarTimeFormatted: new Date(sub.lastBarTime).toISOString(),
      })),
      tokenStores: Array.from(this._tokenDataStores.entries()).map(([key, store]) => ({
        key,
        barsCount: store.bars.length,
        earliestTime: store.earliestTime,
        latestTime: store.latestTime,
        lastUpdated: store.lastUpdated,
        isComplete: store.isComplete,
        latestBars: store.bars.slice(-3).map((bar) => ({
          time: bar.time,
          timeFormatted: new Date(bar.time).toISOString(),
          open: bar.open,
          high: bar.high,
          low: bar.low,
          close: bar.close,
          volume: bar.volume,
        })),
      })),
    };

    if (tokenAddress) {
      const store = this.getTokenDataStore(tokenAddress);
      info.specificTokenStore = {
        barsCount: store.bars.length,
        earliestTime: store.earliestTime,
        latestTime: store.latestTime,
        lastUpdated: store.lastUpdated,
        isComplete: store.isComplete,
        recentBars: store.bars.slice(-10).map((bar) => ({
          time: bar.time,
          timeFormatted: new Date(bar.time).toISOString(),
          open: bar.open,
          high: bar.high,
          low: bar.low,
          close: bar.close,
          volume: bar.volume,
        })),
      };
    }

    return info;
  }

  /**
   * Get data store key (includes market cap mode)
   */
  private getDataStoreKey(tokenAddress: string): string {
    return `${tokenAddress.toLowerCase()}_${this._showMarketCap ? "mcap" : "price"}`;
  }

  /**
   * Get or create token data store
   */
  private getTokenDataStore(tokenAddress: string): TokenDataStore {
    const key = this.getDataStoreKey(tokenAddress);
    let store = this._tokenDataStores.get(key);

    if (!store) {
      store = {
        bars: [],
        earliestTime: 0,
        latestTime: 0,
        lastUpdated: 0,
        isComplete: false,
      };
      this._tokenDataStores.set(key, store);
    }

    return store;
  }

  /**
   * Load initial 7 days of 1-minute data for a token
   */
  private async loadInitialData(tokenAddress: string): Promise<void> {
    const store = this.getTokenDataStore(tokenAddress);

    // Check if we already have recent data
    if (store.bars.length > 0 && Date.now() - store.lastUpdated < CACHE_TIMEOUT_MS) {
      return;
    }

    // Check for pending request
    const pendingKey = `initial_${tokenAddress}`;
    const existingRequest = this._pendingRequests.get(pendingKey);
    if (existingRequest) {
      await existingRequest.promise;
      return;
    }

    const now = Math.floor(Date.now() / 1000);
    const startTime = now - INITIAL_LOAD_DAYS * 24 * 60 * 60;

    const requestPromise = this.fetchOhlcvData(tokenAddress, "1m", startTime, now);

    this._pendingRequests.set(pendingKey, {
      promise: requestPromise,
      startTime,
      endTime: now,
    });

    try {
      const bars = await requestPromise;

      if (bars.length > 0) {
        store.bars = bars.sort((a, b) => a.time - b.time);
        store.earliestTime = store.bars[0].time / 1000;
        store.latestTime = store.bars[store.bars.length - 1].time / 1000;
        store.lastUpdated = Date.now();
      } else {
        console.log(`[Datafeed] No initial data available for ${tokenAddress}`);
      }
    } finally {
      this._pendingRequests.delete(pendingKey);
    }
  }

  /**
   * Load more historical data when scrolling back
   */
  private async loadMoreHistoricalData(tokenAddress: string, requestedStartTime: number): Promise<void> {
    const store = this.getTokenDataStore(tokenAddress);

    // If we already have data covering this time or we've loaded everything, skip
    if (store.isComplete || (store.earliestTime > 0 && requestedStartTime >= store.earliestTime)) {
      return;
    }

    // Calculate the actual start time we need to fetch
    // We want to load data that ends at our current earliest time
    const endTime = store.earliestTime > 0 ? store.earliestTime : Math.floor(Date.now() / 1000);
    const startTime = endTime - PAGINATION_DAYS * 24 * 60 * 60;

    // Check for pending request with the correct key
    const pendingKey = `historical_${tokenAddress}_${startTime}_${endTime}`;
    const existingRequest = this._pendingRequests.get(pendingKey);
    if (existingRequest) {
      await existingRequest.promise;
      return;
    }

    const requestPromise = this.fetchOhlcvData(tokenAddress, "1m", startTime, endTime);

    this._pendingRequests.set(pendingKey, {
      promise: requestPromise,
      startTime,
      endTime,
    });

    try {
      const newBars = await requestPromise;

      if (newBars.length > 0) {
        // Merge new bars with existing ones
        const allBars = [...newBars, ...store.bars];
        const uniqueBars = this.deduplicateBars(allBars);
        store.bars = uniqueBars.sort((a, b) => a.time - b.time);

        const oldEarliestTime = store.earliestTime;
        store.earliestTime = store.bars[0].time / 1000;
        store.lastUpdated = Date.now();

        // Check if we actually got older data
        if (oldEarliestTime > 0 && store.earliestTime >= oldEarliestTime) {
          store.isComplete = true;
        }
      } else {
        // No more data available - mark as complete
        store.isComplete = true;
      }
    } finally {
      this._pendingRequests.delete(pendingKey);
    }
  }

  /**
   * Aggregate 1-minute bars into the requested timeframe
   */
  private aggregateBars(minuteBars: TvBar[], targetResolution: string): TvBar[] {
    if (targetResolution === "1") {
      return minuteBars; // No aggregation needed for 1-minute
    }

    const intervalMs = this.getIntervalMs(targetResolution);
    if (!intervalMs) {
      return minuteBars;
    }

    const aggregatedBars: TvBar[] = [];
    const barGroups = new Map<number, TvBar[]>();

    // Group bars by their aligned timestamp
    for (const bar of minuteBars) {
      const alignedTime = this.alignToInterval(bar.time, intervalMs);
      if (!barGroups.has(alignedTime)) {
        barGroups.set(alignedTime, []);
      }
      barGroups.get(alignedTime)!.push(bar);
    }

    // Aggregate each group into a single bar
    for (const [alignedTime, bars] of Array.from(barGroups.entries()).sort(([a], [b]) => a - b)) {
      if (bars.length === 0) continue;

      // Sort bars by time within the group
      bars.sort((a, b) => a.time - b.time);

      const aggregatedBar: TvBar = {
        time: alignedTime,
        open: bars[0].open,
        high: Math.max(...bars.map((b) => b.high)),
        low: Math.min(...bars.map((b) => b.low)),
        close: bars[bars.length - 1].close,
        volume: bars.reduce((sum, b) => sum + (b.volume || 0), 0),
      };

      aggregatedBars.push(aggregatedBar);
    }

    return aggregatedBars;
  }

  /**
   * Filter bars to a specific time range
   */
  private filterBarsToRange(bars: TvBar[], startTime: number, endTime: number): TvBar[] {
    return bars.filter((bar) => {
      const barTimeSeconds = bar.time / 1000;
      return barTimeSeconds >= startTime && barTimeSeconds < endTime;
    });
  }

  /**
   * Remove duplicate bars based on timestamp
   */
  private deduplicateBars(bars: TvBar[]): TvBar[] {
    const seen = new Set<number>();
    return bars.filter((bar) => {
      if (seen.has(bar.time)) return false;
      seen.add(bar.time);
      return true;
    });
  }

  /**
   * Clean bars to ensure proper candle continuity
   */
  private cleanBars(bars: TvBar[], resolution: string): TvBar[] {
    if (bars.length === 0) return bars;

    // Get interval for alignment
    const intervalMs = this.getIntervalMs(resolution);
    if (!intervalMs) return bars;

    // For 1-minute data, skip time alignment since API data is already properly aligned
    const shouldAlignTime = resolution !== "1";

    // Clean and sort the bars
    const cleanedBars = bars
      .map((bar) => ({
        ...bar,
        time: shouldAlignTime ? this.alignToInterval(bar.time, intervalMs) : bar.time,
      }))
      .sort((a, b) => a.time - b.time);

    // Remove duplicates (same timestamp)
    const uniqueBars: TvBar[] = [];
    for (const bar of cleanedBars) {
      if (uniqueBars.length === 0 || uniqueBars[uniqueBars.length - 1].time !== bar.time) {
        uniqueBars.push(bar);
      }
    }

    // Ensure continuity between bars - each bar's open should match previous bar's close
    const connectedBars: TvBar[] = [];
    for (let i = 0; i < uniqueBars.length; i++) {
      const currentBar = { ...uniqueBars[i] };

      // For bars after the first, ensure open price matches previous close
      if (i > 0) {
        const previousBar = connectedBars[i - 1];
        currentBar.open = previousBar.close;

        // Adjust high/low if needed to accommodate the new open price
        currentBar.high = Math.max(currentBar.high, currentBar.open, currentBar.close);
        currentBar.low = Math.min(currentBar.low, currentBar.open, currentBar.close);
      }

      connectedBars.push(currentBar);
    }

    return connectedBars;
  }

  /**
   * Get interval in milliseconds for a given resolution
   */
  private getIntervalMs(resolution: string): number | null {
    // Handle minute-based intervals (1, 5, 15, 30, etc.)
    if (resolution.match(/^\d+$/)) {
      const minutes = parseInt(resolution, 10);
      return minutes * 60 * 1000;
    }

    // Handle hour-based intervals (1H, 2H, 4H, etc.)
    if (resolution.match(/^\d+H$/)) {
      const hours = parseInt(resolution.replace("H", ""), 10);
      return hours * 60 * 60 * 1000;
    }

    // Handle day-based intervals
    if (resolution === "1D" || resolution === "D") return 24 * 60 * 60 * 1000;
    if (resolution.match(/^\d+D$/)) {
      const days = parseInt(resolution.replace("D", ""), 10);
      return days * 24 * 60 * 60 * 1000;
    }

    // Handle week-based intervals
    if (resolution === "1W" || resolution === "W") return 7 * 24 * 60 * 60 * 1000;
    if (resolution.match(/^\d+W$/)) {
      const weeks = parseInt(resolution.replace("W", ""), 10);
      return weeks * 7 * 24 * 60 * 60 * 1000;
    }

    // Handle month-based intervals (approximate - 30 days)
    if (resolution === "1M" || resolution === "M") return 30 * 24 * 60 * 60 * 1000;
    if (resolution.match(/^\d+M$/)) {
      const months = parseInt(resolution.replace("M", ""), 10);
      return months * 30 * 24 * 60 * 60 * 1000;
    }

    console.warn(`[Datafeed] Unknown resolution format: ${resolution}`);
    return null;
  }

  /**
   * Align timestamp to the nearest interval boundary
   */
  private alignToInterval(timestamp: number, intervalMs: number): number {
    return Math.floor(timestamp / intervalMs) * intervalMs;
  }

  /**
   * Enable real-time updates
   */
  public enableRealTimeUpdates(): void {
    if (this._isRealTimeEnabled) return;

    this._isRealTimeEnabled = true;
  }

  /**
   * Disable real-time updates
   */
  public disableRealTimeUpdates(): void {
    this._isRealTimeEnabled = false;
    this._stopRealTimeUpdates();
  }

  /**
   * Start polling fallback (only when WebSocket fails)
   */
  public startPollingFallback(): void {
    if (!this._isRealTimeEnabled || this._updateInterval) return;

    this._startRealTimeUpdates();
  }

  /**
   * Update real-time bar from WebSocket
   */
  public async updateRealtimeBar(tokenAddress: string, ohlcvData: OhlcvDataPoint): Promise<void> {
    if (!this._isRealTimeEnabled) {
      return;
    }

    // Find all subscriptions for this token
    const subscriptions = Array.from(this._subscriptions.values()).filter(
      (sub) => sub.symbolInfo.ticker?.toLowerCase() === tokenAddress.toLowerCase(),
    );

    if (subscriptions.length === 0) {
      return;
    }

    console.log(`[Datafeed] Processing websocket OHLCV update for ${tokenAddress}:`, ohlcvData);

    // Update all subscriptions for this token
    for (const subscription of subscriptions) {
      try {
        // Transform the WebSocket OHLCV data to TradingView bar format
        const newBar = await this._transformWebSocketOhlcvToBar(ohlcvData, subscription.resolution);

        console.log(`[Datafeed] Transformed bar for resolution ${subscription.resolution}:`, {
          ...newBar,
          timeFormatted: new Date(newBar.time).toISOString(),
          lastBarTime: subscription.lastBarTime,
          lastBarTimeFormatted: new Date(subscription.lastBarTime).toISOString(),
        });

        // Initialize lastBarTime if not set
        if (subscription.lastBarTime === 0) {
          const store = this.getTokenDataStore(tokenAddress);
          if (store.bars.length > 0) {
            // Set to the last bar time in our store
            const lastStoredBar = store.bars[store.bars.length - 1];
            subscription.lastBarTime = lastStoredBar.time;
            console.log(
              `[Datafeed] Initialized lastBarTime to ${subscription.lastBarTime} for ${subscription.subscriberUID}`,
            );
          }
        }

        // Check if this is a new bar or an update to the current bar
        const isNewBar = newBar.time > subscription.lastBarTime;
        const isCurrentBarUpdate = newBar.time === subscription.lastBarTime;

        if (isNewBar || isCurrentBarUpdate) {
          // Always update the 1-minute data store first
          const store = this.getTokenDataStore(tokenAddress);

          // Convert websocket data to 1-minute bar for storage
          const minuteBar = await this._transformWebSocketOhlcvToBar(ohlcvData, "1");
          const existingBarIndex = store.bars.findIndex((bar) => bar.time === minuteBar.time);

          if (existingBarIndex >= 0) {
            // Update existing 1-minute bar (preserve open price for continuity)
            const existingBar = store.bars[existingBarIndex];
            store.bars[existingBarIndex] = {
              ...minuteBar,
              open: existingBar.open, // Keep the original open price for continuity
              high: Math.max(existingBar.high, minuteBar.high),
              low: Math.min(existingBar.low, minuteBar.low),
              // close and volume can be updated
            };
            console.log(`[Datafeed] Updated existing 1-minute bar at index ${existingBarIndex}`);
          } else {
            // Apply price continuity to new 1-minute bars
            if (store.bars.length > 0) {
              const sortedBars = store.bars.sort((a, b) => a.time - b.time);
              const lastBar = sortedBars[sortedBars.length - 1];

              if (minuteBar.time > lastBar.time) {
                // This is a new bar, ensure price continuity
                const originalOpen = minuteBar.open;
                minuteBar.open = lastBar.close;
                minuteBar.high = Math.max(minuteBar.high, minuteBar.open, minuteBar.close);
                minuteBar.low = Math.min(minuteBar.low, minuteBar.open, minuteBar.close);

                console.log(
                  `[Datafeed] Applied price continuity to 1-minute bar - open changed from ${originalOpen} to ${minuteBar.open}`,
                );
              }
            }

            // Add new 1-minute bar
            store.bars.push(minuteBar);
            store.bars.sort((a, b) => a.time - b.time);
            console.log(`[Datafeed] Added new 1-minute bar to store`);
          }

          store.latestTime = Math.max(store.latestTime, minuteBar.time / 1000);
          store.lastUpdated = Date.now();

          // Handle price continuity and aggregation
          let finalBar = newBar;

          if (subscription.resolution !== "1") {
            // For aggregated resolutions, re-aggregate from 1-minute data
            const intervalMs = this.getIntervalMs(subscription.resolution);
            if (intervalMs) {
              const barStartTime = newBar.time;
              const barEndTime = barStartTime + intervalMs;

              // Find all 1-minute bars within this aggregated bar's time range
              const barsInRange = store.bars.filter((bar) => bar.time >= barStartTime && bar.time < barEndTime);

              if (barsInRange.length > 0) {
                // Re-aggregate the bars for this timeframe
                barsInRange.sort((a, b) => a.time - b.time);

                finalBar = {
                  time: barStartTime,
                  open: barsInRange[0].open,
                  high: Math.max(...barsInRange.map((b) => b.high)),
                  low: Math.min(...barsInRange.map((b) => b.low)),
                  close: barsInRange[barsInRange.length - 1].close,
                  volume: barsInRange.reduce((sum, b) => sum + (b.volume || 0), 0),
                };

                console.log(`[Datafeed] Re-aggregated bar from ${barsInRange.length} 1-minute bars:`, finalBar);
              }
            }
          }

          // Apply price continuity for new bars (all resolutions)
          if (isNewBar && store.bars.length > 1) {
            let previousBar: TvBar | null = null;

            if (subscription.resolution === "1") {
              // For 1-minute resolution, find the previous 1-minute bar
              const sortedBars = store.bars.filter((bar) => bar.time < finalBar.time).sort((a, b) => a.time - b.time);

              if (sortedBars.length > 0) {
                previousBar = sortedBars[sortedBars.length - 1];
              }
            } else {
              // For aggregated resolutions, find the previous aggregated bar
              const lastAggregatedBars = this.aggregateBars(
                store.bars.filter((bar) => bar.time < finalBar.time),
                subscription.resolution,
              );

              if (lastAggregatedBars.length > 0) {
                previousBar = lastAggregatedBars[lastAggregatedBars.length - 1];
              }
            }

            if (previousBar) {
              // Ensure the new bar's open matches the previous bar's close
              const originalOpen = finalBar.open;
              finalBar.open = previousBar.close;

              // Adjust high/low to accommodate the new open
              finalBar.high = Math.max(finalBar.high, finalBar.open, finalBar.close);
              finalBar.low = Math.min(finalBar.low, finalBar.open, finalBar.close);

              console.log(
                `[Datafeed] Applied price continuity - open changed from ${originalOpen} to ${finalBar.open} (previous close: ${previousBar.close})`,
              );
            }
          }

          // Check for gaps and fill them dynamically for any interval
          const barsToSend: TvBar[] = [];

          if (isNewBar && subscription.lastBarTime > 0) {
            const intervalMs = this.getIntervalMs(subscription.resolution);
            if (intervalMs) {
              const expectedNextTime = subscription.lastBarTime + intervalMs;
              const timeDifference = finalBar.time - subscription.lastBarTime;
              const gapThreshold = intervalMs * 1.5; // Allow for some timing variance

              // If there's a gap larger than 1.5 intervals, fill it
              if (timeDifference > gapThreshold) {
                const gapCount = Math.floor(timeDifference / intervalMs) - 1;
                console.log(
                  `[Datafeed] Gap detected for ${subscription.resolution} resolution: ${gapCount} missing bars between ${new Date(subscription.lastBarTime).toISOString()} and ${new Date(finalBar.time).toISOString()}`,
                );

                // Get the previous bar's close price for gap filling
                let gapFillPrice = finalBar.open; // Default fallback

                // Find the appropriate previous bar based on resolution
                if (subscription.resolution === "1") {
                  // For 1-minute resolution, find the previous 1-minute bar
                  const previousBars = store.bars
                    .filter((bar) => bar.time <= subscription.lastBarTime)
                    .sort((a, b) => a.time - b.time);

                  if (previousBars.length > 0) {
                    gapFillPrice = previousBars[previousBars.length - 1].close;
                  }
                } else {
                  // For aggregated resolutions, get the previous aggregated bar
                  const previousAggregatedBars = this.aggregateBars(
                    store.bars.filter((bar) => bar.time <= subscription.lastBarTime),
                    subscription.resolution,
                  );

                  if (previousAggregatedBars.length > 0) {
                    gapFillPrice = previousAggregatedBars[previousAggregatedBars.length - 1].close;
                  }
                }

                // Create synthetic bars to fill the gap
                let currentFillTime = expectedNextTime;
                let syntheticBarsCreated = 0;

                while (currentFillTime < finalBar.time && syntheticBarsCreated < gapCount) {
                  const syntheticBar: TvBar = {
                    time: currentFillTime,
                    open: gapFillPrice,
                    high: gapFillPrice,
                    low: gapFillPrice,
                    close: gapFillPrice,
                    volume: 0, // No volume for synthetic bars
                  };

                  barsToSend.push(syntheticBar);
                  console.log(
                    `[Datafeed] Created synthetic ${subscription.resolution} bar at ${new Date(currentFillTime).toISOString()}`,
                  );

                  // For 1-minute resolution, add synthetic bars directly to store
                  if (subscription.resolution === "1") {
                    // Check if this synthetic bar already exists
                    const existingSyntheticIndex = store.bars.findIndex((bar) => bar.time === currentFillTime);
                    if (existingSyntheticIndex === -1) {
                      store.bars.push(syntheticBar);
                      console.log(`[Datafeed] Added synthetic 1-minute bar to store`);
                    }
                  } else {
                    // For aggregated resolutions, create corresponding 1-minute synthetic bars
                    const minuteIntervalMs = 60 * 1000; // 1 minute in milliseconds
                    const barsInThisInterval = Math.floor(intervalMs / minuteIntervalMs);

                    for (let i = 0; i < barsInThisInterval; i++) {
                      const minuteBarTime = currentFillTime + i * minuteIntervalMs;

                      // Only add if this minute bar doesn't already exist and is within bounds
                      if (minuteBarTime < currentFillTime + intervalMs) {
                        const existingMinuteIndex = store.bars.findIndex((bar) => bar.time === minuteBarTime);
                        if (existingMinuteIndex === -1) {
                          const syntheticMinuteBar: TvBar = {
                            time: minuteBarTime,
                            open: gapFillPrice,
                            high: gapFillPrice,
                            low: gapFillPrice,
                            close: gapFillPrice,
                            volume: 0,
                          };

                          store.bars.push(syntheticMinuteBar);
                          console.log(
                            `[Datafeed] Added synthetic 1-minute bar to store for ${subscription.resolution} gap at ${new Date(minuteBarTime).toISOString()}`,
                          );
                        }
                      }
                    }
                  }

                  currentFillTime += intervalMs;
                  syntheticBarsCreated++;
                }

                // Sort the store after adding synthetic bars
                if (barsToSend.length > 0) {
                  store.bars.sort((a, b) => a.time - b.time);
                  const latestSyntheticTime = Math.max(...barsToSend.map((bar) => bar.time));
                  store.latestTime = Math.max(store.latestTime, latestSyntheticTime / 1000);
                  console.log(
                    `[Datafeed] Added ${syntheticBarsCreated} synthetic bars for ${subscription.resolution} resolution`,
                  );
                }

                // Update the final bar's open to match the last synthetic bar's close
                if (barsToSend.length > 0) {
                  const lastSyntheticBar = barsToSend[barsToSend.length - 1];
                  finalBar.open = lastSyntheticBar.close;
                  finalBar.high = Math.max(finalBar.high, finalBar.open, finalBar.close);
                  finalBar.low = Math.min(finalBar.low, finalBar.open, finalBar.close);
                  console.log(
                    `[Datafeed] Updated final ${subscription.resolution} bar open to match synthetic bar close: ${finalBar.open}`,
                  );
                }
              }
            }
          }

          // Add the final bar to the list
          barsToSend.push(finalBar);

          // Send all bars to TradingView (synthetic bars first, then the real bar)
          for (const barToSend of barsToSend) {
            const isRealBar = barToSend === finalBar;
            const isSyntheticBar = !isRealBar;

            // Update subscription tracking for all bars
            if (barToSend.time > subscription.lastBarTime) {
              subscription.lastBarTime = barToSend.time;
            }

            console.log(
              `[Datafeed] Sending ${isSyntheticBar ? "synthetic" : isNewBar ? "new" : "updated"} bar to TradingView:`,
              {
                ...barToSend,
                timeFormatted: new Date(barToSend.time).toISOString(),
              },
            );

            subscription.onRealtimeCallback(barToSend);
          }
        } else {
          console.log(
            `[Datafeed] Skipping bar - time ${newBar.time} is not newer than lastBarTime ${subscription.lastBarTime}`,
          );
        }
      } catch (error) {
        console.error(`[Datafeed] Error updating real-time bar for ${tokenAddress}:`, error);
      }
    }
  }

  /**
   * Transform WebSocket OHLCV data to TradingView bar format
   */
  private async _transformWebSocketOhlcvToBar(ohlcvData: OhlcvDataPoint, resolution: string): Promise<TvBar> {
    const timeMs = new Date(ohlcvData.time).getTime();
    if (isNaN(timeMs)) {
      throw new Error(`Invalid timestamp: ${ohlcvData.time}`);
    }

    // For real-time updates, we need to be more careful about time alignment
    // to ensure continuity with existing chart data
    let alignedTime = timeMs;

    if (resolution !== "1") {
      // For aggregated resolutions, align to interval boundary
      const intervalMs = this.getIntervalMs(resolution);
      if (intervalMs) {
        alignedTime = this.alignToInterval(timeMs, intervalMs);
      }
    }
    // For 1-minute resolution, use the exact timestamp from the websocket

    let { open, high, low, close } = ohlcvData;
    const { volume } = ohlcvData;

    // Apply market cap transformation if enabled
    if (this._showMarketCap) {
      const hypePriceUsd = await this._getHypePriceUsd();
      const multiplier = Math.pow(10, DECIMALS) * hypePriceUsd * HARDCODED_TOTAL_SUPPLY;

      open *= multiplier;
      high *= multiplier;
      low *= multiplier;
      close *= multiplier;
    }

    return {
      time: alignedTime,
      open,
      high,
      low,
      close,
      volume,
    };
  }

  // TradingView Datafeed API Implementation

  onReady(callback: (configuration: DatafeedConfiguration) => void): void {
    setTimeout(() => {
      callback({
        supported_resolutions: SUPPORTED_RESOLUTIONS,
        supports_search: false,
        supports_group_request: false,
        supports_marks: false,
        supports_timescale_marks: false,
        supports_time: false,
      });
    }, 0);
  }

  resolveSymbol(
    symbolName: string,
    onSymbolResolvedCallback: (symbolInfo: TvSymbolInfo) => void,
    onResolveErrorCallback: (reason: string) => void,
  ): void {
    try {
      const tokenAddress = symbolName.toLowerCase();
      const tokenInfo = this.tokens.get(tokenAddress);

      const symbolInfo: TvSymbolInfo = {
        ticker: tokenAddress,
        name: tokenInfo?.name || symbolName,
        full_name: tokenInfo?.name || symbolName,
        description: tokenInfo?.metadata?.description || `${tokenInfo?.name || symbolName} Token`,
        type: "crypto",
        session: "24x7",
        timezone: "Etc/UTC",
        exchange: "LiquidLaunch",
        minmov: 1,
        pricescale: this._showMarketCap ? 100 : Math.pow(10, DECIMALS),
        has_intraday: true,
        has_daily: true,
        has_weekly_and_monthly: true,
        supported_resolutions: SUPPORTED_RESOLUTIONS,
        currency_code: "HYPE",
        volume_precision: 2,
        has_empty_bars: true,
      };

      setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
    } catch (error) {
      console.error("[Datafeed] Error resolving symbol:", error);
      onResolveErrorCallback("Failed to resolve symbol");
    }
  }

  async getBars(
    symbolInfo: TvSymbolInfo,
    resolution: string,
    periodParams: { from: number; to: number; firstDataRequest: boolean; countBack?: number },
    onHistoryCallback: (bars: TvBar[], meta: TvHistoryMetadata) => void,
    onErrorCallback: (error: unknown) => void,
  ): Promise<void> {
    console.log(
      `[Datafeed] getBars called for ${symbolInfo.ticker}, resolution: ${resolution}, period: ${new Date(periodParams.from * 1000).toISOString()} to ${new Date(periodParams.to * 1000).toISOString()}, firstDataRequest: ${periodParams.firstDataRequest}`,
    );

    try {
      const tokenAddress = symbolInfo.ticker;
      if (!tokenAddress) {
        onHistoryCallback([], { noData: true });
        return;
      }

      // Load initial data if this is the first request
      if (periodParams.firstDataRequest) {
        await this.loadInitialData(tokenAddress);

        // For 1-minute data, if the requested range is very narrow, load more historical data
        if (resolution === "1") {
          const store = this.getTokenDataStore(tokenAddress);
          const requestedRangeHours = (periodParams.to - periodParams.from) / 3600;

          // If TradingView is requesting less than 24 hours for 1-minute, load more historical data
          if (requestedRangeHours < 24 && periodParams.from < store.earliestTime && !store.isComplete) {
            await this.loadMoreHistoricalData(tokenAddress, periodParams.from);
          }
        }
      }

      const store = this.getTokenDataStore(tokenAddress);

      // Check if we need to load more historical data
      // We should load more if the requested start time is before our earliest data
      // OR if we're getting close to the edge (within 1 day for 1-minute data)
      const timeUntilEdge = periodParams.from - store.earliestTime;
      const shouldLoadMore =
        (periodParams.from < store.earliestTime || (resolution === "1" && timeUntilEdge < 24 * 60 * 60)) &&
        !store.isComplete;

      if (shouldLoadMore) {
        await this.loadMoreHistoricalData(tokenAddress, periodParams.from);
      }

      // Get all available 1-minute data
      const allMinuteBars = store.bars;

      if (allMinuteBars.length === 0) {
        console.log(`[Datafeed] No data available for ${tokenAddress}`);
        onHistoryCallback([], { noData: true });
        return;
      }

      // Fill gaps in 1-minute data first
      const filledMinuteBars = this.fillGaps(allMinuteBars, "1");

      // Aggregate to the requested timeframe
      const aggregatedBars = this.aggregateBars(filledMinuteBars, resolution);

      // Filter to the requested time range
      const filteredBars = this.filterBarsToRange(aggregatedBars, periodParams.from, periodParams.to);

      // Clean bars for proper continuity
      const cleanedBars = this.cleanBars(filteredBars, resolution);

      // Fill gaps to ensure smooth chart display
      const filledBars = this.fillGaps(cleanedBars, resolution);

      const meta: TvHistoryMetadata = { noData: filledBars.length === 0 };

      // If we have no data in the requested range but have data overall, suggest where to look
      if (filledBars.length === 0 && aggregatedBars.length > 0) {
        const earliestBar = aggregatedBars[0];
        const latestBar = aggregatedBars[aggregatedBars.length - 1];

        if (latestBar.time / 1000 < periodParams.from) {
          // All our data is before the requested range
          meta.nextTime = latestBar.time;
        } else if (earliestBar.time / 1000 > periodParams.to) {
          // All our data is after the requested range
          meta.nextTime = earliestBar.time;
        } else {
          // We have data that spans the requested range, but no data in this specific window
          // This is likely a gap in trading data (e.g., overnight)
          // For 1-minute data, guide TradingView to the nearest available data
          if (resolution === "1") {
            // Find the closest bar to the requested range
            let closestBar = earliestBar;
            let minDistance = Math.abs(earliestBar.time / 1000 - periodParams.from);

            for (const bar of aggregatedBars) {
              const distance = Math.abs(bar.time / 1000 - periodParams.from);
              if (distance < minDistance) {
                minDistance = distance;
                closestBar = bar;
              }
            }

            meta.nextTime = closestBar.time;
          }
        }
      }

      onHistoryCallback(filledBars, meta);
    } catch (error) {
      console.error("[Datafeed] Error in getBars:", error);

      // Handle API errors gracefully - server action already handles retries
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();

        // Handle rate limiting and server errors gracefully
        if (
          errorMessage.includes("429") ||
          errorMessage.includes("too many requests") ||
          errorMessage.includes("502") ||
          errorMessage.includes("503") ||
          errorMessage.includes("504") ||
          errorMessage.includes("timeout") ||
          errorMessage.includes("network")
        ) {
          console.warn("[Datafeed] API temporarily unavailable, returning empty data");
          onHistoryCallback([], { noData: true });
          return;
        }
      }

      onErrorCallback(error);
    }
  }

  /**
   * Fetch OHLCV data from API and transform it
   */
  private async fetchOhlcvData(
    tokenAddress: string,
    apiInterval: string,
    startTime: number,
    endTime: number,
  ): Promise<TvBar[]> {
    const params: GetOhlcvParams = {
      interval: apiInterval,
      start: new Date(startTime * 1000).toISOString(),
      end: new Date(endTime * 1000).toISOString(),
    };

    const ohlcvData = await getOhlcvAction(tokenAddress, params);

    if (!ohlcvData || ohlcvData.length === 0) {
      return [];
    }

    // Get HYPE price for market cap calculations if needed
    let hypePriceUsd = 1;
    if (this._showMarketCap) {
      hypePriceUsd = await this._getHypePriceUsd();
    }

    const transformedBars: TvBar[] = ohlcvData
      .map((dp) => {
        const timeMs = new Date(dp.time).getTime();
        if (isNaN(timeMs)) {
          return null;
        }

        // Raw prices from API
        let open = dp.open;
        let high = dp.high;
        let low = dp.low;
        let close = dp.close;
        const volume = dp.volume;

        if (this._showMarketCap) {
          // Transform to market cap values
          const multiplier = Math.pow(10, DECIMALS) * hypePriceUsd * HARDCODED_TOTAL_SUPPLY;
          open *= multiplier;
          high *= multiplier;
          low *= multiplier;
          close *= multiplier;
        }

        return {
          time: timeMs,
          open,
          high,
          low,
          close,
          volume,
        };
      })
      .filter((bar) => bar !== null) as TvBar[];

    // Sort bars by time
    transformedBars.sort((a, b) => a.time - b.time);

    return transformedBars;
  }

  /**
   * Get interval in seconds for a given resolution
   */
  private getIntervalSeconds(resolution: string): number | null {
    if (resolution.match(/^\d+$/)) {
      const minutes = parseInt(resolution, 10);
      return minutes * 60;
    }
    if (resolution === "1D" || resolution === "D") return 24 * 60 * 60;
    if (resolution === "1W" || resolution === "W") return 7 * 24 * 60 * 60;
    return null;
  }

  subscribeBars(
    symbolInfo: TvSymbolInfo,
    resolution: string,
    onRealtimeCallback: (bar: TvBar) => void,
    subscriberUID: string,
  ): void {
    const tokenAddress = symbolInfo.ticker;
    if (!tokenAddress) return;

    // Initialize lastBarTime based on existing data
    let lastBarTime = 0;
    const store = this.getTokenDataStore(tokenAddress);

    if (store.bars.length > 0) {
      if (resolution === "1") {
        // For 1-minute resolution, use the last bar directly
        lastBarTime = store.bars[store.bars.length - 1].time;
      } else {
        // For other resolutions, aggregate and get the last bar
        const aggregatedBars = this.aggregateBars(store.bars, resolution);
        if (aggregatedBars.length > 0) {
          lastBarTime = aggregatedBars[aggregatedBars.length - 1].time;
        } else {
          // If no aggregated bars, align the last 1-minute bar to the resolution
          const lastMinuteBar = store.bars[store.bars.length - 1];
          const intervalMs = this.getIntervalMs(resolution);
          if (intervalMs) {
            lastBarTime = this.alignToInterval(lastMinuteBar.time, intervalMs);
          }
        }
      }
    }

    const subscription: RealtimeSubscription = {
      symbolInfo,
      resolution,
      onRealtimeCallback,
      subscriberUID,
      lastBarTime,
    };

    this._subscriptions.set(subscriberUID, subscription);

    console.log(
      `[Datafeed] Subscribed to ${resolution} bars for ${tokenAddress}, lastBarTime: ${lastBarTime} (${new Date(lastBarTime).toISOString()})`,
    );
  }

  unsubscribeBars(subscriberUID: string): void {
    this._subscriptions.delete(subscriberUID);

    // Stop updates if no more subscriptions
    if (this._subscriptions.size === 0) {
      this._stopRealTimeUpdates();
    }
  }

  // Private helper methods

  private async _getHypePriceUsd(): Promise<number> {
    if (this._hypePriceUsd !== null) return this._hypePriceUsd;
    if (this._hypePricePromise) return this._hypePricePromise;

    this._hypePricePromise = getNativeHypePrice()
      .then((priceStr) => {
        const price = priceStr ? parseFloat(priceStr) : 1;
        this._hypePriceUsd = isNaN(price) ? 1 : price;
        return this._hypePriceUsd;
      })
      .catch(() => {
        this._hypePriceUsd = 1;
        return 1;
      });

    return this._hypePricePromise;
  }

  private _startRealTimeUpdates(): void {
    if (this._updateInterval) return;

    // Polling fallback for when WebSocket updates aren't available
    this._updateInterval = setInterval(async () => {
      for (const subscription of Array.from(this._subscriptions.values())) {
        try {
          await this._updateSubscription(subscription);
        } catch (error) {
          console.warn(`[Datafeed] Failed to update subscription ${subscription.subscriberUID}:`, error);
        }
      }
    }, 5000); // 5 second intervals
  }

  private _stopRealTimeUpdates(): void {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }

  private async _updateSubscription(subscription: RealtimeSubscription): Promise<void> {
    const { symbolInfo, resolution } = subscription;
    const tokenAddress = symbolInfo.ticker;

    if (!tokenAddress) return;

    // ALWAYS request 1-minute data for real-time updates
    const apiInterval = "1m";

    const now = Math.floor(Date.now() / 1000);
    const fiveMinutesAgo = now - 5 * 60;

    const params: GetOhlcvParams = {
      interval: apiInterval,
      start: new Date(fiveMinutesAgo * 1000).toISOString(),
      end: new Date(now * 1000).toISOString(),
    };

    try {
      const ohlcvData = await getOhlcvAction(tokenAddress, params);

      if (ohlcvData && ohlcvData.length > 0) {
        // Transform 1-minute data
        const minuteBars: TvBar[] = [];
        for (const dataPoint of ohlcvData) {
          try {
            const bar = await this._transformOhlcvToBar(dataPoint, "1");
            minuteBars.push(bar);
          } catch (error) {
            console.warn("[Datafeed] Skipping invalid polling bar:", error);
          }
        }

        // Sort bars by time
        minuteBars.sort((a, b) => a.time - b.time);

        // Aggregate to the subscription's timeframe if needed
        const aggregatedBars = this.aggregateBars(minuteBars, resolution);

        // Clean and connect bars for proper continuity
        const cleanedBars = this.cleanBars(aggregatedBars, resolution);

        // Fill gaps to ensure smooth chart display (same as initial data loading)
        const filledBars = this.fillGaps(cleanedBars, resolution);

        // Store new 1-minute bars in the data store for future continuity checks
        if (resolution === "1" && filledBars.length > 0) {
          const store = this.getTokenDataStore(tokenAddress);
          for (const bar of filledBars) {
            // Check if this bar already exists to avoid duplicates
            const existingBarIndex = store.bars.findIndex((existingBar) => existingBar.time === bar.time);
            if (existingBarIndex >= 0) {
              // Update existing bar
              store.bars[existingBarIndex] = bar;
            } else {
              // Add new bar
              store.bars.push(bar);
            }
          }
          // Sort and update metadata
          store.bars.sort((a, b) => a.time - b.time);
          store.latestTime = Math.max(store.latestTime, filledBars[filledBars.length - 1].time / 1000);
          store.lastUpdated = Date.now();
        }

        // Process all bars that are newer than our last bar time
        for (const bar of filledBars) {
          if (bar.time > subscription.lastBarTime) {
            subscription.lastBarTime = bar.time;
            subscription.onRealtimeCallback(bar);
          }
        }
      }
    } catch (error) {
      // Silently handle 502 errors
      if (!(error instanceof Error && error.message.includes("502"))) {
        throw error;
      }
    }
  }

  private async _transformOhlcvToBar(dataPoint: OhlcvDataPoint, resolution: string): Promise<TvBar> {
    const timeMs = new Date(dataPoint.time).getTime();
    if (isNaN(timeMs)) {
      throw new Error(`Invalid timestamp: ${dataPoint.time}`);
    }

    // Align timestamp to interval boundary for consistent charting
    const intervalMs = this.getIntervalMs(resolution);
    const alignedTime = intervalMs ? this.alignToInterval(timeMs, intervalMs) : timeMs;

    let { open, high, low, close } = dataPoint;
    const { volume } = dataPoint;

    // Apply market cap transformation if enabled
    if (this._showMarketCap) {
      const hypePriceUsd = await this._getHypePriceUsd();
      const multiplier = Math.pow(10, DECIMALS) * hypePriceUsd * HARDCODED_TOTAL_SUPPLY;

      open *= multiplier;
      high *= multiplier;
      low *= multiplier;
      close *= multiplier;
    }

    return {
      time: alignedTime,
      open,
      high,
      low,
      close,
      volume,
    };
  }

  /**
   * Fill gaps in bars with synthetic bars to ensure continuity
   */
  private fillGaps(bars: TvBar[], resolution: string): TvBar[] {
    if (bars.length === 0) return bars;

    const intervalMs = this.getIntervalMs(resolution);
    if (!intervalMs) return bars;

    const filledBars: TvBar[] = [];

    for (let i = 0; i < bars.length; i++) {
      const currentBar = bars[i];
      filledBars.push(currentBar);

      // Check if there's a gap to the next bar
      if (i < bars.length - 1) {
        const nextBar = bars[i + 1];
        const expectedNextTime = currentBar.time + intervalMs;
        const actualNextTime = nextBar.time;

        // If there's a gap larger than one interval, fill it
        if (actualNextTime > expectedNextTime + intervalMs / 2) {
          let fillTime = expectedNextTime;

          // Create synthetic bars to fill the gap
          while (fillTime < actualNextTime) {
            const syntheticBar: TvBar = {
              time: fillTime,
              open: currentBar.close,
              high: currentBar.close,
              low: currentBar.close,
              close: currentBar.close,
              volume: 0, // No volume for synthetic bars
            };
            filledBars.push(syntheticBar);
            fillTime += intervalMs;
          }
        }
      }
    }

    return filledBars;
  }
}

// Global instance for development
declare global {
  interface Window {
    __liquidLaunchDatafeed?: LiquidLaunchDatafeed;
    debugChart?: (tokenAddress?: string) => void;
    debugTimeAlignment?: (timestamp: string | number, resolution: string) => void;
  }
}

// Make the datafeed available globally for debugging
if (typeof window !== "undefined") {
  window.__liquidLaunchDatafeed = LiquidLaunchDatafeed.getInstance();

  // Add global debug function
  window.debugChart = (tokenAddress?: string) => {
    const datafeed = LiquidLaunchDatafeed.getInstance();
    const debugInfo = datafeed.getDebugInfo(tokenAddress);
    console.log("=== Chart Debug Info ===");
    console.log(debugInfo);
    return debugInfo;
  };

  // Add time alignment debug function
  window.debugTimeAlignment = (timestamp: string | number, resolution: string) => {
    const datafeed = LiquidLaunchDatafeed.getInstance();
    const alignmentInfo = datafeed.debugTimeAlignment(timestamp, resolution);
    console.log("=== Time Alignment Debug ===");
    console.log(alignmentInfo);
    return alignmentInfo;
  };
}
