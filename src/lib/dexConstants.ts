// Helper function to convert basis points to percentage string
const bpsToPercentage = (bps: number): string => {
  return (bps / 10000).toFixed(2).replace(/\.00$/, "").replace(/\.0$/, "") + "%";
};

// Helper function to get fee display by basis points
export function getFeeDisplayForAllocation(fee: number, routerName: string, stable: boolean): string {
  if (routerName === "KittenSwapV2" && stable) {
    return "0.02%";
  } else if (routerName === "KittenSwapV2" && !stable) {
    return "0.30%";
  } else {
    return bpsToPercentage(fee);
  }
}

export function getFeeDisplayForDexComparison(fee: string, routerName: string, stable?: boolean): string {
  // Handle KittenSwapV2 specifically using the stable flag if available
  if (routerName === "KittenSwapV2") {
    // Default to volatile if stable is undefined, though the API should provide it
    return stable === true ? "0.02%" : "0.30%";
  }

  // For other DEXes, try converting numeric basis points
  const feeNumber = parseInt(fee, 10);
  if (!isNaN(feeNumber)) {
    return bpsToPercentage(feeNumber);
  }

  // Fallback for non-numeric, non-KittenSwapV2 fees (should ideally not happen)
  console.warn(`[getFeeDisplayForDexComparison] Unexpected non-numeric fee '${fee}' for ${routerName}`);
  return fee; // Return the original string as a last resort
}

/**
 * DEX Constants
 * Contains centralized information about supported DEXes
 */

export interface DexInfo {
  id: number;
  name: string;
  feeTiers: {
    // Fee tiers in basis points (100 = 1%)
    values: number[];
    // Human-readable fee tiers (e.g., "0.3%")
    display: string[];
  };
}

// DEX IDs
export const DEX_IDS = {
  KITTEN_SWAP_V2: 1,
  HYPER_SWAP_V2: 2,
  HYPER_SWAP_V3: 3,
  LAMINAR_V3: 4,
  KITTEN_SWAP_V3: 5,
} as const;

// DEX Information
export const DEXES: DexInfo[] = [
  {
    id: DEX_IDS.KITTEN_SWAP_V2,
    name: "KittenSwapV2",
    feeTiers: {
      values: [2, 30], // 0.02%, 0.3% in basis points
      display: ["0.02%", "0.3%"],
    },
  },
  {
    id: DEX_IDS.KITTEN_SWAP_V3,
    name: "KittenSwapV3",
    feeTiers: {
      values: [1, 25, 75], // 0.01%, 0.25%, 0.75% in basis points
      display: ["0.01%", "0.25%", "0.75%"],
    },
  },
  {
    id: DEX_IDS.HYPER_SWAP_V2,
    name: "HyperSwapV2",
    feeTiers: {
      values: [30], // 0.3% in basis points
      display: ["0.3%"],
    },
  },
  {
    id: DEX_IDS.HYPER_SWAP_V3,
    name: "HyperSwapV3",
    feeTiers: {
      values: [5, 30, 100], // 0.05%, 0.3%, 1% in basis points
      display: ["0.05%", "0.3%", "1%"],
    },
  },
  {
    id: DEX_IDS.LAMINAR_V3,
    name: "LaminarV3",
    feeTiers: {
      values: [5, 30, 100], // 0.05%, 0.3%, 1% in basis points
      display: ["0.05%", "0.3%", "1%"],
    },
  },
];
