/**
 * Represents a single data point in an OHLCV (Open, High, Low, Close, Volume) series.
 */
export interface OhlcvDataPoint {
  /** The timestamp for this data point, in ISO format. */
  time: string;
  /** The opening price. */
  open: number;
  /** The highest price. */
  high: number;
  /** The lowest price. */
  low: number;
  /** The closing price. */
  close: number;
  /** The trading volume. */
  volume: number;
}

/**
 * Parameters for fetching OHLCV data.
 */
export interface GetOhlcvParams {
  /** Time interval for OHLCV data (e.g., "1h", "1d"). Defaults to "1h". */
  interval?: string;
  /** Start time in ISO format. Defaults to 7 days ago. */
  start?: string;
  /** End time in ISO format. Defaults to now. */
  end?: string;
  /** Blockchain network (e.g., "ethereum", "bitcoin"). */
  chain?: string;
}

/**
 * Represents a single token swap event from /api/token/{tokenAddress}/swaps
 */
export interface Swap {
  /** Unique identifier for the swap. */
  id: number;
  /** The token contract address. */
  token: string;
  /** Type of swap, either "purchase" or "sale". */
  type: "purchase" | "sale";
  /** The address of the trader. */
  trader: string;
  /** The amount of HYPE token involved in the swap. */
  hype_amount: string;
  /** The amount of the primary token involved in the swap. */
  token_amount: string;
  /** The price at which the swap occurred. */
  price: string;
  /** Timestamp of the swap. */
  timestamp: number;
  /** Transaction hash of the swap. */
  tx_hash: string;
}

/**
 * Represents a latest swap event from /api/swaps/latest
 */
export interface LatestSwap {
  /** Transaction hash of the swap. */
  tx_hash: string;
  /** The token contract address. */
  token: string;
  /** Timestamp of the swap. */
  timestamp: number;
  /** The amount of the primary token involved in the swap. */
  amount: string;
  /** The amount of HYPE token involved in the swap. */
  hype_amount: string;
  /** The price at which the swap occurred. */
  price: string;
  /** The address of the trader. */
  trader: string;
  /** Type of swap, either "buy" or "sell". */
  swap_type: "buy" | "sell";
  /** Symbol of the token. */
  symbol: string;
  /** Name of the token. */
  name: string;
  /** Whether the token creator has sold any tokens. */
  has_creator_sold: boolean;
  /** Token image URI. */
  image_uri: string | null;
}

/**
 * Pagination details for token swap history.
 */
export interface TokenSwapPagination {
  /** The current page number. */
  currentPage: number;
  /** The total number of pages. */
  totalPages: number;
  /** The total number of swaps. */
  totalSwaps: number;
  /** The number of items per page. */
  limit: number;
}

/**
 * Response structure for token swap history.
 */
export interface TokenSwapsResponse {
  /** Pagination information. */
  pagination: TokenSwapPagination;
  /** An array of swap events. */
  swaps: Swap[];
}

/**
 * Parameters for fetching token swap history.
 */
export interface GetTokenSwapsParams {
  /** Page number for pagination. Defaults to 1. */
  page?: number;
  /** Number of items per page. Defaults to 20. */
  limit?: number;
  /** Filter by swap type ("purchase" or "sale"). */
  type?: "purchase" | "sale";
}

/**
 * Parameters for fetching latest swaps across all tokens.
 */
export interface GetLatestSwapsParams {
  /** Number of swaps to return. Defaults to 50, Max: 500. */
  limit?: number;
}

/**
 * Response structure for latest swaps endpoint.
 */
export type LatestSwapsResponse = LatestSwap[];

/**
 * Represents a single token burn event.
 */
export interface BurnEvent {
  /** Unique identifier for the burn event. */
  id: number;
  /** The token contract address. */
  token: string;
  /** The amount of token burned. */
  amount: string;
  /** Timestamp of the burn event. */
  timestamp: number;
  /** Transaction hash of the burn event. */
  tx_hash: string;
  /** Block number in which the burn occurred. */
  block_number: string;
}

/**
 * Pagination details for token burn history.
 */
export interface TokenBurnPagination {
  /** The current page number. */
  currentPage: number;
  /** The total number of pages. */
  totalPages: number;
  /** The total number of burn events. */
  totalBurns: number;
  /** The number of items per page. */
  limit: number;
}

/**
 * Response structure for token burn history.
 */
export interface TokenBurnsResponse {
  /** Pagination information. */
  pagination: TokenBurnPagination;
  /** An array of burn events. */
  burns: BurnEvent[];
}

/**
 * Parameters for fetching token burn history.
 */
export interface GetTokenBurnsParams {
  /** Page number for pagination. Defaults to 1. */
  page?: number;
  /** Number of items per page. Defaults to 20. */
  limit?: number;
}

/**
 * Represents a token holder's information. TODO: We have two holders types, differentiate them
 */
export interface Holder {
  /** The holder's wallet address. */
  address: string;
  /** The balance of the token held by the address. */
  balance: string;
  /** The USD value of the balance. */
  balance_usd: string;
  /** The percentage of total supply held by this address. */
  percentage: string;
}

/**
 * Information about the token when fetching holders.
 */
export interface TokenInfoForHolders {
  /** The token contract address. */
  address: string;
  /** The name of the token. */
  name: string;
  /** The symbol of the token. */
  symbol: string;
  /** The total supply of the token. */
  total_supply: string;
  /** The current price of the token in USD. */
  current_price_usd: string;
  /** The price of HYPE in USD (likely related to liquidity pairing). */
  hype_price_usd: string;
}

/**
 * Pagination details for token holders.
 */
export interface TokenHolderPagination {
  /** The current page number. */
  currentPage: number;
  /** The total number of pages. */
  totalPages: number;
  /** The total number of holders. */
  totalHolders: number;
  /** The number of items per page. */
  limit: number;
}

/**
 * Response structure for token holder balances.
 */
export interface TokenHoldersResponse {
  /** Information about the token. */
  token: TokenInfoForHolders;
  /** Pagination information. */
  pagination: TokenHolderPagination;
  /** An array of token holders. */
  holders: Holder[];
}

/**
 * Parameters for fetching token holder balances.
 */
export interface GetTokenHoldersParams {
  /** Page number for pagination. Defaults to 1. */
  page?: number;
  /** Number of items per page. Defaults to 20. */
  limit?: number;
  /** Minimum token balance to include (in token units). Defaults to "0". */
  min_balance?: string;
}

/**
 * Represents a generic error response from the API.
 */
export interface ApiErrorResponse {
  /** The error message. */
  error: string;
}

// Types for Get Token Details by Address (/api/token/{tokenAddress})

/**
 * Represents the last swap information for a token.
 */
export interface TokenLastSwap {
  /** Timestamp of the last swap. */
  timestamp: number;
  /** Price at which the last swap occurred. */
  price: string;
  /** Type of the last swap, e.g., "purchase" or "sale". */
  type: "purchase" | "sale";
  /** Transaction hash of the last swap. */
  tx_hash: string;
}

/**
 * Parameters for fetching a list of tokens with filtering and sorting options.
 */
export interface ListTokensParams {
  /** Page number for pagination. Defaults to 1. */
  page?: number;
  /** Number of items per page. Defaults to 20, Max: 100. */
  limit?: number;
  /** Field to sort by. Defaults to 'latest_activity'. */
  sort_by?:
    | "latest_activity"
    | "mcap"
    | "swaps_count"
    | "age"
    | "volume_24h"
    | "price_change_24h"
    | "trades_24h"
    | "holders_count";
  /** Sort order. Defaults to 'desc'. */
  sort_order?: "asc" | "desc";
  /** Minimum market cap in USD. Defaults to 0. */
  min_mcap?: number;
  /** Minimum 24h volume in USD. Defaults to 0. */
  min_volume_24h?: number;
  /** Minimum number of holders. Defaults to 0. */
  min_holders?: number;
  /** Minimum number of swaps. Defaults to 0. */
  min_swaps?: number;
  /** Maximum token age in days. Defaults to 365. */
  max_age_days?: number;
  /** Filter by bonding status. Defaults to all. */
  bonding_status?: "in_progress" | "bonded";
}

/**
 * Represents a token in the list response.
 */
export interface ListedToken {
  /** The contract address of the token. */
  address: string;
  /** Number of decimal places for the token. */
  decimals: number;
  /** The name of the token. */
  name: string;
  /** The symbol of the token. */
  symbol: string;
  /** Token metadata including social links and description. */
  metadata: TokenMetadata;
  /** Supply and reserves data. */
  supplyData: TokenSupplyData;
  /** The creator's address. */
  creator: string;
  /** Timestamp of token creation. */
  creationTimestamp: number;
  /** Current price information. */
  price: TokenPrice;
  /** Market cap information. */
  marketCap: TokenMarketCap;
  /** Liquidity information. */
  liquidity: TokenLiquidity;
  /** Total supply of the token. */
  totalSupply: string;
  /** Number of token holders. */
  holderCount: string;
  /** Time-based metrics. */
  timeframes: TokenTimeframes;
  /** Array of token holders (may be empty for list responses). */
  holders: TokenHolder[];
  /** Percentage held by top 10 holders. */
  top10HoldersPercentage: string;
  /** Whether the token has been bonded to a DEX. */
  isBonded: boolean;
  /** Whether the token is frozen. */
  isFrozen: boolean;
  /** DEX pair address if bonded. */
  pair: string | null;
  /** Timestamp of the last activity. */
  latestActivityTimestamp: number;
  /** Comprehensive financial data. */
  financials: TokenFinancials;
  /** Bonding curve information. */
  bonding: TokenBonding;
}

/**
 * Pagination details for token list response.
 */
export interface TokenListPagination {
  /** The current page number. */
  currentPage: number;
  /** The total number of pages. */
  totalPages: number;
  /** The total number of tokens. */
  totalTokens: number;
  /** The number of items per page. */
  limit: number;
}

/**
 * Response structure for the list tokens endpoint.
 */
export interface ListTokensResponse {
  /** Pagination information. */
  pagination: TokenListPagination;
  /** An array of tokens. */
  tokens: ListedToken[];
}

/**
 * Extended token information that combines both API and contract data.
 */
export interface ExtendedTokenInfo extends ListedToken {
  /** Initial configuration data from contract */
  initialConfig?: {
    initialHypeReserves: string;
    initialLiquidity: string;
    initialPrice: string;
    initialPurchaseAmount: string;
    initialTokenReserves: string;
  } | null;
  /** Social media and website links */
  social?: {
    discord?: string;
    telegram?: string;
    twitter?: string;
    website?: string;
  } | null;
  /** Token description */
  description?: string;
  /** Block number of token creation */
  blockNumber?: string;
  /** Transaction hash of token creation */
  transactionHash?: string;
}

/**
 * Extended response structure that includes both API and contract data.
 */
export interface ExtendedListTokensResponse {
  /** Pagination information */
  pagination: TokenListPagination;
  /** An array of extended token information */
  tokens: ExtendedTokenInfo[];
}

// --- Token Details API Types) --- TY Pars
export interface TokenHolder {
  address: string;
  balance: string;
  percentage: string;
  rank: string;
}

export interface TokenMetadata {
  name: string;
  symbol: string;
  discord?: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  image_uri?: string;
  description?: string;
  creationTimestamp?: { hex: string; type: string };
}

export interface TokenSupplyData {
  tokenReserves: string;
  hypeReserves: string;
  totalSupply: string;
  holders: string;
}

export interface TokenPrice {
  usd: string;
  hype: string;
}

export interface TokenMarketCap {
  usd: string;
  hype: string;
}

export interface TokenLiquidity {
  usd: string;
  hype: string;
}

export interface TokenTimeframes {
  "24h": {
    buys: number;
    sells: number;
    volume: number;
    priceChange: number;
  };
}

export interface TokenFinancials {
  price: TokenPrice;
  marketCap: TokenMarketCap;
  liquidity: TokenLiquidity;
  volume24h: {
    usd: string;
    hype: string;
  };
  priceChange24h: string;
  trades24h: {
    buys: number;
    sells: number;
    total: number;
  };
}

export interface TokenBonding {
  progress: string;
  hypeProgress: string;
  tokensSold: string;
  tokensSoldPercentage: string;
  hypeNeededFor100: string;
  currentHypeInPool: string;
  bondsAtMcap: string;
  isBonded: boolean;
  constants: {
    initialSupply: string;
    targetReserves: string;
    targetBought: string;
    minHypeReserves: string;
    maxHypeReserves: string;
    targetMcapHypeForBonding: string;
  };
}

/**
 * API response for GET /api/token/{tokenAddress}
 * This type should match the backend response structure for a single token details query.
 * Used for mapping to the frontend TokenDetails type.
 */
export interface TokenDetails {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  metadata: TokenMetadata;
  supplyData: TokenSupplyData;
  creator: string;
  creationTimestamp: number;
  price: TokenPrice;
  marketCap: TokenMarketCap;
  liquidity: TokenLiquidity;
  totalSupply: string;
  holderCount: string;
  timeframes: TokenTimeframes;
  holders: TokenHolder[];
  top10HoldersPercentage: string;
  isBonded: boolean;
  isFrozen: boolean;
  pair: string | null;
  latestActivityTimestamp: number;
  financials: TokenFinancials;
  bonding: TokenBonding;
}

export type GetTokenDetailsResponse = TokenDetails;

// Chat API Types - Server communication types
export interface ReplyMessage {
  id: number;
  username: string;
  message: string;
  tokenaddress: string;
}

export interface MessageResponse {
  id: number;
  message: string;
  username: string;
  tokenaddress: string;
  balance: string;
  balancepercent: number;
  createdAt: number;
  updatedAt: number;
  isConfirmed: boolean;
  replyTo?: {
    id: number;
    message: string;
    username: string;
    tokenaddress: string;
  } | null;
  reactions: {
    [emoji: string]: string[];
  };
  isReaction: boolean;
}

export interface MessageReaction {
  messageId: number;
  emoji: string;
  userAddress: string;
  action: "add" | "remove";
}

// Legacy alias for compatibility
export type ChatMessageResponse = MessageResponse;

export interface GetChatHistoryParams {
  limit?: number;
  page?: number;
  before?: string;
}

export interface GetChatHistoryResponse {
  messages: MessageResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

/**
 * Represents a search result token from /api/tokens/search
 */
export interface SearchToken {
  /** The contract address of the token. */
  address: string;
  /** The number of decimal places for the token. */
  decimals: number;
  /** The name of the token. */
  name: string;
  /** The symbol of the token. */
  symbol: string;
  /** URL to the token's image. */
  image_uri: string;
}

/**
 * Parameters for searching tokens.
 */
export interface SearchTokensParams {
  /** Search query (token name, symbol, or address). */
  query: string;
  /** Maximum number of results to return. Defaults to 10. */
  limit?: number;
}

/**
 * Response structure for token search endpoint.
 */
export interface SearchTokensResponse {
  /** An array of matching tokens. */
  tokens: SearchToken[];
}

// --- DexScreener API Types ---

/**
 * DexScreener API response structure for token pairs
 */
export interface DexScreenerPair {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  labels?: string[];
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd?: string;
  txns: {
    m5: { buys: number; sells: number };
    h1: { buys: number; sells: number };
    h6: { buys: number; sells: number };
    h24: { buys: number; sells: number };
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd?: number;
    base: number;
    quote: number;
  };
  fdv?: number;
  marketCap?: number;
  pairCreatedAt?: number;
  info?: {
    imageUrl?: string;
    websites?: { label: string; url: string }[];
    socials?: { type: string; url: string }[];
  };
}

/**
 * DexScreener API response structure
 */
export type DexScreenerResponse = DexScreenerPair[];

/**
 * Comprehensive token details from DexScreener for bonded tokens
 */
export interface BondedTokenDetails {
  address: string;
  symbol: string;
  name: string;
  price: {
    usd: number;
    native: number;
  };
  marketCap: {
    usd: number;
    fdv?: number;
  };
  liquidity: {
    usd: number;
    base: number;
    quote: number;
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  trades: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  metadata: {
    imageUrl?: string;
    websites?: { label: string; url: string }[];
    socials?: { type: string; url: string }[];
  };
  pairAddress: string;
  dexId: string;
  url: string;
  pairCreatedAt?: number;
  labels?: string[];
}

/**
 * Parameters for fetching multiple bonded token details
 */
export interface GetBondedTokensParams {
  addresses: string[];
}

/**
 * Response for fetching multiple bonded token details
 */
export interface GetBondedTokensResponse {
  tokens: BondedTokenDetails[];
}

/**
 * Wallet API Types
 */

/**
 * Represents a single portfolio token holding in wallet information.
 */
export interface WalletPortfolioToken {
  /** Token contract address. */
  token: string;
  /** Token name. */
  token_name: string;
  /** Token symbol. */
  token_symbol: string;
  /** Token balance as string. */
  balance: string;
  /** Value in HYPE as string. */
  value_hype: string;
}

/**
 * Response structure for wallet information endpoint.
 */
export interface WalletInfoResponse {
  /** Wallet address. */
  address: string;
  /** Total trading volume as string. */
  total_volume: string;
  /** Total number of trades. */
  total_trades: number;
  /** Number of buy trades. */
  buy_count: number;
  /** Number of sell trades. */
  sell_count: number;
  /** Timestamp of first trade. */
  first_trade: number;
  /** Timestamp of last trade. */
  last_trade: number;
  /** Portfolio holdings array. */
  portfolio: WalletPortfolioToken[];
}

/**
 * Represents a single wallet swap with token metadata.
 */
export interface WalletSwap {
  /** Token contract address. */
  token: string;
  /** Type of swap, either "purchase" or "sale". */
  type: "purchase" | "sale";
  /** The amount of HYPE token involved in the swap. */
  hype_amount: string;
  /** The amount of the primary token involved in the swap. */
  token_amount: string;
  /** The price at which the swap occurred. */
  price: string;
  /** Timestamp of the swap. */
  timestamp: number;
  /** Transaction hash of the swap. */
  tx_hash: string;
  /** Token name. */
  token_name: string;
  /** Token symbol. */
  token_symbol: string;
  /** Additional token metadata. */
  metadata: Record<string, unknown>;
}

/**
 * Pagination details for wallet swaps.
 */
export interface WalletSwapsPagination {
  /** The current page number. */
  currentPage: number;
  /** The total number of pages. */
  totalPages: number;
  /** The total number of swaps. */
  totalSwaps: number;
  /** The number of items per page. */
  limit: number;
}

/**
 * Response structure for wallet swaps endpoint.
 */
export interface WalletSwapsResponse {
  /** Pagination information. */
  pagination: WalletSwapsPagination;
  /** An array of wallet swap events. */
  swaps: WalletSwap[];
}

/**
 * Parameters for fetching wallet swaps.
 */
export interface GetWalletSwapsParams {
  /** Optional token address to filter swaps. */
  token?: string;
  /** Page number for pagination. Defaults to 1. */
  page?: number;
  /** Number of items per page. Defaults to 20. */
  limit?: number;
  /** Sort order by timestamp. Defaults to "desc". */
  sort_order?: "asc" | "desc";
}
