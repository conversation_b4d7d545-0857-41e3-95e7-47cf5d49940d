import { ApiClient, ApiClientOptions, QueryParams } from "../client";
import { WalletInfoResponse, WalletSwapsResponse, GetWalletSwapsParams } from "../types";

/**
 * Service for interacting with wallet-related endpoints of the LiquidLaunch API.
 */
export class WalletService {
  private apiClient: ApiClient;

  /**
   * Creates an instance of WalletService.
   * @param {ApiClientOptions | ApiClient} options - Options to configure the ApiClient or an existing ApiClient instance.
   */
  constructor(options: ApiClientOptions | ApiClient) {
    if (options instanceof ApiClient) {
      this.apiClient = options;
    } else {
      this.apiClient = new ApiClient(options);
    }
  }

  /**
   * Get wallet information including trading statistics.
   * @param {string} walletAddress - Wallet address to get information for.
   * @returns {Promise<WalletInfoResponse>} A promise that resolves with the wallet information.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getWalletInfo(walletAddress: string): Promise<WalletInfoResponse> {
    if (!walletAddress) {
      throw new Error("Wallet address is required to fetch wallet information.");
    }
    const path = `/wallet/${walletAddress.toLowerCase()}`;
    return this.apiClient.get<WalletInfoResponse>(path);
  }

  /**
   * Get wallet swaps with optional token filter.
   * @param {string} walletAddress - Wallet address to get swaps for.
   * @param {GetWalletSwapsParams} [params] - Optional parameters for pagination and filtering.
   * @returns {Promise<WalletSwapsResponse>} A promise that resolves with the wallet swap history.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getWalletSwaps(walletAddress: string, params?: GetWalletSwapsParams): Promise<WalletSwapsResponse> {
    if (!walletAddress) {
      throw new Error("Wallet address is required to fetch wallet swaps.");
    }
    const path = `/wallet/${walletAddress.toLowerCase()}/swaps`;
    return this.apiClient.get<WalletSwapsResponse>(path, params as QueryParams);
  }
}
