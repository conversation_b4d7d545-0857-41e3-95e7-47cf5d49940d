import { ApiClient, ApiClientOptions, QueryParams } from "../client";
import {
  TokenSwapsResponse,
  GetTokenSwapsParams,
  TokenBurnsResponse,
  GetTokenBurnsParams,
  TokenHoldersResponse,
  GetTokenHoldersParams,
  GetTokenDetailsResponse,
  ListTokensParams,
  ListTokensResponse,
  LatestSwapsResponse,
  GetLatestSwapsParams,
  SearchTokensParams,
  SearchTokensResponse,
} from "../types";

/**
 * Service for interacting with token-related endpoints of the LiquidLaunch API.
 */
export class TokenService {
  private apiClient: ApiClient;

  /**
   * Creates an instance of TokenService.
   * @param {ApiClientOptions | ApiClient} options - Options to configure the ApiClient or an existing ApiClient instance.
   */
  constructor(options: ApiClientOptions | ApiClient) {
    if (options instanceof ApiClient) {
      this.apiClient = options;
    } else {
      this.apiClient = new ApiClient(options);
    }
  }

  /**
   * Get paginated swap history for a token.
   * @param {string} tokenAddress - Token contract address.
   * @param {GetTokenSwapsParams} [params] - Optional parameters for pagination and filtering.
   * @returns {Promise<TokenSwapsResponse>} A promise that resolves with the token swap history.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getTokenSwaps(tokenAddress: string, params?: GetTokenSwapsParams): Promise<TokenSwapsResponse> {
    if (!tokenAddress) {
      throw new Error("Token address is required to fetch swaps.");
    }
    const path = `/token/${tokenAddress.toLowerCase()}/swaps`;
    return this.apiClient.get<TokenSwapsResponse>(path, params as QueryParams);
  }

  /**
   * Get paginated token burn events.
   * @param {string} tokenAddress - Token contract address.
   * @param {GetTokenBurnsParams} [params] - Optional parameters for pagination.
   * @returns {Promise<TokenBurnsResponse>} A promise that resolves with the token burn history.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getTokenBurns(tokenAddress: string, params?: GetTokenBurnsParams): Promise<TokenBurnsResponse> {
    if (!tokenAddress) {
      throw new Error("Token address is required to fetch burn events.");
    }
    const path = `/token/${tokenAddress.toLowerCase()}/burns`;
    return this.apiClient.get<TokenBurnsResponse>(path, params as QueryParams);
  }

  /**
   * Get current token holder balances with USD values.
   * @param {string} tokenAddress - Token contract address.
   * @param {GetTokenHoldersParams} [params] - Optional parameters for pagination and filtering.
   * @returns {Promise<TokenHoldersResponse>} A promise that resolves with the token holder balances.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getTokenHolders(tokenAddress: string, params?: GetTokenHoldersParams): Promise<TokenHoldersResponse> {
    if (!tokenAddress) {
      throw new Error("Token address is required to fetch holders.");
    }
    const path = `/token/${tokenAddress.toLowerCase()}/holders`;
    return this.apiClient.get<TokenHoldersResponse>(path, params as QueryParams);
  }

  /**
   * Fetches details for a specific token.
   * @param {string} tokenAddress - The contract address of the token.
   * @returns {Promise<GetTokenDetailsResponse>} A promise that resolves with the token details.
   * @throws {ApiError} If the API returns an error or if the token address is not provided.
   */
  public async getTokenDetails(tokenAddress: string): Promise<GetTokenDetailsResponse> {
    if (!tokenAddress) {
      throw new Error("Token address is required to fetch token details.");
    }
    const lowercaseAddress = tokenAddress.toLowerCase();
    // The endpoint is /api/token/{tokenAddress}, so the path for the client should be /token/{tokenAddress}
    // assuming the apiClient has a base URL like /api
    const path = `/token/${lowercaseAddress}`;
    return this.apiClient.get<GetTokenDetailsResponse>(path);
  }

  /**
   * Fetches a list of tokens with filtering and sorting options.
   * @param {ListTokensParams} params - Parameters for filtering and sorting tokens.
   * @returns {Promise<ListTokensResponse>} A promise that resolves with the list of tokens and pagination info.
   * @throws {ApiError} If the API returns an error.
   */
  public async listTokens(params?: ListTokensParams): Promise<ListTokensResponse> {
    return this.apiClient.get<ListTokensResponse>("/tokens", params as QueryParams);
  }

  /**
   * Get latest swaps across all tokens with significant volume.
   * @param {GetLatestSwapsParams} [params] - Optional parameters for limiting results.
   * @returns {Promise<LatestSwapsResponse>} A promise that resolves with the latest swaps.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async getLatestSwaps(params?: GetLatestSwapsParams): Promise<LatestSwapsResponse> {
    const path = "/swaps/latest";
    return this.apiClient.get<LatestSwapsResponse>(path, params as QueryParams);
  }

  /**
   * Search tokens by name, symbol, or address.
   * @param {SearchTokensParams} params - Parameters for searching tokens.
   * @returns {Promise<SearchTokensResponse>} A promise that resolves with the search results.
   * @throws {ApiError} If the API returns an error.
   * @see https://donkey-api.liquidlaunch.app/documentation/static/index.html
   */
  public async searchTokens(params: SearchTokensParams): Promise<SearchTokensResponse> {
    if (!params.query) {
      throw new Error("Search query is required.");
    }
    const path = "/tokens/search";
    return this.apiClient.get<SearchTokensResponse>(path, params as unknown as QueryParams);
  }
}
