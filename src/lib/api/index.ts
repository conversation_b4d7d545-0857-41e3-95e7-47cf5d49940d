// API Client
export { ApiClient } from "./client";
export type { ApiClientOptions } from "./client";

// Services
export { OhlcvService } from "./services/OhlcvService";
export { TokenService } from "./services/TokenService";
export { ChatService } from "./services/ChatService";
export { WalletService } from "./services/WalletService";

// Types
export * from "./types";

// Utilities / Errors
export { ApiError } from "./utils";

/**
 * Main class to access all LiquidLaunch API services.
 * This provides a convenient way to initialize and use all available services.
 */
import { OhlcvService } from "./services/OhlcvService";
import { TokenService } from "./services/TokenService";
import { ChatService } from "./services/ChatService";
import { WalletService } from "./services/WalletService";
import { ApiClient, ApiClientOptions } from "./client";

export class LiquidLaunchApiService {
  public readonly apiClient: ApiClient;
  public readonly ohlcv: OhlcvService;
  public readonly token: TokenService;
  public readonly chat: ChatService;
  public readonly wallet: WalletService;

  /**
   * Creates an instance of LiquidLaunchApiService.
   * @param {ApiClientOptions} [options={}] - Configuration options for the underlying ApiClient.
   */
  constructor(options: ApiClientOptions = {}) {
    this.apiClient = new ApiClient(options);
    this.ohlcv = new OhlcvService(this.apiClient);
    this.token = new TokenService(this.apiClient);
    this.chat = new ChatService();
    this.wallet = new WalletService(this.apiClient);
  }
}
