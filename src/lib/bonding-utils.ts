import { getNativeHypePrice } from "@/services/dexscreenerService";

// Constants for bonding calculation
const MIN_HYPE_RESERVES = 300e18; // 0% progress (300 HYPE virtual)
const MAX_HYPE_RESERVES = 855e18; // 100% progress (300 virtual + 555 real = 855 total)
const HYPE_RANGE = MAX_HYPE_RESERVES - MIN_HYPE_RESERVES;

// Target market cap for bonding completion
export const TARGET_MCAP_HYPE_FOR_BONDING = 2439; // 2439 HYPE for bonding completion

export const calculateBondingProgress = (currentHypeReserves: number | bigint) => {
  // Convert values to numbers to ensure proper calculation
  const currentReserves = typeof currentHypeReserves === "bigint" ? Number(currentHypeReserves) : currentHypeReserves;

  // If below the minimum threshold, return 0%
  if (currentReserves <= MIN_HYPE_RESERVES) {
    return 0;
  }

  // Calculate progress based on position in the HYPE range
  const progress = ((currentReserves - MIN_HYPE_RESERVES) / HYPE_RANGE) * 100;

  // Round to nearest whole number and cap at 100%
  const roundedProgress = Math.round(progress);

  // Ensure the progress is capped at 100%
  return Math.min(roundedProgress, 100);
};

/**
 * Calculate bonding market cap in USD
 * @param hypePriceInUsd - The current price of HYPE in USD
 * @returns The bonding target market cap in USD
 */
export const calculateBondingMcap = async (): Promise<number> => {
  const hypePriceInUsd = await getNativeHypePrice();
  if (hypePriceInUsd === undefined || hypePriceInUsd === null) {
    return 0;
  }

  return TARGET_MCAP_HYPE_FOR_BONDING * Number(hypePriceInUsd);
};
