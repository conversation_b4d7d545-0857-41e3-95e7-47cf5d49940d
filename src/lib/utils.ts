import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { TOKENS } from "@/lib/constants";
import { checksumAddress, formatUnits } from "viem";
import { formatDistanceToNowStrict } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a numeric value with K, M, B suffixes
 * @param value - The numeric value to format
 * @param decimals - Number of decimal places to show
 * @param threshold - Minimum value to start using suffixes
 */
export function formatTokenValue(
  value: number | string,
  decimals: number = TOKENS.DISPLAY_DECIMALS,
  threshold: number = 1000,
): string {
  // If value is a string, remove any commas before parsing
  const num = typeof value === "string" ? parseFloat(value.replace(/,/g, "")) : value;

  if (isNaN(num)) return "0";

  // Show exact value below threshold
  if (Math.abs(num) < threshold) {
    return num.toFixed(decimals).replace(/\.?0+$/, "");
  }

  // Format with suffixes for larger values
  const absValue = Math.abs(num);
  const sign = num < 0 ? "-" : "";

  if (absValue >= 1_000_000_000) {
    return sign + (absValue / 1_000_000_000).toFixed(decimals).replace(/\.?0+$/, "") + "B";
  } else if (absValue >= 1_000_000) {
    return sign + (absValue / 1_000_000).toFixed(decimals).replace(/\.?0+$/, "") + "M";
  } else if (absValue >= 1_000) {
    return sign + (absValue / 1_000).toFixed(decimals).replace(/\.?0+$/, "") + "K";
  }

  return num.toFixed(decimals).replace(/\.?0+$/, "");
}

/**
 * Formats a number to 2 decimal places for display purposes
 * @param value - The number to format
 * @returns Formatted string with 2 decimal places
 */
export const formatDisplayValue = (value: number | string): string => {
  if (typeof value === "string") {
    value = parseFloat(value);
  }
  if (isNaN(value)) return "0.00";
  return value.toFixed(2);
};

/**
 * Formats a number to a specific number of decimal places for exchange rates.
 * @param value - The numeric value to format
 * @param decimals - Number of decimal places to show
 * @returns Formatted string with the specified decimals
 */
export function formatRateValue(value: number | string, decimals: number = 8): string {
  const num = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(num)) return "0";

  // Always use toFixed for rates, removing trailing zeros after the decimal point if they exist
  return num.toFixed(decimals).replace(/(\.\d*?[1-9])0+$|\.0+$/, "$1");
}

/**
 * Formats a token balance string (raw balance from contract reads) into a readable format.
 * Uses token decimals to convert raw balance to human-readable format.
 * @param value - The raw balance string (e.g., "1000000000000000000").
 * @param decimals - The number of decimals the token uses (e.g., 18).
 * @param symbol - Optional token symbol (not used for formatting logic).
 * @returns Formatted balance string for display.
 */
export function formatTokenBalance(
  value: string | undefined | null,
  decimals: number | undefined,
  symbol?: string,
): string {
  // Input validation
  if (value === undefined || value === null || decimals === undefined || decimals === null) {
    console.warn("[formatTokenBalance] Received invalid input:", { value, decimals, symbol });
    return "0.00";
  }

  if (value === "0") return "0.00"; // Handle zero raw balance

  try {
    const formattedBalanceString = formatUnits(BigInt(value), decimals);
    const num = parseFloat(formattedBalanceString);

    if (isNaN(num)) {
      console.warn("[formatTokenBalance] Result of formatUnits is NaN:", { value, decimals });
      return "0.00";
    }

    if (num === 0) return "0.00"; // Handle parsed zero

    // Standard formatting for all tokens
    return num.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  } catch (error) {
    console.error("[formatTokenBalance] Error formatting balance:", { value, decimals, symbol, error });
    return "0.00"; // Fallback on error
  }
}

/**
 * Shortens an Ethereum address for display.
 * @param address The full Ethereum address.
 * @param isMobile Optional flag to use shorter format (0x..abc).
 * @returns The shortened address string.
 */
export function shortenAddress(address: string | undefined, isMobile: boolean = false): string {
  if (!address) return "";

  if (isMobile) {
    const prefix = address.substring(0, 2 + 2); // 0x + 2 chars
    const suffix = address.substring(address.length - 3);
    return `${prefix}...${suffix}`;
  } else {
    const chars = 4; // Default for desktop
    const prefix = address.substring(0, chars + 2); // Include '0x'
    const suffix = address.substring(address.length - chars);
    return `${prefix}...${suffix}`;
  }
}

// Define formatCurrency here as it's needed by this client component
export function formatCurrency(value: number): string {
  // Handle cases where value might not be a valid number
  if (typeof value !== "number" || isNaN(value)) {
    return "$0.00"; // Or some other placeholder
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
}

/**
 * Formats a number using Intl.NumberFormat (basic locale string).
 * Useful for general number display like counts.
 * @param value The number to format.
 * @returns Formatted number string.
 */
export function formatNumber(value: number | undefined | null): string {
  if (value === null || value === undefined || isNaN(value)) {
    return "0";
  }
  return value.toLocaleString(); // Basic locale-specific formatting
}

/**
 * Formats a date string to "MMM D, YYYY" (e.g., "Jan 1, 2024").
 * @param dateString The date string to format.
 * @returns Formatted date string or original string if parsing fails.
 */
export function formatDisplayDate(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  } catch (e) {
    console.error("[formatDisplayDate] Error parsing date:", dateString, e);
    return dateString; // Fallback to original string
  }
}

/**
 * Formats a date string to "MMM D" (e.g., "Jan 1").
 * Used for chart axes.
 * @param dateString The date string to format.
 * @returns Formatted date string or original string if parsing fails.
 */
export function formatAxisDate(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString(undefined, {
      month: "numeric",
      day: "numeric",
    });
  } catch (e) {
    console.error("[formatAxisDate] Error parsing date:", dateString, e);
    return dateString; // Fallback to original string
  }
}
/**
 * Gets the base URL for the application.
 * Prefers Vercel deployment URL if available, otherwise defaults to localhost.
 * @returns The base URL string.
 */
export function getBaseNextApiUrl(): string {
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }
  // Default to localhost for development
  return `http://localhost:${process.env.PORT || 3000}`;
}

/**
 * Formats a timestamp into a "time ago" string.
 * e.g., "5 minutes ago", "about 2 hours ago", "yesterday"
 * @param timestamp The Unix timestamp in seconds.
 * @returns A string representing the time elapsed since the timestamp.
 */
export function formatTimeAgo(timestamp?: number): string {
  if (timestamp === undefined || timestamp === null) {
    return "N/A";
  }
  try {
    const date = new Date(timestamp * 1000);
    // formatDistanceToNowStrict provides more precise output without "about"
    // and allows adding a suffix.
    return formatDistanceToNowStrict(date, { addSuffix: true });
  } catch (error) {
    console.error("[formatTimeAgo] Error formatting date:", { timestamp, error });
    // Fallback to a simple date string if date-fns fails for some reason
    try {
      return new Date(timestamp * 1000).toLocaleDateString();
    } catch (e) {
      console.error("[formatTimeAgo] Error formatting date:", { timestamp, e });
      return "Invalid date";
    }
  }
}

/**
 * Formats an Ethereum address for display in tables.
 * @param address The full Ethereum address.
 * @returns The formatted address string.
 */
export function formatAddressForTable(address: string) {
  const checksummedAddress = checksumAddress(address as `0x${string}`);
  return `${checksummedAddress.substring(checksummedAddress.length - 6)}`;
}

/**
 * Sanitizes a URL to ensure it uses a safe protocol (http/https).
 * @param url The URL to sanitize
 * @param base Optional base URL for resolving relative URLs
 * @returns The sanitized URL if valid, undefined otherwise
 */
export function safeUrl(url: string, base: string = "https://dummy"): string | undefined {
  try {
    const parsedUrl = new URL(url, base);
    if (parsedUrl.protocol === "http:" || parsedUrl.protocol === "https:") {
      return parsedUrl.href;
    }
  } catch (error) {
    console.error("[safeUrl] Error parsing URL:", { url, base, error });
  }
  return undefined;
}

/**
 * Copies text to clipboard and provides feedback state management.
 * @param text The text to copy to clipboard
 * @returns A promise that resolves to true if successful, false otherwise
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("[copyToClipboard] Failed to copy text:", error);
    return false;
  }
}
