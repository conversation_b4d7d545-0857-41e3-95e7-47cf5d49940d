import { ChatMessage, ChatUser } from "@/types/chat";
import { MessageResponse } from "@/lib/api/types";
import { CHAT_CONFIG } from "@/config/chat";
import DOMPurify from "dompurify";

// Format timestamp for display
export const formatMessageTime = (timestamp: Date): string => {
  const now = new Date();
  const messageDate = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return "now";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m`;
  } else if (diffInMinutes < 1440) {
    // 24 hours
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `${days}d`;
  }
};

// Format full timestamp for tooltips
export const formatFullTimestamp = (timestamp: Date): string => {
  return new Date(timestamp).toLocaleString();
};

// Truncate address for display
export const truncateAddress = (address: string, startChars = 6, endChars = 4): string => {
  if (address.length <= startChars + endChars) {
    return address;
  }
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

// Get display name for user
export const getUserDisplayName = (user: ChatUser): string => {
  return user.username || truncateAddress(user.address);
};

// Get display name for user by ID from messages
export const getUserDisplayNameById = (userId: string, messages: ChatMessage[]): string => {
  // Try to find user from recent messages
  const userFromMessages = messages.find((msg) => msg.userId === userId)?.user;
  if (userFromMessages) {
    return getUserDisplayName(userFromMessages);
  }
  // Fallback to truncated userId
  return truncateAddress(userId);
};

// Get avatar seed from user address
export const getAvatarSeed = (address: string): string => {
  // Use the address as seed for consistent avatar generation
  return address.toLowerCase();
};

// Get user avatar seed (with fallback)
export const getUserAvatarSeed = (user: ChatUser): string => {
  return getAvatarSeed(user.address);
};

// Parse message content for mentions and links
export const parseMessageContent = (
  content: string,
): {
  text: string;
  mentions: string[];
  tokenLinks: string[];
  urls: string[];
} => {
  const mentions: string[] = [];
  const tokenLinks: string[] = [];
  const urls: string[] = [];

  // Extract @mentions (addresses)
  const mentionRegex = /@(0x[a-fA-F0-9]{40})/g;
  let match;
  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1]);
  }

  // Extract token links (token addresses)
  const tokenRegex = /\b(0x[a-fA-F0-9]{40})\b/g;
  while ((match = tokenRegex.exec(content)) !== null) {
    if (!mentions.includes(match[1])) {
      // Don't double-count mentions
      tokenLinks.push(match[1]);
    }
  }

  // Extract URLs with improved regex pattern
  // More robust pattern that validates domain structure and common URL formats
  const urlRegex = /https?:\/\/(?:[-\w.])+(?:\.[a-zA-Z]{2,})+(?:\/[^\s]*)?/g;
  while ((match = urlRegex.exec(content)) !== null) {
    const url = match[0];
    // Validate the URL before adding it
    if (isValidUrl(url)) {
      urls.push(url);
    }
  }

  return {
    text: content,
    mentions,
    tokenLinks,
    urls,
  };
};

// Validate URL format and structure
const isValidUrl = (urlString: string): boolean => {
  try {
    const url = new URL(urlString);

    // Check for valid protocol
    if (!["http:", "https:"].includes(url.protocol)) {
      return false;
    }

    // Check for valid hostname (must have at least one dot and valid TLD)
    const hostname = url.hostname.toLowerCase();
    if (!hostname || !hostname.includes(".")) {
      return false;
    }

    // Check for valid TLD (at least 2 characters)
    const parts = hostname.split(".");
    const tld = parts[parts.length - 1];
    if (!tld || tld.length < 2 || !/^[a-z]+$/.test(tld)) {
      return false;
    }

    // Check for valid domain characters
    if (!/^[a-z0-9.-]+$/.test(hostname)) {
      return false;
    }

    // Reject URLs with suspicious patterns
    if (hostname.startsWith(".") || hostname.endsWith(".") || hostname.includes("..")) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
};

// Parse message content into segments for rendering
export const parseMessageSegments = (
  content: string,
): Array<{
  type: "text" | "mention" | "tokenLink" | "url";
  content: string;
  data?: { url?: string; address?: string };
}> => {
  const segments: Array<{
    type: "text" | "mention" | "tokenLink" | "url";
    content: string;
    data?: { url?: string; address?: string };
  }> = [];

  // Combined regex to match all special content types
  // Updated URL pattern to be more robust
  const combinedRegex =
    /(https?:\/\/(?:[-\w.])+(?:\.[a-zA-Z]{2,})+(?:\/[^\s]*)?)|(@0x[a-fA-F0-9]{40})|(\b0x[a-fA-F0-9]{40}\b)/g;

  let lastIndex = 0;
  let match;

  while ((match = combinedRegex.exec(content)) !== null) {
    // Add text before the match
    if (match.index > lastIndex) {
      const textContent = content.slice(lastIndex, match.index);
      if (textContent) {
        segments.push({
          type: "text",
          content: textContent,
        });
      }
    }

    // Determine the type of match
    if (match[1]) {
      // URL match - validate before adding
      const url = match[1];
      if (isValidUrl(url)) {
        segments.push({
          type: "url",
          content: url,
          data: { url },
        });
      } else {
        // If invalid URL, treat as text
        segments.push({
          type: "text",
          content: url,
        });
      }
    } else if (match[2]) {
      // Mention match (@0x...)
      const address = match[2].slice(1); // Remove @ symbol
      segments.push({
        type: "mention",
        content: match[2],
        data: { address },
      });
    } else if (match[3]) {
      // Token link match (0x... without @)
      segments.push({
        type: "tokenLink",
        content: match[3],
        data: { address: match[3] },
      });
    }

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < content.length) {
    const textContent = content.slice(lastIndex);
    if (textContent) {
      segments.push({
        type: "text",
        content: textContent,
      });
    }
  }

  return segments;
};

// Highlight mentions and token links in message content
export const highlightMessageContent = (content: string): string => {
  // Sanitize content first to prevent XSS
  let highlighted = DOMPurify.sanitize(content);

  // Highlight @mentions
  highlighted = highlighted.replace(/@(0x[a-fA-F0-9]{40})/g, '<span class="text-primary font-medium">@$1</span>');

  // Highlight token links
  highlighted = highlighted.replace(
    /\b(0x[a-fA-F0-9]{40})\b/g,
    '<span class="text-accent hover:underline cursor-pointer">$1</span>',
  );

  return highlighted;
};

// Check if message mentions a specific user
export const messageContainsMention = (message: ChatMessage, userAddress: string): boolean => {
  return message.metadata?.userMentions?.includes(userAddress) || false;
};

// Group messages by date
export const groupMessagesByDate = (messages: ChatMessage[]): Record<string, ChatMessage[]> => {
  const groups: Record<string, ChatMessage[]> = {};

  messages.forEach((message) => {
    const date = new Date(message.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
  });

  return groups;
};

// Check if two messages should be grouped together (same user, close in time)
export const shouldGroupMessages = (currentMessage: ChatMessage, previousMessage?: ChatMessage): boolean => {
  if (!previousMessage) return false;
  if (currentMessage.userId !== previousMessage.userId) return false;
  if (currentMessage.type !== previousMessage.type) return false;

  const timeDiff = new Date(currentMessage.timestamp).getTime() - new Date(previousMessage.timestamp).getTime();
  const maxGroupTime = 5 * 60 * 1000; // 5 minutes

  return timeDiff <= maxGroupTime;
};

// Generate a temporary message ID
export const generateTempMessageId = (): string => {
  return `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Check if message contains Telegram bot links
export const containsTelegramBotLink = (content: string): boolean => {
  const telegramBotRegex = /(t\.me|telegram\.me|telegram\.org)\/[^?\s]*bot[^?\s]*/i;
  return telegramBotRegex.test(content);
};

// Check if message or its reply contains Telegram bot links
export const messageContainsTelegramBotLink = (message: ChatMessage): boolean => {
  // Check the message content
  if (containsTelegramBotLink(message.content)) {
    return true;
  }

  // Check if it's a reply to a message with Telegram bot links
  if (message.replyTo && containsTelegramBotLink(message.replyTo.message)) {
    return true;
  }

  return false;
};

// Validate message content
export const validateMessageContent = (
  content: string,
): {
  isValid: boolean;
  error?: string;
} => {
  if (!content.trim()) {
    return { isValid: false, error: "Message cannot be empty" };
  }

  if (content.length > 500) {
    return { isValid: false, error: "Message too long (max 500 characters)" };
  }

  // Block messages with Telegram bot links
  if (containsTelegramBotLink(content)) {
    return { isValid: false, error: "Telegram bot links are not allowed" };
  }

  return { isValid: true };
};

// Extract emoji from message content
export const extractEmojis = (content: string): string[] => {
  // Simplified emoji detection - can be enhanced with a proper emoji library
  const emojiRegex = /[\u2600-\u27BF]|[\uD83C][\uDF00-\uDFFF]|[\uD83D][\uDC00-\uDE4F]|[\uD83D][\uDE80-\uDEFF]/g;
  return content.match(emojiRegex) || [];
};

// Format participant count
export const formatParticipantCount = (count: number): string => {
  if (count < 1000) {
    return count.toString();
  } else if (count < 1000000) {
    return `${(count / 1000).toFixed(1)}k`;
  } else {
    return `${(count / 1000000).toFixed(1)}m`;
  }
};

// Get room display name
export const getRoomDisplayName = (roomId: string, roomName?: string): string => {
  if (roomName) return roomName;

  if (roomId === CHAT_CONFIG.GLOBAL_ROOM_ID) {
    return "Global";
  }

  if (roomId.startsWith("0x") && roomId.length === 42) {
    return truncateAddress(roomId);
  }

  return roomId;
};

// Sort messages by timestamp
export const sortMessagesByTimestamp = (messages: ChatMessage[]): ChatMessage[] => {
  return [...messages].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

// Filter messages by search term
export const filterMessages = (messages: ChatMessage[], searchTerm: string): ChatMessage[] => {
  if (!searchTerm.trim()) return messages;

  const term = searchTerm.toLowerCase();
  return messages.filter(
    (message) =>
      message.content.toLowerCase().includes(term) || getUserDisplayName(message.user).toLowerCase().includes(term),
  );
};

// GIF validation utilities
export const isValidGifUrl = (url: string): boolean => {
  try {
    const parsedUrl = new URL(url);
    return (
      parsedUrl.hostname === "giphy.com" ||
      parsedUrl.hostname === "media.giphy.com" ||
      parsedUrl.hostname === "tenor.com" ||
      parsedUrl.hostname === "media.tenor.com"
    );
  } catch {
    return false;
  }
};

export const extractGifUrl = (text: string): string | null => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const matches = text.match(urlRegex);
  if (!matches) return null;

  for (const url of matches) {
    if (isValidGifUrl(url)) {
      return url;
    }
  }
  return null;
};

/**
 * Converts API chat message response to internal ChatMessage format
 */
export const convertApiMessageToInternal = (apiMessage: MessageResponse): ChatMessage => {
  // The API returns: { id, username, message, tokenaddress, balance, balancepercent, reactions, replyTo, ... }
  const userAddress = apiMessage.username; // username field contains the wallet address
  const userId = userAddress;

  // Detect if message is a GIF
  const isGif = isValidGifUrl(apiMessage.message);

  // Handle room ID - for global rooms, tokenaddress might be undefined or null
  let roomId = "";
  if (apiMessage.tokenaddress) {
    roomId = apiMessage.tokenaddress.toLowerCase();
  } else {
    // If no tokenaddress, assume it's global room
    roomId = CHAT_CONFIG.GLOBAL_ROOM_ID;
  }

  return {
    id: apiMessage.id.toString(),
    content: apiMessage.message || "",
    userId: userId,
    user: {
      id: userId,
      address: userAddress,
      username: undefined, // No separate username, just use address
      avatar: undefined,
      isOnline: true, // Default to online since we don't have this info
      tokenBalance: apiMessage.balance,
      balancePercent: apiMessage.balancepercent,
    },
    roomId: roomId,
    timestamp: new Date(apiMessage.createdAt),
    type: isGif ? "gif" : "text",
    replyTo: apiMessage.replyTo
      ? {
          id: apiMessage.replyTo.id.toString(),
          username: apiMessage.replyTo.username,
          message: apiMessage.replyTo.message,
          tokenaddress: apiMessage.replyTo.tokenaddress
            ? apiMessage.replyTo.tokenaddress.toLowerCase()
            : CHAT_CONFIG.GLOBAL_ROOM_ID,
        }
      : null,
    reactions: apiMessage.reactions || {},
    isConfirmed: apiMessage.isConfirmed ?? true,
    isReaction: apiMessage.isReaction ?? false,
    balance: apiMessage.balance || "0",
    balancePercent: apiMessage.balancepercent || 0,
    metadata: isGif ? { gifUrl: apiMessage.message } : undefined,
  };
};

/**
 * Converts array of API chat messages to internal format and filters out Telegram bot links
 */
export const convertApiMessagesToInternal = (apiMessages: MessageResponse[]): ChatMessage[] => {
  return apiMessages.map(convertApiMessageToInternal).filter((message) => {
    const hasTelegramBot = messageContainsTelegramBotLink(message);
    if (hasTelegramBot) {
      console.warn("Filtered out API message with Telegram bot link:", message.content);
    }
    return !hasTelegramBot;
  });
};

/**
 * Validates that the user is authenticated and the wallet address matches the session
 */
export const validateChatAuthentication = (
  sessionStatus: string,
  session: { address?: string } | null,
  walletAddress?: string,
): { isValid: boolean; error?: string } => {
  if (sessionStatus !== "authenticated") {
    return { isValid: false, error: "Authentication required" };
  }

  const sessionAddress = session?.address;
  if (!sessionAddress) {
    return { isValid: false, error: "No session address found" };
  }

  if (!walletAddress) {
    return { isValid: false, error: "No wallet address provided" };
  }

  if (walletAddress.toLowerCase() !== sessionAddress.toLowerCase()) {
    return { isValid: false, error: "Wallet address does not match authenticated session" };
  }

  return { isValid: true };
};
