/**
 * Token Constants
 */
export const TOKENS = {
  // Addresses
  WHYPE_ADDRESS: "0x5555555555555555555555555555555555555555" as const,
  NATIVE_HYPE_ADDRESS: "0x000000000000000000000000000000000000dEaD" as const,

  // Identifiers
  NATIVE_HYPE_IDENTIFIER: "Native HYPE" as const,

  // Display Decimals
  DISPLAY_DECIMALS: 2 as const,

  // Native Token Decimals
  NATIVE_HYPE_DECIMALS: 18 as const,
};

export const CONTRACTS = {
  LAUNCHPAD_ADDRESS: "0xDEC3540f5BA6f2aa3764583A9c29501FeB020030" as const,
  MULTICALL_ADDRESS: "0xcA11bde05977b3631167028862bE2a173976CA11" as const,
};

// ‼️WARNING: ONLY EXPOSE API_BASE_URL to SERVER SIDE ROUTES‼️

export const API_BASE_URL = process.env.LIQD_AG_API_URL;
export const API_LIQUID_LAUNCH_URL = process.env.API_LIQUID_LAUNCH_URL;
export const LIQUIDLAUNCH_API_BASE_URL = "https://donkey-api.liquidlaunch.app/api";
export const SIGNATURE_API_URL = "/api/swaps/sign";

export const NATIVE_HYPE_GAS_RESERVE = "0.01"; // Reserve 0.01 HYPE for gas when clicking Max

export const HYPER_EVM_CHAIN = {
  id: 999,
  name: "HyperEVM",
  nativeCurrency: {
    decimals: 18,
    name: "HYPE",
    symbol: "HYPE",
  },
  rpcUrls: {
    default: { http: [process.env.NEXT_PUBLIC_RPC_URL!] },
  },
  blockExplorers: {
    default: { name: "Hyperscan", url: process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL! },
  },
};
