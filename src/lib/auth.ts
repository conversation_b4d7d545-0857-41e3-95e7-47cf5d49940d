import { NextAuthOptions, type User } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { SiweMessage } from "siwe";
import { cookies } from "next/headers";

declare module "next-auth" {
  interface Session {
    address?: string;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Ethereum",
      credentials: {
        message: {
          label: "Message",
          type: "text",
        },
        signature: {
          label: "Signature",
          type: "text",
        },
      },
      async authorize(credentials): Promise<User | null> {
        if (!credentials?.message || !credentials?.signature) {
          console.error("[NextAuth Authorize] Missing credentials");
          return null;
        }

        const cookieStore = await cookies();
        const nonceCookie = cookieStore.get("siwe-nonce");
        const expectedNonce = nonceCookie?.value;

        if (nonceCookie) {
          cookieStore.set("siwe-nonce", "", {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            maxAge: -1,
            path: "/",
            sameSite: "lax",
          });
        }

        if (!expectedNonce) {
          console.error("[NextAuth Authorize] Missing expected nonce cookie.");
          return null;
        }

        try {
          const siwe = new SiweMessage(credentials.message);
          const nextAuthUrl = new URL(process.env.NEXTAUTH_URL!);

          console.log("[NextAuth Authorize] Verifying SIWE signature...");
          const result = await siwe.verify({
            signature: credentials.signature,
            domain: nextAuthUrl.host,
            nonce: expectedNonce,
          });

          if (!result.success) {
            console.error(
              `[NextAuth Authorize] SIWE verification failed for ${siwe.address}:`,
              result.error?.type || "Unknown error",
              `Expected Nonce: ${expectedNonce}, Message Nonce: ${siwe.nonce}`,
            );
            return null;
          }

          console.log(`[NextAuth Authorize] SIWE verification successful for ${siwe.address}`);
          return {
            id: siwe.address,
          };
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
          console.error("[NextAuth Authorize] Error during SIWE verification:", errorMessage);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async session({ session, token }) {
      if (token?.sub) {
        session.address = token.sub;
      } else {
        console.warn("[NextAuth Callback] Token sub (address) missing in session callback.");
        session.address = undefined;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.sub = user.id;
      }
      return token;
    },
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production" ? `__Secure-next-auth.session-token` : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        maxAge: 30 * 24 * 60 * 60, // 30 days to match session maxAge
      },
    },
  },
  pages: {
    signIn: "/",
  },
  debug: process.env.NODE_ENV === "development",
};
