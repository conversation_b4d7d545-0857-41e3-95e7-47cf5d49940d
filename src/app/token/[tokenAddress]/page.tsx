import TokenDataTabs from "./SwapComponents/TokenDataTabs";
import SwapWidget from "./SwapComponents/SwapWidget";
import TokenChart from "./SwapComponents/TokenChart";
import TokenHeader from "./SwapComponents/TokenHeader";
import TokenStoreLoader from "./SwapComponents/TokenStoreLoader";
import { TokenDetails } from "@/lib/api/types";
import { getTokenDetails } from "./actions/details.actions";
import { ChatWidget } from "@/components/chat/ChatWidget";
import MobileSwapButton from "@/components/mobile/MobileSwapButton";
import { DexScreenerChart } from "./SwapComponents/DexScreenerChart";

// Define a specific props interface for the page
interface TokenPageProps {
  params: Promise<{ tokenAddress: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

async function getTokenDetailsData(tokenAddress: string): Promise<TokenDetails | null> {
  try {
    const apiData = await getTokenDetails(tokenAddress);
    return apiData;
  } catch (error) {
    console.error("Error fetching token details for address:", tokenAddress, error);
    return null;
  }
}

export default async function TokenPage({ params: paramsPromise }: TokenPageProps) {
  const params = await paramsPromise;
  // const searchParams = searchParamsPromise ? await searchParamsPromise : {}; // if needed later
  const { tokenAddress } = params;
  const tokenDetails = await getTokenDetailsData(tokenAddress);
  const isBonded = tokenDetails?.isBonded;

  return (
    <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-start">
      {/* Load token into store for real-time updates */}
      <TokenStoreLoader tokenDetails={tokenDetails} />

      {/* Left Column: Unified Header + Chart Card, then Data Tabs */}
      <div className="flex flex-col gap-4 min-w-0">
        <div className="bg-backgroundDark rounded-lg overflow-hidden">
          <TokenHeader tokenDetails={tokenDetails} className="rounded-t-lg border-b border-border bg-background-dark" />
          {isBonded ? (
            <DexScreenerChart tokenDetails={tokenDetails} />
          ) : (
            <TokenChart tokenDetails={tokenDetails} tokenAddress={tokenAddress} className="rounded-t-none border-0" />
          )}
        </div>
        {!isBonded && <TokenDataTabs tokenDetails={tokenDetails} tokenAddress={tokenAddress} />}
      </div>

      {/* Right Column: Swap Widget - Hidden on mobile -- Mobile component uses <SwapWidget /> as well */}
      <SwapWidget
        tokenDetails={tokenDetails}
        tokenAddress={tokenAddress as `0x${string}`}
        className="mb-12 sm:mb-0 hidden md:block"
      />

      {/* Mobile Swap Button - Only visible on mobile */}
      <MobileSwapButton tokenDetails={tokenDetails} tokenAddress={tokenAddress as `0x${string}`} />

      {/* Chat Widget - Unified for both mobile and desktop with bubble when minimized */}
      <div className="fixed bottom-5 sm:bottom-2 sm:right-2 right-4 z-50">
        <ChatWidget roomId={tokenAddress.toLowerCase()} tokenDetails={tokenDetails} defaultMinimized={false} />
      </div>
    </div>
  );
}
