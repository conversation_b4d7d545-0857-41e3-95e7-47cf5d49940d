"use server";

import { LiquidLaunchApiService, ApiError } from "@/lib/api";
import { GetTokenDetailsResponse } from "@/lib/api/types";

const apiService = new LiquidLaunchApiService();

/**
 * Server Action to fetch a single token's details for a given token address.
 *
 * @param tokenAddress The contract address of the token.
 * @returns A promise that resolves with the token details.
 * @throws {ApiError} when the API call returns a handled error.
 * @throws {Error} for all other unexpected issues.
 */
export async function getTokenDetails(tokenAddress: string): Promise<GetTokenDetailsResponse> {
  const lowercasedAddress = tokenAddress.toLowerCase();
  console.log("[getTokenDetails] Received (original/lowercased):", {
    tokenAddress,
    lowercasedAddress,
  });
  try {
    const tokenDetailsData = await apiService.token.getTokenDetails(lowercasedAddress);
    return tokenDetailsData;
  } catch (error) {
    console.error("[getTokenDetails] Error caught:", error);
    if (error instanceof ApiError) {
      // Re-throw the original ApiError to preserve status code and specific error response
      throw error;
    }
    // Log and re-throw a generic error for unexpected issues
    console.error("Unexpected error in getTokenDetails (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while fetching token details.");
  }
}
