"use client";

import { TokenDetails } from "@/lib/api/types";

interface DexScreenerChartProps {
  tokenDetails: TokenDetails | null;
}

export const DexScreenerChart = ({ tokenDetails }: DexScreenerChartProps) => {
  if (!tokenDetails?.address) {
    return (
      <div className="w-full h-96 bg-card rounded-lg flex items-center justify-center">
        <p className="text-muted-foreground">Unable to load chart - token address not available</p>
      </div>
    );
  }

  // Construct the DexScreener embed URL
  const embedUrl = `https://dexscreener.com/hyperevm/${tokenDetails.address}?embed=1&loadChartSettings=0&info=0&chartLeftToolbar=0&chartTheme=dark&theme=dark&chartStyle=1&chartType=usd&interval=1`;

  return (
    <div className="w-full">
      <style jsx>{`
        #dexscreener-embed {
          position: relative;
          width: 100%;
          padding-bottom: 100%;
          overflow: hidden;
        }
        @media (min-width: 1400px) {
          #dexscreener-embed {
            padding-bottom: 65%;
          }
        }
        #dexscreener-embed iframe {
          position: absolute;
          width: 100%;
          height: 110%; /* Make iframe slightly taller */
          top: 0;
          left: 0;
          border: 0;
          clip-path: inset(0 0 40px 0); /* Clip bottom 40px to hide branding */
        }
        @media (max-width: 768px) {
          #dexscreener-embed iframe {
            height: 105%; /* Less aggressive clipping on mobile */
            clip-path: inset(0 0 30px 0); /* Clip bottom 30px on mobile */
          }
        }
      `}</style>
      <div id="dexscreener-embed">
        <iframe src={embedUrl} title={`DexScreener Chart for ${tokenDetails.symbol || "Token"}`} loading="lazy" />
      </div>
    </div>
  );
};
