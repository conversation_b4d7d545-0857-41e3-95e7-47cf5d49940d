"use client";

import { useEffect } from "react";
import { useTokensStore } from "@/stores/tokensStore";
import { TokenDetails } from "@/lib/api/types";

interface TokenStoreLoaderProps {
  tokenDetails: TokenDetails | null;
}

/**
 * Client-side component that loads the current token into the tokens store
 * for real-time WebSocket updates. This ensures the token page can receive
 * live updates and always has the freshest data from the API.
 */
export default function TokenStoreLoader({ tokenDetails }: TokenStoreLoaderProps) {
  const loadSingleToken = useTokensStore((state) => state.loadSingleToken);

  useEffect(() => {
    if (tokenDetails) {
      // Always load/update the token with fresh API data
      // This ensures we have the most recent data even if the user
      // navigated from a stale tokens list page
      loadSingleToken(tokenDetails);
      console.log(`[TokenStoreLoader] Loaded/updated token ${tokenDetails.symbol} with fresh API data`);
    }
  }, [tokenDetails, loadSingleToken]);

  // This component doesn't render anything
  return null;
}
