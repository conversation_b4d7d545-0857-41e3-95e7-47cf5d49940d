"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import React from "react";

interface BubbleMapsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tokenAddress?: string; // For future dynamic support
}

export default function BubbleMapsModal({ open, onOpenChange, tokenAddress }: BubbleMapsModalProps) {
  const url = tokenAddress ? `https://bubble.acc8.dev/token/${tokenAddress}` : "https://bubble.acc8.dev";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-[90vw] sm:max-w-[80vw] h-[80vh] max-h-[80vh] p-0 overflow-hidden flex">
        <VisuallyHidden>
          <DialogTitle>Bubble Maps</DialogTitle>
        </VisuallyHidden>
        <iframe src={url} width="100%" height="100%" style={{ border: "none" }} loading="lazy" title="Bubble Maps" />
      </DialogContent>
    </Dialog>
  );
}
