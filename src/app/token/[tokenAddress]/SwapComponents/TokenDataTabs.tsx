"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { List, Users, User, TrendingUp, Flame } from "lucide-react";
import { useState } from "react";
import SwapsTable from "./TokenDataTables/SwapsTable";
import HoldersTable from "./TokenDataTables/HoldersTable";
import TopTradersTable from "./TokenDataTables/TopTradersTable";
import { BurnsTable } from "./TokenDataTables/BurnsTable";
import { PersonalTable } from "./TokenDataTables/PersonalTable";
import { TokenDetails } from "@/lib/api/types";

interface TokenDataTabsProps {
  tokenDetails: TokenDetails | null;
  tokenAddress: string;
}

export default function TokenDataTabs({ tokenDetails, tokenAddress }: TokenDataTabsProps) {
  const [activeTab, setActiveTab] = useState("transactions");

  return (
    <Tabs defaultValue="transactions" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="flex w-full justify-start gap-2 overflow-x-auto pb-2">
        <TabsTrigger value="transactions" className="flex items-center gap-2">
          <List className="w-4 h-4" /> Transactions
        </TabsTrigger>
        <TabsTrigger value="holders" className="flex items-center gap-2">
          <Users className="w-4 h-4" /> Holders
        </TabsTrigger>
        <TabsTrigger value="personal" className="flex items-center gap-2">
          <User className="w-4 h-4" /> Personal
        </TabsTrigger>
        <TabsTrigger value="toptraders" className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" /> Top Traders
        </TabsTrigger>
        <TabsTrigger value="burns" className="flex items-center gap-2">
          <Flame className="w-4 h-4" /> Burns
        </TabsTrigger>
      </TabsList>
      <TabsContent value="transactions">
        <SwapsTable symbol={tokenDetails?.symbol || "-"} tokenAddress={tokenAddress} tokenDetails={tokenDetails} />
      </TabsContent>
      <TabsContent value="holders">
        <HoldersTable tokenAddress={tokenAddress} tokenDetails={tokenDetails} isActive={activeTab === "holders"} />
      </TabsContent>
      <TabsContent value="personal">
        <PersonalTable
          tokenAddress={tokenAddress}
          symbol={tokenDetails?.symbol || "Token"}
          tokenDetails={tokenDetails}
          isActive={activeTab === "personal"}
        />
      </TabsContent>
      <TabsContent value="toptraders">
        <TopTradersTable />
      </TabsContent>
      <TabsContent value="burns">
        <BurnsTable tokenAddress={tokenAddress} />
      </TabsContent>
    </Tabs>
  );
}
