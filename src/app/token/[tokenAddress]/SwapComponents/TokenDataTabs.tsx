"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { List, Users, User, TrendingUp, Flame, Globe2 } from "lucide-react";
import { useState } from "react";
import SwapsTable from "./TokenDataTables/SwapsTable";
import HoldersTable from "./TokenDataTables/HoldersTable";
import TopTradersTable from "./TokenDataTables/TopTradersTable";
import { BurnsTable } from "./TokenDataTables/BurnsTable";
import { PersonalTable } from "./TokenDataTables/PersonalTable";
import { TokenDetails } from "@/lib/api/types";
import React from "react";
import BubbleMapsModal from "./BubbleMapsModal";

interface TokenDataTabsProps {
  tokenDetails: TokenDetails | null;
  tokenAddress: string;
}

export default function TokenDataTabs({ tokenDetails, tokenAddress }: TokenDataTabsProps) {
  const [activeTab, setActiveTab] = useState("transactions");
  const [bubbleMapsOpen, setBubbleMapsOpen] = useState(false);
  const prevTabRef = React.useRef<string>("transactions");

  const handleTabChange = (tab: string) => {
    if (tab === "bubblemaps") {
      setBubbleMapsOpen(true);
      // Don't actually switch the tab
      return;
    }
    prevTabRef.current = tab;
    setActiveTab(tab);
  };

  // When modal closes, reset tab to previous
  React.useEffect(() => {
    if (!bubbleMapsOpen && activeTab === "bubblemaps") {
      setActiveTab(prevTabRef.current);
    }
  }, [bubbleMapsOpen, activeTab]);

  return (
    <>
      <Tabs defaultValue="transactions" value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="flex w-full justify-start gap-2 overflow-x-auto pb-2">
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <List className="w-4 h-4" /> Transactions
          </TabsTrigger>
          <TabsTrigger value="holders" className="flex items-center gap-2">
            <Users className="w-4 h-4" /> Holders
          </TabsTrigger>
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="w-4 h-4" /> Personal
          </TabsTrigger>
          <TabsTrigger value="toptraders" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" /> Top Traders
          </TabsTrigger>
          <TabsTrigger value="burns" className="flex items-center gap-2">
            <Flame className="w-4 h-4" /> Burns
          </TabsTrigger>
          <TabsTrigger
            value="bubblemaps"
            className={"flex items-center gap-2" + (bubbleMapsOpen ? " data-[state=active]" : "")}
          >
            <Globe2 className="w-4 h-4" /> Bubble Maps
          </TabsTrigger>
        </TabsList>
        <TabsContent value="transactions">
          <SwapsTable symbol={tokenDetails?.symbol || "-"} tokenAddress={tokenAddress} tokenDetails={tokenDetails} />
        </TabsContent>
        <TabsContent value="holders">
          <HoldersTable tokenAddress={tokenAddress} tokenDetails={tokenDetails} isActive={activeTab === "holders"} />
        </TabsContent>
        <TabsContent value="personal">
          <PersonalTable
            tokenAddress={tokenAddress}
            symbol={tokenDetails?.symbol || "Token"}
            tokenDetails={tokenDetails}
            isActive={activeTab === "personal"}
          />
        </TabsContent>
        <TabsContent value="toptraders">
          <TopTradersTable />
        </TabsContent>
        <TabsContent value="burns">
          <BurnsTable tokenAddress={tokenAddress} />
        </TabsContent>
        {/* No TabsContent for Bubble Maps */}
      </Tabs>
      <BubbleMapsModal open={bubbleMapsOpen} onOpenChange={setBubbleMapsOpen} tokenAddress={tokenAddress} />
    </>
  );
}
