"use client";

import React, { useState } from "react";
import { shortenAddress, copyToClipboard } from "@/lib/utils";
import { formatTimeAgo } from "@/components/swaps/swapsUtils";
import { Toolt<PERSON>, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { Copy, Check } from "lucide-react";
import Image from "next/image";
import { TokenDetails } from "@/lib/api/types";
import { SocialLinks } from "@/components/shared/SocialLinks";
import { CurrencyDisplay } from "@/components/shared/CurrencyDisplay";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { useTokensStore } from "@/stores/tokensStore";
import { useBondedTokenDetails } from "@/hooks/contracts/useBondedTokenDetails";

interface TokenHeaderProps {
  tokenDetails: TokenDetails | null;
  className?: string;
}

// Stat block component
function Stat({ label, value }: { label: string; value: React.ReactNode }) {
  return (
    <div className="flex flex-col items-start min-w-[60px] lg:min-w-[70px] flex-shrink-0">
      <span className="text-xs text-muted-foreground leading-none">{label}</span>
      <span className="block font-mono text-sm leading-tight">{value}</span>
    </div>
  );
}

export default function TokenHeader({ tokenDetails, className }: TokenHeaderProps) {
  const { formatCurrency } = useCurrencyFormatter();
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  // Memoize the token address to prevent selector recreation
  const tokenAddress = React.useMemo(() => tokenDetails?.address.toLowerCase() || null, [tokenDetails?.address]);

  // Get dynamic token data from the store with proper reactivity
  const storeToken = useTokensStore((state) => {
    if (!tokenAddress) return null;
    return state.tokens[tokenAddress] || null;
  });

  // Check if token is bonded to determine which token details to use
  const isBonded = storeToken?.isBonded ?? tokenDetails?.isBonded ?? false;

  // Fetch bonded token details from DexScreener for bonded tokens
  const {
    bondedTokenDetails: dexScreenerTokenDetails,
    isLoading: isDexScreenerLoading,
    error: dexScreenerError,
  } = useBondedTokenDetails({
    tokenAddress: tokenAddress || "",
    enabled: isBonded && !!tokenAddress,
    originalTokenDetails: storeToken || tokenDetails, // Pass original token details for creator address
  });

  // Smart data selection based on bonding status and DexScreener availability
  const activeTokenDetails = React.useMemo(() => {
    if (!isBonded) {
      // For non-bonded tokens, use store data or API data
      console.log("[TokenHeader] Using non-bonded token data for", tokenAddress);
      return storeToken || tokenDetails;
    }

    // For bonded tokens, prioritize DexScreener data
    if (dexScreenerTokenDetails) {
      // DexScreener data is available - use it
      console.log("[TokenHeader] Using DexScreener data for bonded token", tokenAddress);
      return dexScreenerTokenDetails;
    }

    if (isDexScreenerLoading) {
      // Still loading DexScreener data - use original data temporarily
      console.log("[TokenHeader] DexScreener loading, using fallback data for bonded token", tokenAddress);
      return storeToken || tokenDetails;
    }

    if (dexScreenerError) {
      // DexScreener failed - fall back to original data
      console.warn("[TokenHeader] DexScreener failed for bonded token, using fallback data:", dexScreenerError);
      return storeToken || tokenDetails;
    }

    // Default fallback
    console.log("[TokenHeader] Using default fallback data for bonded token", tokenAddress);
    return storeToken || tokenDetails;
  }, [
    isBonded,
    dexScreenerTokenDetails,
    isDexScreenerLoading,
    dexScreenerError,
    storeToken,
    tokenDetails,
    tokenAddress,
  ]);

  // Handle copying addresses
  const handleCopyAddress = async (address: string) => {
    const success = await copyToClipboard(address);
    if (success) {
      setCopiedAddress(address);
      setTimeout(() => setCopiedAddress(null), 2000);
    }
  };

  // Show loading state while DexScreener data is being fetched for bonded tokens
  if (isBonded && isDexScreenerLoading && !dexScreenerTokenDetails && !dexScreenerError) {
    return (
      <div className={`w-full h-[64px] flex items-center px-3 animate-pulse ${className || ""}`}>
        <div className="flex items-center gap-x-2">
          <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full bg-muted animate-pulse" />
          <div className="flex flex-col gap-1">
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
            <div className="h-3 w-16 bg-muted animate-pulse rounded" />
          </div>
        </div>
      </div>
    );
  }

  // Log DexScreener errors but don't block rendering
  if (isBonded && dexScreenerError) {
    console.warn("[TokenHeader] DexScreener error:", dexScreenerError);
  }

  if (!activeTokenDetails) {
    return <div className={`w-full h-[64px] flex items-center px-3 animate-pulse ${className || ""}`} />;
  }

  const { metadata, symbol, name, creator, creationTimestamp, address, marketCap, liquidity, bonding, financials } =
    activeTokenDetails;

  const image_uri = metadata?.image_uri;

  // Use financials data for more accurate information, fallback to direct values
  const mcUsd = financials?.marketCap?.usd || marketCap?.usd;
  const mcHype = financials?.marketCap?.hype || marketCap?.hype;
  const liquidityUsd = financials?.liquidity?.usd || liquidity?.usd;
  const liquidityHype = financials?.liquidity?.hype || liquidity?.hype;

  return (
    <div className={`w-full overflow-x-auto overflow-y-hidden ${className || ""}`}>
      {/* Mobile: Horizontal scrollable container */}
      <div className="flex items-center px-3 py-2.5 gap-x-4 lg:gap-x-6 min-w-max">
        {/* Left: Image + Symbol/Name/Time */}
        <div className="flex items-center gap-x-2 min-w-0 flex-shrink-0">
          {image_uri ? (
            <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full border border-border bg-card overflow-hidden flex-shrink-0">
              <Image src={image_uri} alt={symbol} width={44} height={44} className="w-full h-full object-cover" />
            </div>
          ) : (
            <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full bg-muted flex-shrink-0" />
          )}
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-x-1 lg:gap-x-2">
              <span className="font-semibold text-base lg:text-lg truncate">{symbol}</span>
              {/* Social Links */}
              <SocialLinks
                className="mr-1 relative top-0.5"
                website={metadata?.website}
                twitter={metadata?.twitter}
                telegram={metadata?.telegram}
                discord={metadata?.discord}
                variant="header"
              />
            </div>
            {name && (
              <div className="flex items-center gap-x-1 lg:gap-x-2">
                <span className="text-muted-foreground text-xs truncate max-w-[120px] lg:max-w-none">{name}</span>
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatTimeAgo(Number(creationTimestamp))}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Divider */}
        <div className="h-8 lg:h-10 border-l border-border flex-shrink-0" />

        {/* Stats */}
        <div className="flex gap-x-3 lg:gap-x-6 flex-shrink-0">
          <Stat label="MCap" value={<CurrencyDisplay {...formatCurrency({ usd: mcUsd, hype: mcHype })} />} />
          {!activeTokenDetails?.isBonded && (
            <Stat
              label="Bonds at"
              value={
                <CurrencyDisplay
                  {...formatCurrency({
                    usd: bonding?.bondsAtMcap,
                    hype: bonding?.constants?.targetMcapHypeForBonding,
                  })}
                />
              }
            />
          )}
          <Stat
            label="Volume"
            value={
              <CurrencyDisplay
                {...formatCurrency({
                  usd: financials?.volume24h?.usd,
                  hype: financials?.volume24h?.hype,
                })}
              />
            }
          />
          <Stat
            label="Liquidity"
            value={<CurrencyDisplay {...formatCurrency({ usd: liquidityUsd, hype: liquidityHype })} />}
          />
        </div>

        {/* Bonding Progress */}
        <div className="flex flex-col items-center gap-1 min-w-[100px] lg:min-w-[120px] flex-shrink-0">
          <span className="text-xs text-muted-foreground self-start">
            {activeTokenDetails?.isBonded ? "Bonded 🎉" : "Bonding"}
          </span>
          <div className="flex items-center gap-2 w-full">
            <Progress value={bonding?.progress ? parseFloat(bonding.progress) : 0} className="w-16 lg:w-20 h-2" />
            <span className="text-xs font-medium text-primary min-w-[35px]">
              {bonding?.progress ? `${parseFloat(bonding.progress).toFixed(1)}%` : "0%"}
            </span>
          </div>
        </div>

        {/* Spacer - only visible on lg+ screens */}
        <div className="hidden lg:block flex-1" />

        {/* Creator Address */}
        <div className="flex gap-x-3 lg:gap-x-4 flex-shrink-0">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={() => handleCopyAddress(creator)}
                className="text-xs select-none flex flex-col items-center gap-y-1 text-muted-foreground font-mono cursor-pointer hover:text-primary transition-colors"
              >
                <div className="flex items-center gap-1">
                  <span>Dev</span>
                  {copiedAddress === creator ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </div>
                <span className="text-primary hover:text-primary/80 transition-colors">
                  {shortenAddress(creator, false)}
                </span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <span className="font-mono">Copy dev address</span>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={() => handleCopyAddress(address)}
                className="text-xs select-none flex flex-col items-center gap-y-1 text-muted-foreground font-mono cursor-pointer hover:text-primary transition-colors"
              >
                <div className="flex items-center gap-1">
                  <span>CA</span>
                  {copiedAddress === address ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </div>
                <span className="text-primary hover:text-primary/80 transition-colors">
                  {shortenAddress(address, false)}
                </span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <span className="font-mono">Copy contract address</span>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}
