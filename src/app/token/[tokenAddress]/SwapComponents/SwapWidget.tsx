"use client";

import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAccount } from "wagmi";
import { type Address, type Hex } from "viem";
import { cn, formatTokenValue } from "@/lib/utils";
import { parseUnits, formatUnits } from "viem";
import { IMAGES } from "@/lib/constants-images";
import { Info, Settings as SettingsIcon, ExternalLink, X } from "lucide-react";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";

import { useDebounce } from "@/hooks/utils/useDebounce";
import { useTokenInfo } from "@/hooks/contracts/useTokenInfo";
import { useBalanceStore } from "@/stores/balanceStore";
import { useSwapEstimates } from "@/hooks/contracts/useSwapEstimates";
import { useSwapHandler } from "@/hooks/contracts/useSwapHandler";
import { useBondedTokenSwap } from "@/hooks/contracts/useBondedTokenSwap";
import { useConnectModal } from "@rainbow-me/rainbowkit";

import { CONTRACTS, TOKENS } from "@/lib/constants";
import { TokenDetails } from "@/lib/api/types";
interface SwapWidgetProps {
  tokenDetails?: TokenDetails | null;
  tokenAddress: Address;
  className?: string;
}

const QUICK_AMOUNTS = [0.25, 0.5, 0.75, 1];
const SLIPPAGE_PRESETS = [0.1, 0.5, 1.0, 5.0, 20.0];
const HYPE_DECIMALS = TOKENS.NATIVE_HYPE_DECIMALS;
const HYPE_SYMBOL = "HYPE";

export default function SwapWidget({ tokenDetails, tokenAddress, className }: SwapWidgetProps) {
  const { address: userAddress, isConnected } = useAccount();
  const { openConnectModal } = useConnectModal();

  const [activeTab, setActiveTab] = useState<"buy" | "sell">("buy");
  const [inputAmountRaw, setInputAmountRaw] = useState("");
  const [outputAmountString, setOutputAmountString] = useState("");

  const [settingsOpen, setSettingsOpen] = useState(false);
  const [slippageSetting, setSlippageSetting] = useState(0.5);
  const [customSlippageInput, setCustomSlippageInput] = useState<string>("0.5");

  const [hasInitiatedTx, setHasInitiatedTx] = useState(false);
  const displayedErrorRef = useRef<string | null>(null);

  const debouncedInputAmountRaw = useDebounce(inputAmountRaw, 500);
  const isBuy = activeTab === "buy";

  const {
    decimals: targetTokenDecimalsData,
    symbol: targetTokenSymbolData,
    isLoading: isLoadingTargetInfo,
    error: tokenInfoError,
  } = useTokenInfo(tokenAddress);

  const targetTokenDecimals = targetTokenDecimalsData ?? 18;
  const displayTargetSymbol = targetTokenSymbolData || tokenDetails?.symbol || "Token";

  const { updateSpecificTokenBalances, ownedTokens, isBalancesLoading } = useBalanceStore();

  // Memoize token addresses to prevent infinite re-renders
  const tokenAddresses = useMemo(() => [TOKENS.NATIVE_HYPE_IDENTIFIER, tokenAddress], [tokenAddress]);

  // Refetch function for after swaps
  const refetchSpecificBalances = useCallback(async () => {
    if (isConnected && userAddress && tokenAddresses.length > 0) {
      await updateSpecificTokenBalances(tokenAddresses);
    }
  }, [isConnected, userAddress, tokenAddresses, updateSpecificTokenBalances]);

  // Fetch specific balances when component mounts or dependencies change
  useEffect(() => {
    refetchSpecificBalances();
  }, [refetchSpecificBalances]);

  const hypeTokenEntry = ownedTokens.find(
    (t) => t.address.toLowerCase() === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase() || t.symbol === HYPE_SYMBOL
  );
  console.log("[SwapWidget] Found HYPE token entry:", hypeTokenEntry);
  const rawHypeBalanceFromStore = hypeTokenEntry?.balance;
  console.log("[SwapWidget] Raw HYPE balance:", rawHypeBalanceFromStore);
  const hypeBalanceBigInt = rawHypeBalanceFromStore ? BigInt(rawHypeBalanceFromStore) : undefined;
  console.log("[SwapWidget] Derived hypeBalanceBigInt:", hypeBalanceBigInt?.toString());

  const rawTargetTokenBalanceFromStore = ownedTokens.find(
    (t) => t.address.toLowerCase() === tokenAddress.toLowerCase()
  )?.balance;
  const targetTokenBalanceBigInt = rawTargetTokenBalanceFromStore ? BigInt(rawTargetTokenBalanceFromStore) : undefined;

  const parsedDebouncedInputAmount = parseUnits(debouncedInputAmountRaw, isBuy ? HYPE_DECIMALS : targetTokenDecimals);

  // Check if token is bonded to determine which swap method to use
  const isBonded = tokenDetails?.isBonded ?? false;

  const {
    estimate: estimatedOutputAmountBigInt,
    isLoading: isLoadingEstimation,
    error: estimationError,
  } = useSwapEstimates({
    launchpadAddress: CONTRACTS.LAUNCHPAD_ADDRESS,
    tokenAddress: tokenAddress,
    parsedAmount: parsedDebouncedInputAmount,
    isBuy: isBuy,
  });

  const handleSuccessfulTransactionToast = useCallback(
    (txHash: Hex) => {
      setInputAmountRaw("");
      setHasInitiatedTx(false);

      // Update balances for both tokens involved in the swap
      const tokensToUpdate = [
        TOKENS.NATIVE_HYPE_IDENTIFIER, // HYPE token
        tokenAddress, // The target token being swapped
      ].filter(Boolean); // Filter out any undefined values

      // Store handles retry logic automatically
      updateSpecificTokenBalances(tokensToUpdate).catch((error) => {
        console.error("[SwapWidget] Failed to update token balances after swap:", error);
      });

      toast.success(
        <div className="flex flex-col items-start">
          <span>Transaction successful!</span>
          <a
            href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/tx/${txHash}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs underline hover:text-opacity-80 mt-1 text-primary-foreground/80 flex items-center"
          >
            View on Explorer <ExternalLink className="h-3 w-3 inline-block ml-1" />
          </a>
        </div>,
        {
          id: `tx-success-${txHash}`,
          duration: 5000,
        }
      );
    },
    [tokenAddress, updateSpecificTokenBalances]
  );

  // Use different swap handlers based on token bonding status
  const bondingCurveSwap = useSwapHandler({
    inputAmountRaw: debouncedInputAmountRaw,
    isBuy,
    tokenAddress,
    hypeTokenDecimals: HYPE_DECIMALS,
    targetTokenDecimals: targetTokenDecimals,
    hypeBalance: hypeBalanceBigInt,
    targetTokenBalance: targetTokenBalanceBigInt,
    displayTargetSymbol,
    onTransactionSuccess: handleSuccessfulTransactionToast,
  });

  const dexSwap = useBondedTokenSwap({
    inputAmountRaw: debouncedInputAmountRaw,
    isBuy,
    tokenAddress,
    hypeTokenDecimals: HYPE_DECIMALS,
    targetTokenDecimals: targetTokenDecimals,
    hypeBalance: hypeBalanceBigInt,
    targetTokenBalance: targetTokenBalanceBigInt, // This will be overridden by bondedTokenBalance internally
    displayTargetSymbol,
    slippagePercent: slippageSetting,
    onTransactionSuccess: handleSuccessfulTransactionToast,
    isBonded,
  });

  // Select the appropriate swap handler based on bonding status
  const {
    handleSwap,
    isProcessing,
    isConfirming,
    uiError: swapHandlerErrorText,
    isLoadingButton: isSwapButtonLoadingHook,
    swapButtonText,
    isAmountValid,
    clearError,
  } = isBonded ? dexSwap : bondingCurveSwap;

  // For bonded tokens, use the direct chain balance instead of balance store
  const effectiveTargetTokenBalance = isBonded ? dexSwap.bondedTokenBalance : targetTokenBalanceBigInt;

  useEffect(() => {
    let message: string | null = null;
    if (swapHandlerErrorText) message = swapHandlerErrorText;
    else if (!isBonded && estimationError) message = `Estimation error: ${estimationError.message}`;
    else if (tokenInfoError) message = `Token info error: ${tokenInfoError.message}`;

    if (message && message !== displayedErrorRef.current) {
      displayedErrorRef.current = message;
      toast.error(message, {
        id: `swap-error-${activeTab}-${Date.now()}`,
        duration: 5000,
      });
    }
  }, [swapHandlerErrorText, estimationError, tokenInfoError, activeTab, isBonded]);

  useEffect(() => {
    if (isBonded && dexSwap.estimatedOutput) {
      // For bonded tokens, use the estimate from the DEX swap hook
      setOutputAmountString(dexSwap.estimatedOutput);
    } else if (!isBonded && estimatedOutputAmountBigInt !== undefined && parsedDebouncedInputAmount > BigInt(0)) {
      // For non-bonded tokens, use the launchpad estimate
      setOutputAmountString(formatUnits(estimatedOutputAmountBigInt, isBuy ? targetTokenDecimals : HYPE_DECIMALS));
    } else {
      setOutputAmountString("");
    }
  }, [
    isBonded,
    dexSwap.estimatedOutput,
    isBuy,
    estimatedOutputAmountBigInt,
    targetTokenDecimals,
    parsedDebouncedInputAmount,
  ]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^\d*\.?\d*$/.test(value) || value === "") {
      setInputAmountRaw(value);
      if (hasInitiatedTx && !isProcessing && !isConfirming) {
        setHasInitiatedTx(false);
      }
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value as "buy" | "sell");
    if (!isProcessing && !isConfirming) {
      setInputAmountRaw("");
      setOutputAmountString("");
    }
    setHasInitiatedTx(false);
    displayedErrorRef.current = null;
    clearError();
  };

  const currentInputEffectiveBalance = isBuy ? hypeBalanceBigInt : effectiveTargetTokenBalance;
  const currentInputEffectiveSymbol = isBuy ? HYPE_SYMBOL : displayTargetSymbol;
  const currentInputEffectiveDecimals = isBuy ? HYPE_DECIMALS : targetTokenDecimals;
  const currentInputIcon = isBuy ? IMAGES.HYPE : tokenDetails?.metadata.image_uri || IMAGES.SWIRL;
  const currentOutputSymbol = isBuy ? displayTargetSymbol : HYPE_SYMBOL;
  // const currentOutputIcon = isBuy ? tokenDetails?.metadata.image_uri || IMAGES.SWIRL : IMAGES.HYPE;

  const formattedAvailableBalance = () => {
    if (currentInputEffectiveBalance !== undefined && currentInputEffectiveDecimals !== undefined) {
      return parseFloat(formatUnits(currentInputEffectiveBalance, currentInputEffectiveDecimals)).toLocaleString(
        undefined,
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 4,
        }
      );
    }
    return "0.00";
  };

  const handleQuickAmount = (fraction: number) => {
    if (currentInputEffectiveBalance === undefined || currentInputEffectiveDecimals === undefined) {
      setInputAmountRaw("0");
      return;
    }
    const fullBalanceString = formatUnits(currentInputEffectiveBalance, currentInputEffectiveDecimals);
    const fullBalanceFloat = parseFloat(fullBalanceString);

    if (isNaN(fullBalanceFloat) || fullBalanceFloat === 0) {
      setInputAmountRaw("0");
      return;
    }
    const val = fullBalanceFloat * fraction;
    const displayPrecision = Math.min(currentInputEffectiveDecimals, 8);
    setInputAmountRaw(val.toFixed(displayPrecision).replace(/\.?0+$/, ""));
  };

  const handleReset = () => {
    setInputAmountRaw("");
  };

  const handleSwapButtonClick = async () => {
    if (!isConnected || !userAddress) {
      toast.error("Please connect your wallet.", {
        id: "wallet-connection-required",
        duration: 4000,
      });
      return;
    }
    setHasInitiatedTx(true);
    await handleSwap();
  };

  const handleSlippagePresetSelect = (preset: number) => {
    setSlippageSetting(preset);
    setCustomSlippageInput(preset.toString());
  };

  const handleCustomSlippageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomSlippageInput(value);
    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue) && parsedValue > 0 && parsedValue <= 50) {
      setSlippageSetting(parsedValue);
    } else if (value !== "" && (isNaN(parsedValue) || parsedValue <= 0 || parsedValue > 50)) {
      // No immediate error toast for this specific input per previous behavior
    }
  };

  const fees = "1% / 1%";

  // For bonded tokens, the hook handles its own loading logic (including zero-downtime swapping)
  // For non-bonded tokens, we use the traditional loading logic
  const isLoadingAnything = isBonded
    ? isLoadingTargetInfo || isBalancesLoading // Don't include dexSwap.isLoadingEstimate for bonded tokens
    : isLoadingTargetInfo || isLoadingEstimation || isBalancesLoading;

  const isMainButtonDisabled =
    !isConnected ||
    isSwapButtonLoadingHook ||
    isProcessing ||
    isConfirming ||
    !isAmountValid ||
    parseFloat(inputAmountRaw) <= 0 ||
    isLoadingAnything;

  return (
    <Card className={cn(`w-full bg-backgroundDark backdrop-blur-md pb-3 pt-2 border-none`, className)}>
      <CardContent className="px-4 py-2">
        <div className="flex items-center justify-between mb-4">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="w-full grid grid-cols-2">
              <TabsTrigger
                value="buy"
                className={cn(
                  "rounded-l-lg",
                  activeTab === "buy" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}
              >
                Buy
              </TabsTrigger>
              <TabsTrigger
                value="sell"
                className={cn(
                  "rounded-r-lg",
                  activeTab === "sell" ? "bg-destructive text-destructive-foreground" : "bg-muted text-muted-foreground"
                )}
              >
                Sell
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon" className="ml-2 text-xs px-2 py-1 h-8" aria-label="Open settings">
                <SettingsIcon className="w-5 h-5" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-[90vw] sm:absolute top-[21.2%] sm:left-[86.5%] 2xl:left-[88.2%] sm:max-w-[340px] rounded-3xl bg-backgroundDark border-none">
              <DialogHeader>
                <DialogTitle className="text-foreground">Transaction Settings</DialogTitle>
              </DialogHeader>
              <div className="mt-4">
                <div className="mb-2 font-medium text-muted-foreground text-sm">Slippage Tolerance</div>
                <div className="flex gap-2 mb-3">
                  {SLIPPAGE_PRESETS.map((preset) => (
                    <Button
                      key={preset}
                      variant={
                        slippageSetting === preset && customSlippageInput === preset.toString() ? "default" : "outline"
                      }
                      size="sm"
                      className={cn(
                        slippageSetting === preset && customSlippageInput === preset.toString()
                          ? "bg-primary text-primary-foreground border-primary"
                          : "border-borderBright hover:bg-secondary"
                      )}
                      onClick={() => handleSlippagePresetSelect(preset)}
                    >
                      {preset}%
                    </Button>
                  ))}
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    type="text"
                    placeholder="Custom"
                    value={customSlippageInput}
                    onChange={handleCustomSlippageInputChange}
                    className="w-20 bg-background border-border focus:ring-primary"
                  />
                  <span className="text-muted-foreground text-sm">%</span>
                </div>
              </div>
              <DialogFooter>
                <Button variant="secondary" onClick={() => setSettingsOpen(false)} className="mt-4 w-full">
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-muted-foreground">Available to Trade</span>
          <span className="text-sm font-medium">
            {formattedAvailableBalance()} {currentInputEffectiveSymbol}
          </span>
        </div>

        <div className="relative flex items-center mb-3">
          <Input
            type="text"
            placeholder="0.00"
            value={inputAmountRaw}
            onChange={handleInputChange}
            className="pl-4 pr-24 text-lg h-14 bg-muted/30 border border-border rounded-lg"
            disabled={isProcessing || isConfirming}
          />
          <div className="absolute right-3 flex items-center gap-2">
            {inputAmountRaw && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0 hover:bg-muted/50"
                onClick={handleReset}
                disabled={isProcessing || isConfirming}
              >
                <X className="h-4 w-4 text-muted-foreground" />
              </Button>
            )}
            <span className="font-medium text-base">{currentInputEffectiveSymbol}</span>
            <img
              src={currentInputIcon}
              alt={currentInputEffectiveSymbol}
              width={24}
              height={24}
              className="rounded-full"
            />
          </div>
        </div>

        <div className="flex gap-2 mb-4">
          {QUICK_AMOUNTS.map((q) => (
            <Button
              key={q}
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => handleQuickAmount(q)}
              disabled={
                isProcessing ||
                isConfirming ||
                currentInputEffectiveBalance === undefined ||
                currentInputEffectiveBalance === BigInt(0)
              }
            >
              {`${Math.round(q * 100)}%`}
            </Button>
          ))}
        </div>

        <div className="flex justify-between items-center mb-1">
          <span className="text-sm text-muted-foreground flex items-center gap-1">
            Estimated Output
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="w-4 h-4 text-muted-foreground/60 cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="bg-backgroundDark border-border text-foreground">
                  <p className="text-xs">Output is an estimate. Actual amount may vary.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </span>
          {(isBonded ? dexSwap.isLoadingEstimate : isLoadingEstimation) &&
          parsedDebouncedInputAmount > BigInt(0) &&
          !outputAmountString ? (
            <span className="text-sm font-medium text-muted-foreground">Estimating...</span>
          ) : (
            <span className="text-sm font-medium">
              {formatTokenValue(outputAmountString) || "0.00"} {currentOutputSymbol}
            </span>
          )}
        </div>
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm text-muted-foreground">Fees</span>
          <span className="text-sm font-medium">{fees}</span>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-sm text-muted-foreground">Slippage</span>
          <span className="text-sm font-medium">{slippageSetting}%</span>
        </div>

        {!isConnected ? (
          <Button
            className="w-full h-12 text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground"
            onClick={() => {
              if (openConnectModal) {
                openConnectModal();
              }
            }}
          >
            Connect Wallet
          </Button>
        ) : (
          <Button
            className={cn(
              "w-full h-12 text-lg font-semibold",
              isMainButtonDisabled
                ? "bg-primary/60 text-primary-foreground/70 cursor-not-allowed"
                : isBuy
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : "bg-destructive text-destructive-foreground hover:bg-destructive/90",
              (isProcessing || isConfirming || isSwapButtonLoadingHook || isLoadingAnything) &&
                "opacity-70 cursor-not-allowed"
            )}
            disabled={isMainButtonDisabled}
            onClick={handleSwapButtonClick}
          >
            {swapButtonText.endsWith("...") ? (
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-current"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                {swapButtonText}
              </div>
            ) : (
              swapButtonText
            )}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
