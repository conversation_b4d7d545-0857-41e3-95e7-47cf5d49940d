"use client";

import React, { useC<PERSON>back, useEffect, useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAccount } from "wagmi";
import type { ColumnDef, PaginationState } from "@tanstack/react-table";
import { DataTable } from "@/components/ui/data-table";
import { getWalletSwapsAction } from "@/app/actions/wallet.actions";
import { ApiError } from "@/lib/api";
import { WalletSwapsResponse, WalletSwap, TokenDetails } from "@/lib/api/types";
import { TimeAgoCell } from "@/components/ui/time-ago-cell";
import { formatAddressForTable, formatTokenValue } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import CustomLink from "@/components/shared/Link";
import { useBalanceStore } from "@/stores/balanceStore";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { ScientificPrice } from "@/components/ui/scientific-price";
import { TOKENS } from "@/lib/constants";
import { IMAGES } from "@/lib/constants-images";
import Image from "next/image";
import { Calendar, DollarSign, Filter, ExternalLink } from "lucide-react";
import { updaterService } from "@/services/updaterService";

interface PersonalTableProps {
  tokenAddress: string;
  symbol: string;
  tokenDetails?: TokenDetails | null;
  isActive?: boolean;
}

export function PersonalTable({ tokenAddress, symbol, tokenDetails, isActive = true }: PersonalTableProps) {
  const { showUsd } = useCurrencyFormatter();
  const hypePriceUsd = useBalanceStore((state) => state.getTokenPrice(TOKENS.NATIVE_HYPE_IDENTIFIER));
  const { address: userAddress, isConnected } = useAccount();
  const queryClient = useQueryClient();
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const queryKey = useMemo(
    () => ["walletSwaps", userAddress, tokenAddress, pagination.pageIndex + 1, pagination.pageSize] as const,
    [userAddress, tokenAddress, pagination.pageIndex, pagination.pageSize],
  );

  const {
    data: response,
    isLoading: queryIsLoading,
    error,
  } = useQuery<WalletSwapsResponse, ApiError, WalletSwapsResponse, typeof queryKey>({
    queryKey: queryKey,
    queryFn: async () => {
      if (!userAddress || !tokenAddress) {
        return {
          swaps: [],
          pagination: { currentPage: 1, totalPages: 0, totalSwaps: 0, limit: pagination.pageSize },
        };
      }
      return getWalletSwapsAction(userAddress.toLowerCase(), {
        token: tokenAddress.toLowerCase(),
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sort_order: "desc",
      });
    },
    placeholderData: (previousData) => previousData,
    enabled: !!(isConnected && userAddress && tokenAddress),
    refetchOnWindowFocus: false, // Handled by polling instead
  });

  // Polling callback to refetch data
  const refreshData = useCallback(async () => {
    if (isConnected && userAddress && tokenAddress) {
      await queryClient.invalidateQueries({
        queryKey: ["walletSwaps", userAddress, tokenAddress],
      });
    }
  }, [queryClient, isConnected, userAddress, tokenAddress]);

  // Set up polling when tab is visible and data exists
  useEffect(() => {
    if (!isConnected || !userAddress || !tokenAddress || !isActive) {
      return;
    }

    const pollerId = updaterService.startWalletSwapsPolling(
      userAddress.toLowerCase(),
      tokenAddress.toLowerCase(),
      refreshData,
      {
        enabled: true,
        interval: 5000, // 5 seconds
      },
    );

    return () => {
      updaterService.stopPolling(pollerId);
    };
  }, [userAddress, tokenAddress, refreshData, isConnected, isActive]);

  const columns = useMemo<ColumnDef<WalletSwap>[]>(
    () => [
      {
        accessorKey: "timestamp",
        header: () => (
          <div className="flex items-center gap-1">
            DATE <Calendar className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => <TimeAgoCell timestamp={row.original.timestamp} />,
      },
      {
        accessorKey: "type",
        header: () => (
          <div className="flex items-center gap-1">
            TYPE <Filter className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => {
          const type = row.original.type === "purchase" ? "Buy" : "Sell";
          // Personal table - all transactions are by the user, so no dev highlighting needed
          const baseClasses = `px-2 py-0.5 rounded-full text-xs font-medium ${
            type === "Buy" ? "bg-green-500/10 text-green-400" : "bg-red-500/10 text-red-400"
          }`;

          const borderClasses = type === "Buy" ? "border border-green-500/30" : "border border-red-500/30";

          return <span className={`${baseClasses} ${borderClasses}`}>{type}</span>;
        },
      },
      {
        accessorKey: "hype_amount",
        header: () => (
          <div className="flex items-center gap-1">
            VALUE{" "}
            {showUsd ? (
              <DollarSign className="w-4 h-4 opacity-60" />
            ) : (
              <Image src={IMAGES.HYPE} alt="HYPE" width={16} height={16} className="opacity-60" />
            )}
          </div>
        ),
        cell: ({ row }) => {
          const hypeAmount = parseFloat(row.original.hype_amount);

          if (showUsd) {
            if (hypePriceUsd) {
              const usdValue = hypeAmount * parseFloat(hypePriceUsd);
              return (
                <div className="tabular-nums">
                  $
                  {usdValue.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
              );
            } else {
              // Show skeleton when USD is selected but HYPE price not available
              return (
                <div className="tabular-nums">
                  <Skeleton className="h-4 w-16" />
                </div>
              );
            }
          }

          return (
            <div className="tabular-nums">
              {hypeAmount.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </div>
          );
        },
      },
      {
        accessorKey: "token_amount",
        header: () => <div className="flex items-center gap-1">{symbol ? symbol.toUpperCase() : "TOKEN"} AMOUNT</div>,
        cell: ({ row }) => {
          const tokenAmount = row.original.token_amount;

          try {
            // TODO: Make sure if any API changes to this value, we need to update the decimals
            const formattedAmount = formatTokenValue(tokenAmount, 2);
            return <div className="tabular-nums">{formattedAmount}</div>;
          } catch (error) {
            console.warn("[PersonalTable] Error parsing token amount:", { tokenAmount, error });
            return <div className="tabular-nums text-muted-foreground">Invalid</div>;
          }
        },
      },
      {
        accessorKey: "price",
        header: () => (
          <div className="flex items-center gap-1">
            PRICE{" "}
            {showUsd ? (
              <DollarSign className="w-4 h-4 opacity-60" />
            ) : (
              <Image src={IMAGES.HYPE} alt="HYPE" width={16} height={16} className="opacity-60" />
            )}
          </div>
        ),
        cell: ({ row }) => {
          let displayPrice = parseFloat(row.original.price);

          if (showUsd) {
            if (hypePriceUsd) {
              // Convert HYPE price to USD (swap price is in HYPE per token)
              displayPrice = displayPrice * parseFloat(hypePriceUsd);
              return (
                <div className="tabular-nums">
                  <ScientificPrice price={displayPrice} usd={true} />
                </div>
              );
            } else {
              // Show skeleton when USD is selected but HYPE price not available
              return (
                <div className="tabular-nums">
                  <Skeleton className="h-4 w-20" />
                </div>
              );
            }
          }

          return (
            <div className="tabular-nums">
              <ScientificPrice price={displayPrice} usd={false} />
            </div>
          );
        },
      },
      {
        accessorKey: "tx_hash",
        header: () => <div className="whitespace-nowrap">TXN</div>,
        cell: ({ row }) => (
          <CustomLink
            href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/tx/${row.original.tx_hash}`}
            target="_blank"
            rel="noopener noreferrer"
            className="hover:underline text-primary/90 font-mono text-xs"
            title={row.original.tx_hash}
          >
            <div className="flex items-center gap-1">
              <ExternalLink className="w-4 h-4" />
            </div>
          </CustomLink>
        ),
      },
    ],
    [symbol, showUsd, hypePriceUsd],
  );

  const tableData = response?.swaps || [];
  const pageCount = response?.pagination?.totalPages;
  const totalItems = response?.pagination?.totalSwaps;
  // Only show loading on initial load, not during background refetches
  const tableIsLoading = queryIsLoading && !response;

  // Show connection message if not connected
  if (!isConnected) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        <p>Connect your wallet to view your personal trading history for this token.</p>
      </div>
    );
  }

  // Show error if any
  if (error) {
    return (
      <div className="text-red-500 p-4 rounded-lg border border-destructive bg-destructive/10">
        Error fetching personal swaps: {error.message}
        {error.errorResponse && (
          <pre className="mt-2 text-xs whitespace-pre-wrap">{JSON.stringify(error.errorResponse, null, 2)}</pre>
        )}
      </div>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={tableData}
      pageCount={pageCount}
      pagination={pagination}
      onPaginationChange={setPagination}
      isLoading={tableIsLoading}
      itemTypeName="personal swaps"
      totalItems={totalItems}
    />
  );
}
