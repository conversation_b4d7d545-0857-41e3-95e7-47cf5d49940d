"use client";

import { Calendar, Filter, DollarSign, ExternalLink } from "lucide-react";
import React, { useState, useMemo, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAccount } from "wagmi";
import { getTokenSwapsAction } from "@/app/token/[tokenAddress]/actions";
import { ApiError, TokenSwapsResponse, Swap } from "@/lib/api";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { TimeAgoCell } from "@/components/ui/time-ago-cell";
import { Skeleton } from "@/components/ui/skeleton";
import { formatAddressForTable, formatTokenValue } from "@/lib/utils";
import CustomLink from "@/components/shared/Link";
import { useTokenSwaps } from "@/hooks/useWebSocket";
import { useBalanceStore } from "@/stores/balanceStore";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { TokenDetails } from "@/lib/api/types";
import { ScientificPrice } from "@/components/ui/scientific-price";
import { TOKENS } from "@/lib/constants";
import { IMAGES } from "@/lib/constants-images";
import Image from "next/image";

interface SwapsTableProps {
  symbol: string;
  tokenAddress: string;
  tokenDetails?: TokenDetails | null;
}

export default function SwapsTable({ symbol, tokenAddress, tokenDetails }: SwapsTableProps) {
  const { showUsd } = useCurrencyFormatter();
  const hypePriceUsd = useBalanceStore((state) => state.getTokenPrice(TOKENS.NATIVE_HYPE_IDENTIFIER));
  const { address: userAddress } = useAccount();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const queryClient = useQueryClient();
  const queryKey = useMemo(
    () => ["swaps", tokenAddress, pagination.pageIndex + 1, pagination.pageSize, undefined] as const,
    [tokenAddress, pagination.pageIndex, pagination.pageSize],
  );

  // Handle real-time swap updates via WebSocket
  const handleSwapUpdate = useCallback(
    (newSwap: Swap) => {
      console.log(`[SwapsTable] Received new swap for token: ${tokenAddress}`, newSwap);

      // Update the query cache with the new swap
      queryClient.setQueryData(queryKey, (oldData: TokenSwapsResponse | undefined) => {
        if (!oldData) return oldData;

        // Check if swap already exists to prevent duplicates
        const existingSwapIndex = oldData.swaps.findIndex(
          (swap) => swap.id === newSwap.id || swap.tx_hash === newSwap.tx_hash,
        );

        if (existingSwapIndex !== -1) {
          console.log(`[SwapsTable] Duplicate swap detected, skipping:`, newSwap.tx_hash);
          return oldData;
        }

        // Only add to first page to avoid pagination issues
        if (pagination.pageIndex !== 0) {
          return oldData;
        }

        // Add the new swap to the beginning of the list (most recent first)
        const updatedSwaps = [newSwap, ...oldData.swaps].slice(0, pagination.pageSize);

        return {
          ...oldData,
          swaps: updatedSwaps,
          pagination: {
            ...oldData.pagination,
            totalSwaps: oldData.pagination.totalSwaps + 1,
          },
        };
      });
    },
    [queryClient, tokenAddress, queryKey, pagination.pageIndex, pagination.pageSize],
  );

  // Set up WebSocket subscription for real-time swap updates
  useTokenSwaps(tokenAddress, handleSwapUpdate, {
    enabled: !!tokenAddress,
    onError: (error) => {
      console.error(`[SwapsTable] WebSocket error for token ${tokenAddress}:`, error);
    },
  });

  const {
    data: response,
    isLoading: queryIsLoading,
    error,
  } = useQuery<TokenSwapsResponse, ApiError, TokenSwapsResponse, typeof queryKey>({
    queryKey: queryKey,
    queryFn: async () => {
      if (!tokenAddress) throw new Error("Token address is required.");
      return getTokenSwapsAction(tokenAddress, {
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        type: undefined,
      });
    },
    placeholderData: (previousData) => previousData,
    enabled: !!tokenAddress,
    refetchOnWindowFocus: false, // Prevent automatic refetches since we use WebSocket
    staleTime: Infinity, // Data is always fresh due to WebSocket updates
  });

  const columns = useMemo<ColumnDef<Swap>[]>(
    () => [
      {
        accessorKey: "timestamp",
        header: () => (
          <div className="flex items-center gap-1">
            DATE <Calendar className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => <TimeAgoCell timestamp={row.original.timestamp} />,
      },
      {
        accessorKey: "type",
        header: () => (
          <div className="flex items-center gap-1">
            TYPE <Filter className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => {
          const type = row.original.type === "purchase" ? "Buy" : "Sell";
          const isDevTransaction =
            tokenDetails?.creator && row.original.trader.toLowerCase() === tokenDetails.creator.toLowerCase();

          const baseClasses = `px-2 py-0.5 rounded-full text-xs font-medium ${
            isDevTransaction
              ? type === "Buy"
                ? "bg-green-500/10 text-yellow-400"
                : "bg-red-500/10 text-yellow-400"
              : type === "Buy"
                ? "bg-green-500/10 text-green-400"
                : "bg-red-500/10 text-red-400"
          }`;

          const borderClasses = isDevTransaction
            ? "border border-yellow-400/30"
            : type === "Buy"
              ? "border border-green-500/30"
              : "border border-red-500/30";

          return <span className={`${baseClasses} ${borderClasses}`}>{isDevTransaction ? `Dev ${type}` : type}</span>;
        },
      },
      {
        accessorKey: "hype_amount",
        header: () => (
          <div className="flex items-center gap-1">
            VALUE{" "}
            {showUsd ? (
              <DollarSign className="w-4 h-4 opacity-60" />
            ) : (
              <Image src={IMAGES.HYPE} alt="HYPE" width={16} height={16} className="opacity-60" />
            )}
          </div>
        ),
        cell: ({ row }) => {
          const hypeAmount = parseFloat(row.original.hype_amount);

          if (showUsd) {
            if (hypePriceUsd) {
              const usdValue = hypeAmount * parseFloat(hypePriceUsd);
              return (
                <div className="tabular-nums">
                  $
                  {usdValue.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
              );
            } else {
              // Show skeleton when USD is selected but HYPE price not available
              return (
                <div className="tabular-nums">
                  <Skeleton className="h-4 w-16" />
                </div>
              );
            }
          }

          return (
            <div className="tabular-nums">
              {hypeAmount.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </div>
          );
        },
      },
      {
        accessorKey: "token_amount",
        header: () => <div className="flex items-center gap-1">{symbol ? symbol.toUpperCase() : "TOKEN"} AMOUNT</div>,
        cell: ({ row }) => {
          const tokenAmount = row.original.token_amount;

          try {
            // TODO: Make sure if any API changes to this value, we need to update the decimals
            const formattedAmount = formatTokenValue(tokenAmount, 2);
            return <div className="tabular-nums">{formattedAmount}</div>;
          } catch (error) {
            console.warn("[SwapsTable] Error parsing token amount:", { tokenAmount, error });
            return <div className="tabular-nums text-muted-foreground">Invalid</div>;
          }
        },
      },
      {
        accessorKey: "price",
        header: () => (
          <div className="flex items-center gap-1">
            PRICE{" "}
            {showUsd ? (
              <DollarSign className="w-4 h-4 opacity-60" />
            ) : (
              <Image src={IMAGES.HYPE} alt="HYPE" width={16} height={16} className="opacity-60" />
            )}
          </div>
        ),
        cell: ({ row }) => {
          let displayPrice = parseFloat(row.original.price);

          if (showUsd) {
            if (hypePriceUsd) {
              // Convert HYPE price to USD (swap price is in HYPE per token)
              displayPrice = displayPrice * parseFloat(hypePriceUsd);
              return (
                <div className="tabular-nums">
                  <ScientificPrice price={displayPrice} usd={true} />
                </div>
              );
            } else {
              // Show skeleton when USD is selected but HYPE price not available
              return (
                <div className="tabular-nums">
                  <Skeleton className="h-4 w-20" />
                </div>
              );
            }
          }

          return (
            <div className="tabular-nums">
              <ScientificPrice price={displayPrice} usd={false} />
            </div>
          );
        },
      },
      {
        accessorKey: "trader",
        header: () => (
          <div className="flex items-center gap-1">
            MAKER <Filter className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => {
          const isUserTransaction = userAddress && row.original.trader.toLowerCase() === userAddress.toLowerCase();

          return (
            <div className="flex items-center gap-2">
              <CustomLink
                href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/address/${row.original.trader}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline text-primary/90 font-mono text-xs"
                title={row.original.trader}
              >
                {formatAddressForTable(row.original.trader)}
              </CustomLink>
              {isUserTransaction && (
                <span className="px-1.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/30">
                  You
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "tx_hash",
        header: () => <div className="whitespace-nowrap">TXN</div>,
        cell: ({ row }) => (
          <CustomLink
            href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/tx/${row.original.tx_hash}`}
            target="_blank"
            rel="noopener noreferrer"
            className="hover:underline text-primary/90 font-mono text-xs"
            title={row.original.tx_hash}
          >
            <div className="flex items-center gap-1">
              <ExternalLink className="w-4 h-4" />
            </div>
          </CustomLink>
        ),
      },
    ],
    [symbol, showUsd, tokenDetails, hypePriceUsd, userAddress],
  );

  // Function to determine custom row styling for user swaps
  const getRowClassName = useCallback(
    (swap: Swap) => {
      const isUserTransaction = userAddress && swap.trader.toLowerCase() === userAddress.toLowerCase();
      if (!isUserTransaction) return "";

      const isBuy = swap.type === "purchase";
      if (isBuy) {
        return "border-l-2 border-l-green-400 bg-green-500/5 hover:bg-green-500/10";
      } else {
        return "border-l-2 border-l-red-400 bg-red-500/5 hover:bg-red-500/10";
      }
    },
    [userAddress],
  );

  const swapsToDisplay = response?.swaps || [];
  const pageCount = response?.pagination?.totalPages;
  const totalItems = response?.pagination?.totalSwaps;

  // Determine loading state for DataTable - only show loading on initial load
  // Don't show loading during background refetches to maintain silent updates
  const tableIsLoading = queryIsLoading && !response;

  if (error) {
    return (
      <div className="text-red-500 p-4 rounded-lg border border-destructive bg-destructive/10">
        Error fetching swaps: {error.message}
        {error.errorResponse && (
          <pre className="mt-2 text-xs whitespace-pre-wrap">{JSON.stringify(error.errorResponse, null, 2)}</pre>
        )}
      </div>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={swapsToDisplay}
      isLoading={tableIsLoading}
      pageCount={pageCount}
      pagination={{
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      }}
      onPaginationChange={setPagination}
      totalItems={totalItems}
      itemTypeName="swaps"
      getRowClassName={getRowClassName}
    />
  );
}
