"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, TrendingUp, Hash } from "lucide-react";
import React, { useState, useMemo, useCallback, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getTokenHoldersAction } from "@/app/token/[tokenAddress]/actions";
import { ApiError, TokenHoldersResponse, Holder } from "@/lib/api";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { formatAddressForTable, formatTokenBalance, formatCurrency, formatTokenValue } from "@/lib/utils";
import { parseUnits } from "viem";
import { TokenDetails } from "@/lib/api/types";
import { CONTRACTS } from "@/lib/constants";
import { updaterService } from "@/services/updaterService";

interface HoldersTableProps {
  tokenAddress: string;
  tokenDetails?: TokenDetails | null;
  isActive?: boolean;
}

export default function HoldersTable({ tokenAddress, tokenDetails, isActive = true }: HoldersTableProps) {
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const queryKey = ["holders", tokenAddress, pagination.pageIndex + 1, pagination.pageSize] as const;

  const {
    data: response,
    isLoading: queryIsLoading,
    error,
  } = useQuery<TokenHoldersResponse, ApiError, TokenHoldersResponse, typeof queryKey>({
    queryKey: queryKey,
    queryFn: async () => {
      if (!tokenAddress) throw new Error("Token address is required.");
      return getTokenHoldersAction(tokenAddress.toLowerCase(), {
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
      });
    },
    placeholderData: (previousData) => previousData,
    enabled: !!tokenAddress,
    refetchOnWindowFocus: false, // Handled by polling instead
  });

  // Polling callback to refetch data
  const refreshData = useCallback(async () => {
    if (tokenAddress) {
      await queryClient.invalidateQueries({
        queryKey: ["holders", tokenAddress],
      });
    }
  }, [queryClient, tokenAddress]);

  // Set up polling when component is mounted and has tokenAddress
  useEffect(() => {
    if (!tokenAddress || !isActive) {
      return;
    }

    const pollerId = updaterService.startHoldersPolling(tokenAddress.toLowerCase(), refreshData, {
      enabled: true,
      interval: 5000, // 5 seconds
    });

    return () => {
      updaterService.stopPolling(pollerId);
    };
  }, [tokenAddress, refreshData, isActive]);

  const columns = useMemo<ColumnDef<Holder>[]>(
    () => [
      {
        id: "rank",
        header: () => (
          <div className="flex items-center gap-1">
            RANK <Hash className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row, table }) => {
          const { pageIndex, pageSize } = table.getState().pagination;
          return pageIndex * pageSize + row.index + 1;
        },
      },
      {
        accessorKey: "address",
        header: () => (
          <div className="flex items-center gap-1">
            ADDRESS <Wallet className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => {
          const isDevHolder =
            tokenDetails?.creator && row.original.address.toLowerCase() === tokenDetails.creator.toLowerCase();
          const isLaunchpadHolder = row.original.address.toLowerCase() === CONTRACTS.LAUNCHPAD_ADDRESS.toLowerCase();

          return (
            <div className="flex items-center gap-2">
              <a
                href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/address/${row.original.address}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline text-primary/90 font-mono text-xs"
                title={row.original.address}
              >
                {formatAddressForTable(row.original.address)}
              </a>
              {isDevHolder && (
                <span className="px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-400 border border-yellow-400/30">
                  Dev
                </span>
              )}
              {isLaunchpadHolder && (
                <span className="px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/10 text-blue-400 border border-blue-400/30">
                  Curve
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "balance",
        header: () => (
          <div className="flex items-center gap-1 justify-end w-full">
            BALANCE <TrendingUp className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => (
          // TODO: Make sure if any API changes to this value, we need to update the decimals
          <div className="tabular-nums text-right">
            {formatTokenValue(Number(parseUnits(row.original.balance, 12)), 2)}
          </div>
        ),
      },
      {
        accessorKey: "percentage",
        header: () => (
          <div className="flex items-center gap-1 justify-end w-full">
            SHARE <Percent className="w-4 h-4 opacity-60" />
          </div>
        ),
        cell: ({ row }) => (
          <div className="tabular-nums text-right">{parseFloat(row.original.percentage).toFixed(2)}%</div>
        ),
      },
    ],
    [response?.token?.symbol, tokenDetails?.creator],
  );

  const holdersToDisplay = response?.holders || [];
  const pageCount = response?.pagination?.totalPages;
  const totalItems = response?.pagination?.totalHolders;
  // Only show loading on initial load, not during background refetches
  const tableIsLoading = queryIsLoading && !response;

  if (error) {
    return (
      <div className="text-red-500 p-4 rounded-lg border border-destructive bg-destructive/10">
        Error fetching holders: {error.message}
        {error.errorResponse && (
          <pre className="mt-2 text-xs whitespace-pre-wrap">{JSON.stringify(error.errorResponse, null, 2)}</pre>
        )}
      </div>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={holdersToDisplay}
      isLoading={tableIsLoading}
      pageCount={pageCount}
      pagination={{
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      }}
      onPaginationChange={setPagination}
      totalItems={totalItems}
      itemTypeName="holders"
    />
  );
}
