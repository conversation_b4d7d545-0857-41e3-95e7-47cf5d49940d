"use client";

import { useEffect, useState } from "react";
import { useAccount } from "wagmi";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2, CheckCircle2, LinkIcon, ArrowRight } from "lucide-react";
import Link from "next/link";
import { shortenAddress, cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { CreateDetails } from "@/components/create/CreateDetails";
import { ProgressBar } from "@/components/create/ProgressBar";
import { TokenDetailsForm } from "@/components/create/TokenDetailsForm";
import { SocialMediaForm } from "@/components/create/SocialMediaForm";
import { TokenPreview } from "@/components/create/TokenPreview";
import { useTokenForm } from "@/hooks/create/use-token-form";
import { useTokenSubmission } from "@/hooks/create/use-token-submission";
import { useTokenSteps } from "@/hooks/create/use-token-steps";

export default function CreateToken() {
  const [isMounted, setIsMounted] = useState(false);
  const { address } = useAccount();
  const router = useRouter();

  const { formData, errors, validateStep1, validateStep2, handleInputChange, handleImageChange, resetForm } =
    useTokenForm();

  const { isPendingTx, submitToken } = useTokenSubmission();
  const { currentStep, handleNext, handleBack } = useTokenSteps();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!address) return;

    if (currentStep === 1) {
      if (validateStep1()) {
        handleNext();
      }
      return;
    }

    if (!validateStep2()) {
      toast.error("Please check the form for errors");
      return;
    }

    try {
      const result = await submitToken(formData);

      toast.success(
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5" />
            <span>
              Token {formData.name} ({formData.symbol}) has been created!
            </span>
          </div>
          {result && (
            <Link
              href={`/token/${result.pool_address}`}
              className="inline-flex items-center gap-1 text-primary hover:underline"
            >
              <LinkIcon className="h-4 w-4" />
              {shortenAddress(result.tx_hash)}
            </Link>
          )}
        </div>
      );

      resetForm();

      // Navigate to the token page if we have the address
      if (result?.pool_address) {
        router.push(`/token/${result.pool_address}`);
      }
    } catch (error) {
      console.error("Error creating token:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create token");
    }
  };

  if (!isMounted) return null;

  const disabled =
    !address ||
    isPendingTx ||
    (currentStep === 1 && (isNaN(Number(formData.initialLiquidity)) || Number(formData.initialLiquidity) < 1));

  return (
    <div className="-mx-6">
      {/* Header Section */}
      <div className="px-6 mb-6">
        <div className="mb-4">
          <h1 className="text-2xl font-semibold tracking-tight">Create Token</h1>
          <p className="text-muted-foreground text-sm mt-1">Launch your token on HyperEVM with zero upfront costs</p>
        </div>
        <ProgressBar currentStep={currentStep} totalSteps={2} />
      </div>

      {/* Main Content */}
      <div className="px-6">
        <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-start">
          {/* Left Column: Form and Details */}
          <div className="flex flex-col gap-4 min-w-0">
            {/* Main Form Card */}
            <Card className="w-full bg-backgroundDark backdrop-blur-md border-none">
              <form onSubmit={handleSubmit}>
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-medium">
                    {currentStep === 1 ? "Token Details" : "Social Media & Image"}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {currentStep === 1
                      ? "Configure your token's basic information and initial liquidity"
                      : "Add optional social media links and token image to build trust"}
                  </CardDescription>
                </CardHeader>

                <CardContent className="px-6 pb-4">
                  {currentStep === 1 ? (
                    <TokenDetailsForm formData={formData} errors={errors} onChange={handleInputChange} />
                  ) : (
                    <SocialMediaForm
                      formData={formData}
                      errors={errors}
                      onChange={handleInputChange}
                      onImageChange={handleImageChange}
                    />
                  )}
                </CardContent>

                <CardFooter className="flex gap-3 pt-4">
                  {currentStep === 2 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                      disabled={isPendingTx}
                    >
                      Back
                    </Button>
                  )}
                  <Button
                    type="submit"
                    className={cn(
                      "flex-1 bg-primary hover:bg-primary/90 text-primary-foreground",
                      currentStep === 1 && "justify-between"
                    )}
                    disabled={disabled}
                  >
                    {currentStep === 1 ? (
                      <>
                        Next Step
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    ) : (
                      <>
                        {isPendingTx && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {!address ? "Connect Wallet" : isPendingTx ? "Creating Token..." : "Create Token"}
                      </>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>

            {/* Details Card */}
            <CreateDetails currentStep={currentStep} />
          </div>

          {/* Right Column: Token Preview */}
          <div className="w-full md:w-[320px]">
            <TokenPreview
              name={formData.name}
              symbol={formData.symbol}
              description={formData.description}
              initialLiquidity={formData.initialLiquidity}
              dexIndex={formData.dexIndex}
              website={formData.website}
              twitter={formData.twitter}
              telegram={formData.telegram}
              discord={formData.discord}
              imageUrl={formData.imageUrl}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
