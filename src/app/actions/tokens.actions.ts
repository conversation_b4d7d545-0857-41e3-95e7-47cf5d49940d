"use server";

import { LiquidLaunchApiService, ApiError } from "@/lib/api";
import { ListTokensParams, ListTokensResponse, SearchTokensParams, SearchTokensResponse } from "@/lib/api/types";

const apiService = new LiquidLaunchApiService();

/**
 * Server Action to fetch a list of tokens with filtering and sorting options.
 * This is used on the homepage to display the token grid.
 *
 * @param params Optional parameters for filtering and sorting tokens
 * @returns A promise that resolves with the list of tokens and pagination info
 * @throws ApiError if the underlying API service call fails or a generic Error for unexpected issues
 */
export async function getTokens(params?: ListTokensParams): Promise<ListTokensResponse> {
  try {
    const tokensData = await apiService.token.listTokens(params);

    return tokensData;
  } catch (error) {
    console.error("[listTokensAction] Error caught:", error);
    if (error instanceof ApiError) {
      // Re-throw the original ApiError to preserve status code and specific error response
      throw error;
    }
    // Log and re-throw a generic error for unexpected issues
    console.error("Unexpected error in listTokensAction (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while fetching token list.");
  }
}

/**
 * Server Action to search tokens by name, symbol, or address.
 *
 * @param params Parameters for searching tokens including query and optional limit
 * @returns A promise that resolves with the search results
 * @throws ApiError if the underlying API service call fails or a generic Error for unexpected issues
 */
export async function searchTokens(params: SearchTokensParams): Promise<SearchTokensResponse> {
  console.log("[searchTokensAction] Received params:", JSON.stringify(params, null, 2));
  try {
    const searchResults = await apiService.token.searchTokens(params);
    console.log(
      `\x1b[36m🔍 [searchTokensAction] Search results for "${params.query}":\x1b[0m\n` +
        `\x1b[90m${"─".repeat(60)}\x1b[0m\n` +
        `Found ${searchResults.tokens.length} tokens\n` +
        `\x1b[90m${"─".repeat(60)}\x1b[0m`,
    );
    return searchResults;
  } catch (error) {
    console.error("[searchTokensAction] Error caught:", error);
    if (error instanceof ApiError) {
      // Re-throw the original ApiError to preserve status code and specific error response
      throw error;
    }
    // Log and re-throw a generic error for unexpected issues
    console.error("Unexpected error in searchTokensAction (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while searching tokens.");
  }
}
