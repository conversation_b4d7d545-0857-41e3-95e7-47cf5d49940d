"use server";

import { LiquidLaunchApiService, ApiError } from "@/lib/api";
import { GetLatestSwapsParams, LatestSwapsResponse } from "@/lib/api/types";

const apiService = new LiquidLaunchApiService();

/**
 * Server Action to fetch latest swaps across all tokens.
 *
 * @param params Parameters for limiting results (GetLatestSwapsParams).
 * @returns A promise that resolves with the latest swaps.
 * @throws ApiError if the underlying API service call fails or a generic Error for unexpected issues.
 */
export async function getLatestSwapsAction(params?: GetLatestSwapsParams): Promise<LatestSwapsResponse> {
  // Ensure limit defaults to 50 if not provided or is undefined
  const effectiveParams: GetLatestSwapsParams = {
    limit: params?.limit === undefined ? 50 : params.limit,
  };


  try {
    const swapsData = await apiService.token.getLatestSwaps(effectiveParams);
    return swapsData;
  } catch (error) {
    console.error("[getLatestSwapsAction] Error caught:", error);
    if (error instanceof ApiError) {
      throw new ApiError(error.errorResponse?.error || error.message, error.statusCode, error.errorResponse);
    }
    console.error("Unexpected error in getLatestSwapsAction (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while fetching latest swaps.");
  }
}
