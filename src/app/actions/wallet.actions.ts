"use server";

import { LiquidLaunchApiService, ApiError } from "@/lib/api";
import { WalletInfoResponse, WalletSwapsResponse, GetWalletSwapsParams } from "@/lib/api/types";

const apiService = new LiquidLaunchApiService();

/**
 * Server Action to fetch wallet information including trading statistics.
 *
 * @param walletAddress The wallet address to get information for.
 * @returns A promise that resolves with the wallet information.
 * @throws ApiError if the underlying API service call fails or a generic Error for unexpected issues.
 */
export async function getWalletInfoAction(walletAddress: string): Promise<WalletInfoResponse> {
  const lowercasedAddress = walletAddress.toLowerCase();
  console.log("[getWalletInfoAction] Received (original/lowercased):", {
    walletAddress,
    lowercasedAddress,
  });

  try {
    const walletData = await apiService.wallet.getWalletInfo(lowercasedAddress);
    console.log("[getWalletInfoAction] Data from service:", walletData);
    return walletData;
  } catch (error) {
    console.error("[getWalletInfoAction] Error caught:", error);
    if (error instanceof ApiError) {
      throw new ApiError(error.errorResponse?.error || error.message, error.statusCode, error.errorResponse);
    }
    console.error("Unexpected error in getWalletInfoAction (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while fetching wallet information.");
  }
}

/**
 * Server Action to fetch wallet swaps with optional token filter.
 *
 * @param walletAddress The wallet address to get swaps for.
 * @param params Parameters for pagination and filtering (GetWalletSwapsParams).
 * @returns A promise that resolves with the wallet swap history.
 * @throws ApiError if the underlying API service call fails or a generic Error for unexpected issues.
 */
export async function getWalletSwapsAction(
  walletAddress: string,
  params?: GetWalletSwapsParams,
): Promise<WalletSwapsResponse> {
  const lowercasedAddress = walletAddress.toLowerCase();
  // Ensure limit defaults to 20 if not provided or is undefined
  const effectiveParams: GetWalletSwapsParams = {
    token: params?.token,
    page: params?.page,
    limit: params?.limit === undefined ? 20 : params.limit,
    sort_order: params?.sort_order || "desc",
  };
  console.log("[getWalletSwapsAction] Received (original/lowercased):", {
    walletAddress,
    lowercasedAddress,
    params: effectiveParams,
  });

  try {
    const swapsData = await apiService.wallet.getWalletSwaps(lowercasedAddress, effectiveParams);
    console.log("[getWalletSwapsAction] Data from service:", swapsData);
    return swapsData;
  } catch (error) {
    console.error("[getWalletSwapsAction] Error caught:", error);
    if (error instanceof ApiError) {
      throw new ApiError(error.errorResponse?.error || error.message, error.statusCode, error.errorResponse);
    }
    console.error("Unexpected error in getWalletSwapsAction (rethrowing generic):", error);
    throw new Error("An unexpected error occurred while fetching wallet swaps.");
  }
}
