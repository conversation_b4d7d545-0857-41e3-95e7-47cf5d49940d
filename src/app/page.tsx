"use client";

import { useEffect, useCallback } from "react";
import TokensView from "@/components/tokens/TokensView";
import { LatestSwapsBar } from "@/components/swaps/LatestSwapsBar";
import { TokenFilterToolbar } from "@/components/tokens/TokenFilterToolbar";
import { useTokensStore } from "@/stores/tokensStore";
import { ListTokensParams } from "@/lib/api/types";
import { usePublicClient } from "wagmi";
import { Separator } from "@/components/ui/separator";
import { useTokensUpdater } from "@/hooks/useUpdater";
import { ChatWidget } from "@/components/chat/ChatWidget";

const initialFilterParams: ListTokensParams = {
  sort_by: "mcap",
  sort_order: "desc",
  page: 1,
  limit: 20,
};

export default function HomePage() {
  const publicClient = usePublicClient();

  // Access store values directly
  const fetchTokens = useTokensStore((state) => state.fetchTokens);
  const refreshVisibleTokens = useTokensStore((state) => state.refreshVisibleTokens);
  const currentListParams = useTokensStore((state) => state.currentListParams);
  const currentPage = useTokensStore((state) => state.currentPage);
  const tokenIds = useTokensStore((state) => state.tokenIds);

  useEffect(() => {
    if (publicClient) {
      fetchTokens(publicClient, initialFilterParams);
    }
  }, [publicClient, fetchTokens]);

  // Callback to refresh tokens data silently
  const refreshTokensData = useCallback(async () => {
    console.log(
      `[HomePage] Refreshing ${tokenIds.length} visible tokens (page ${currentPage}) with filters: ${JSON.stringify(
        currentListParams
      )}`
    );
    if (publicClient) {
      // Use refreshVisibleTokens to refresh all currently visible tokens
      try {
        await refreshVisibleTokens(publicClient);
      } catch (error) {
        // Silently handle errors during background refresh
        console.warn("[HomePage] Visible tokens refresh failed:", error);
      }
    }
  }, [publicClient, refreshVisibleTokens, currentListParams, currentPage, tokenIds.length]);

  // Set up polling for new tokens
  useTokensUpdater(refreshTokensData, {
    enabled: !!publicClient,
    interval: 5000, // 5 seconds
  });

  const handleApplyFilters = useCallback(
    (params: ListTokensParams) => {
      if (publicClient) {
        fetchTokens(publicClient, params);
      }
    },
    [publicClient, fetchTokens]
  );

  return (
    <div className="-mx-6">
      <div className="px-6">
        <LatestSwapsBar />
      </div>
      <Separator className="mt-4 mb-2" />
      <div className="px-6">
        <TokenFilterToolbar onApplyFilters={handleApplyFilters} initialParams={initialFilterParams} />
        <TokensView />
      </div>

      {/* Chat Widget - Fixed position */}
      <div className="fixed bottom-2 right-2 z-50">
        <ChatWidget defaultMinimized={false} />
      </div>
    </div>
  );
}
