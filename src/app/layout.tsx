import type { Metada<PERSON>, Viewport } from "next";
import "./globals.css";
import { ThemeProvider } from "@/providers/ThemeProvider";
import { Web3Provider } from "@/providers/Web3Provider";
import { LoggingProvider } from "@/providers/LoggingProvider";
import { Toaster } from "sonner";
import { BackgroundPattern } from "@/components/BackgroundPattern";
import { Footer } from "@/components/Footer";
import { montserrat } from "@/lib/fonts";
import { NavBar } from "@/components/NavBar";
import { TooltipProvider } from "@radix-ui/react-tooltip";
import { DisclaimerPopup } from "@/components/DisclaimerPopup";
import { PWAInstallPrompt } from "@/components/pwa/PWAInstallPrompt";
import { ServiceWorkerRegistration } from "@/components/pwa/ServiceWorkerRegistration";


export const metadata: Metadata = {
  title: "LiquidLaunch - Hyperliquid and HyperEVM Fair Launchpad",
  description:
    "Fair launch platform built for HyperEVM and Hyperliquid. Create tokens, seed liquidity, and build a community.",
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "https://liquidlaunch.app"),
  keywords: [
    "LiquidLaunch",
    "Hyperliquid",
    "HyperEVM",
    "HyperCore",
    "fair launchpad",
    "token launch",
    "liquidity generation",
    "community building",
    "chat rooms",
    "fair launch hyperevm",
    "fair launch hyperliquid",
    "fair launch hypercore",
    "fair launch kittenswap",
    "fair launch hyperswap",
    "fair launch laminar",
  ],
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "LiquidLaunch",
    startupImage: [
      {
        url: "/icons/icon-512x512.png",
        media: "(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)",
      },
      {
        url: "/icons/icon-512x512.png", 
        media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)",
      },
      {
        url: "/icons/icon-512x512.png",
        media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)", 
      },
    ],
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: "LiquidLaunch",
    title: "LiquidLaunch - Hyperliquid and HyperEVM Fair Launchpad",
    description:
      "Fair launch platform built for HyperEVM and Hyperliquid. Create tokens, seed liquidity, and build a community.",
  },
  twitter: {
    card: "summary",
    title: "LiquidLaunch - Hyperliquid and HyperEVM Fair Launchpad",
    description:
      "Fair launch platform built for HyperEVM and Hyperliquid. Create tokens, seed liquidity, and build a community.",
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "black-translucent",
    "apple-mobile-web-app-title": "LiquidLaunch",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#10B981" },
    { media: "(prefers-color-scheme: dark)", color: "#10B981" },
  ],
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  // Also supported by less commonly used
  // interactiveWidget: 'resizes-visual',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning className={montserrat.variable}>
      <body className={montserrat.className}>
        <ServiceWorkerRegistration />
        <LoggingProvider>
          <TooltipProvider delayDuration={100}>
            <ThemeProvider>
              <BackgroundPattern opacity="medium">
                <Web3Provider>
                  <div className="min-h-screen flex flex-col">
                    <NavBar />
                    <main className="flex-1 overflow-y-auto sm:px-6 px-3.5 py-4">{children}</main>
                    <Footer />
                  </div>

                  <DisclaimerPopup />

                  <PWAInstallPrompt />
                  
                </Web3Provider>
              </BackgroundPattern>
            </ThemeProvider>
          </TooltipProvider>
        </LoggingProvider>
        <Toaster
          position="bottom-right"
          theme="system"
          className="dark:bg-secondary dark:text-foreground dark:border-border"
          closeButton
          richColors
          expand={true}
          visibleToasts={5}
          toastOptions={{
            duration: 5000,
            classNames: {
              toast: "bg-background border border-border text-foreground",
              title: "text-foreground",
              description: "text-muted-foreground",
              actionButton: "bg-primary text-primary-foreground hover:bg-primary/90",
              cancelButton: "bg-secondary text-secondary-foreground hover:bg-secondary/90",
              // Not sure if these sytles actually work
              closeButton: "bg-background border border-border text-foreground hover:bg-destructive",
            },
          }}
        />
      </body>
    </html>
  );
}
