@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
    --dropdown: 0 0% 100%;
    --dropdown-hover: 142.1 76.2% 36.3%;
    --dropdown-active: 142.1 76.2% 36.3%;
    --subtext: 220 8.9% 46.1%;
    --chat: 220 14.3% 95.9%;
    --link: 142.1 76.2% 36.3%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-darker: 217.2deg 31.57% 15.39%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --border-bright: 217.2deg 24.76% 35.89% / 58%;

    --input: 217.2 32.6% 17.5%;
    --ring: 142.4 71.8% 29.2%;
    --dropdown: 222.2 84% 4.9%;
    --dropdown-hover: 142.1 70.6% 45.3%;
    --dropdown-active: 142.1 70.6% 45.3%;
    --subtext: 215 20.2% 65.1%;
    --chat: 222.2 84% 4.9%;
    --link: 142.1 70.6% 45.3%;
    --chart-1: 220 70% 70%;
    --chart-2: 160 60% 65%;
    --chart-3: 30 80% 75%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "ss01", "ss02", "cv01", "cv03";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4 {
    @apply font-medium tracking-tight;
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}

@layer utilities {
  .gradient-border {
    border-width: 1px;
    border-style: solid;
    border-image: linear-gradient(to right, #10b981, #0d9488) 1;
  }

  .gradient-border-bottom {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-image: linear-gradient(to right, #10b981, #0d9488) 1;
  }

  .gradient-border-transparent {
    border-image: linear-gradient(to right, transparent, #10b981, transparent) 1;
  }

  .glow-sm {
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
  }

  .glow {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }

  .glow-lg {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
  }
}

/* Background Pattern Styles */
.bg-pattern-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
}

.bg-pattern {
  position: fixed;
  inset: 0;
  z-index: -1;
  overflow: hidden; /* Ensure the video stays within bounds */
}

/* Video background opacity controls */
.bg-pattern video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.bg-pattern-light video {
  opacity: 0.05;
}

.bg-pattern-medium video {
  opacity: 0.1;
}

.bg-pattern-dark video {
  opacity: 0.2;
}

.bg-pattern-content {
  position: relative;
  z-index: 0;
}

.animate-in {
  animation: animateIn 0.3s ease 0.15s both;
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes jiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(-1deg) scale(1.02);
  }
  20% {
    transform: rotate(1deg) scale(1.02);
  }
  30% {
    transform: rotate(-1deg) scale(1.02);
  }
  40% {
    transform: rotate(1deg) scale(1.02);
  }
  50% {
    transform: rotate(-0.5deg) scale(1.01);
  }
  60% {
    transform: rotate(0.5deg) scale(1.01);
  }
  70% {
    transform: rotate(-0.5deg) scale(1.01);
  }
  80% {
    transform: rotate(0.5deg) scale(1.01);
  }
  90% {
    transform: rotate(0deg) scale(1.005);
  }
}

.animate-jiggle {
  animation: jiggle 0.8s ease-in-out;
}

/* Sonner Toast Customizations */
[data-sonner-toast] {
  @apply border !important;
}

[data-sonner-toast][data-type="error"] {
  @apply border-destructive bg-background text-destructive-foreground !important;
}

[data-sonner-toast][data-type="error"] [data-title] {
  @apply text-destructive !important;
}

[data-sonner-toast][data-type="error"] [data-description] {
  @apply text-destructive/90 !important;
}

/* Optional: Style icon if sonner uses one */
[data-sonner-toast][data-type="error"] [data-icon] svg {
  @apply text-destructive !important;
}

/* Add styles for other types if desired */
[data-sonner-toast][data-type="success"] {
  @apply border-primary bg-background !important;
}
[data-sonner-toast][data-type="success"] [data-title] {
  @apply text-primary !important;
}
[data-sonner-toast][data-type="success"] [data-description] {
  @apply text-primary/90 !important;
}
[data-sonner-toast][data-type="success"] [data-icon] svg {
  @apply text-primary !important;
}

/* Add more styles for warning, info, etc. as needed */

/* Ensure links within toasts are styled appropriately */
[data-sonner-toast] a {
  @apply text-link hover:underline !important;
}

[data-sonner-toast][data-type="error"] a {
  @apply text-destructive hover:text-destructive/80 underline !important;
}

[data-sonner-toast][data-type="success"] a {
  @apply text-primary hover:text-primary/80 underline !important;
}
