import { NextRequest, NextResponse } from "next/server";
import { createPublicClient, http, parseAbiItem, type Address } from "viem";
import { TOKENS, HYPER_EVM_CHAIN } from "@/lib/constants";
import ERC20_ABI from "@/lib/contracts/erc20.abi.json";
import { contractCallWithRetry } from "@/lib/retryUtils";
export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    // Get wallet parameter from the request
    const wallet = request.nextUrl.searchParams.get("wallet");
    const tokenAddresses = request.nextUrl.searchParams.get("tokens");

    if (!wallet) {
      return NextResponse.json({ success: false, message: "Missing wallet parameter" }, { status: 400 });
    }

    if (!tokenAddresses) {
      return NextResponse.json({ success: false, message: "Missing tokens parameter" }, { status: 400 });
    }

    // Create a public client for blockchain interactions
    const publicClient = createPublicClient({
      chain: HYPER_EVM_CHAIN,
      transport: http(process.env.NEXT_PUBLIC_RPC_URL),
    });

    const tokens = tokenAddresses.split(",");
    const results = [];

    // Fetch balances for each token
    for (const tokenAddress of tokens) {
      try {
        // Skip if token address is empty or invalid
        if (!tokenAddress || tokenAddress === "undefined") continue;

        // Handle native token (HYPE) separately
        if (tokenAddress.toLowerCase() === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase()) {
          const nativeBalance = await contractCallWithRetry(() =>
            publicClient.getBalance({
              address: wallet as Address,
            }),
          );

          results.push({
            token: "Native HYPE",
            balance: nativeBalance.toString(),
            name: "Hyperliquid",
            symbol: "HYPE",
            decimals: 18,
          });
          continue;
        }

        // Get token balance using contract read with retry
        const balance = await contractCallWithRetry(
          () =>
            publicClient.readContract({
              address: tokenAddress as Address,
              abi: ERC20_ABI,
              functionName: "balanceOf",
              args: [wallet as Address],
            }) as Promise<bigint>,
        );

        // Get token metadata with retry logic
        const decimalsAbi = parseAbiItem("function decimals() view returns (uint8)");
        const nameAbi = parseAbiItem("function name() view returns (string)");
        const symbolAbi = parseAbiItem("function symbol() view returns (string)");

        const decimals = await contractCallWithRetry(
          () =>
            publicClient.readContract({
              address: tokenAddress as Address,
              abi: [decimalsAbi],
              functionName: "decimals",
            }) as Promise<number>,
        );

        const name = await contractCallWithRetry(
          () =>
            publicClient.readContract({
              address: tokenAddress as Address,
              abi: [nameAbi],
              functionName: "name",
            }) as Promise<string>,
        );

        const symbol = await contractCallWithRetry(
          () =>
            publicClient.readContract({
              address: tokenAddress as Address,
              abi: [symbolAbi],
              functionName: "symbol",
            }) as Promise<string>,
        );

        results.push({
          token: tokenAddress,
          balance: balance.toString(),
          name: name,
          symbol: symbol,
          decimals: decimals,
        });
      } catch (error) {
        console.error(`Error fetching balance for token ${tokenAddress}:`, error);
        // Continue with other tokens on error
      }
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          tokens: results,
        },
      },
      {
        headers: {
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0, s-maxage=0",
          Pragma: "no-cache",
          Expires: "0",
          "Surrogate-Control": "no-store",
          "CDN-Cache-Control": "no-store",
          Vary: "Authorization, X-Requested-With",
          "Last-Modified": new Date().toUTCString(),
          ETag: `"${Date.now()}"`,
        },
      },
    );
  } catch (error) {
    console.error("Specific token balances API error:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "An unknown error occurred",
      },
      { status: 500 },
    );
  }
}
