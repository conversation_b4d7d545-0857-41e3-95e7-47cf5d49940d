// Ripped straight from Liqd.ag repo - https://github.com/Seipex-Finance/liquidswap-frontend/blob/dev/src/app/api/swap/quote/route.ts

import { NextResponse } from "next/server";
// Import all necessary types from the updated swap.ts
import type {
  SwapQuote,
  SwapPath,
  SwapImprovement,
  DexComparison,
  ApiResponse,
  ApiAllocation,
  ApiSinglePool,
  ApiHop,
  QuoteHopInfo,
} from "@/types/swap";
import { TOKENS } from "@/lib/constants";
import { getFeeDisplayForAllocation, getFeeDisplayForDexComparison } from "@/lib/dexConstants";
import { fetchWithRetry } from "@/lib/retryUtils";

export const dynamic = "force-dynamic";

const API_BASE_URL = process.env.QUOTE_API_BASE_URL;

// Removed old inline interfaces - now imported from @/types/swap

export async function GET(request: Request) {
  const params = new URL(request.url).searchParams;

  // Check for required parameters
  const inputToken = params.get("inputToken");
  const outputToken = params.get("outputToken");
  const amount = params.get("amount");
  const amountOut = params.get("amountOut");
  const excludeDexes = params.get("excludeDexes");

  if (!inputToken || !outputToken) {
    return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
  }

  if (!amount && !amountOut) {
    return NextResponse.json({ error: "Either amount or amountOut must be provided" }, { status: 400 });
  }

  try {
    // Always request multi-hop capability from the backend
    const url = new URL(`${API_BASE_URL}/route?multiHop=true`);

    url.searchParams.append("tokenA", inputToken);
    url.searchParams.append("tokenB", outputToken);

    if (amount) {
      url.searchParams.append("amountIn", amount);
    } else if (amountOut) {
      url.searchParams.append("amountOut", amountOut);
    }

    if (excludeDexes) {
      url.searchParams.append("excludeDexes", excludeDexes);
    }

    const response = await fetchWithRetry(url.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const apiResponse: ApiResponse = await response.json();

    if (!apiResponse.success || !apiResponse.data || apiResponse.data.status !== "success") {
      console.error("API returned error or unsuccessful status:", apiResponse);
      return NextResponse.json(
        {
          success: false,
          error: apiResponse.error || "Failed to fetch swap quote due to insufficient liquidity or API error",
        },
        { status: response.status !== 200 ? response.status : 400 }, // Use API status or default to 400
      );
    }

    const { tokenInfo, bestPath, singlePools } = apiResponse.data;

    // --- Data Transformation ---

    let transformedPaths: SwapPath[] = [];
    let transformedHops: QuoteHopInfo[] | undefined = undefined;

    // Check if the response is multi-hop (has 'hop' array)
    if (bestPath.hop && bestPath.hop.length > 0) {
      transformedPaths = bestPath.hop.flatMap((hop: ApiHop) =>
        hop.allocations.map((alloc: ApiAllocation) => transformAllocation(alloc)),
      );
      // Also transform hop info for display purposes
      transformedHops = bestPath.hop.map(
        (hop: ApiHop): QuoteHopInfo => ({
          hopAmountIn: hop.hopAmountIn,
          hopAmountOut: hop.hopAmountOut,
          tokenIn: hop.tokenIn,
          tokenOut: hop.tokenOut,
        }),
      );
    }
    // Check if the response is single-leg (has 'allocations' array directly under bestPath)
    else if (bestPath.allocations && bestPath.allocations.length > 0) {
      // Add tokenIn/tokenOut from the main tokenInfo for single-leg allocations
      transformedPaths = bestPath.allocations.map((alloc: ApiAllocation) =>
        transformAllocation({
          ...alloc,
          tokenIn: tokenInfo.tokenIn.address,
          tokenOut: tokenInfo.tokenOut.address,
        }),
      );
    } else {
      console.warn("API response has no valid path data (neither hop nor allocations)");
    }

    if (transformedPaths.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "No valid swap paths found in API response",
        },
        { status: 400 },
      );
    }

    const transformedImprovement = singlePools.length > 0 ? transformImprovement(singlePools[0]) : undefined;
    const transformedDexComparisons = singlePools.map(transformDexComparison);

    const transformedData: SwapQuote = {
      success: true,
      timestamp: new Date().toISOString(),
      inputToken: tokenInfo.tokenIn.address,
      outputToken: tokenInfo.tokenOut.address,
      // Add intermediate token info if present
      intermediateTokenSymbol: tokenInfo.intermediate?.symbol,
      intermediateTokenAddress: tokenInfo.intermediate?.address,
      intermediateTokenDecimals: tokenInfo.intermediate?.decimals,
      totalAmountIn: tokenInfo.amountIn,
      inputTokenSymbol: tokenInfo.tokenIn.symbol,
      inputTokenDecimals: tokenInfo.tokenIn.decimals,
      outputTokenDecimals: tokenInfo.tokenOut.decimals,
      estimatedTotalOutput: bestPath.amountOut,
      paths: transformedPaths, // Use the processed paths
      hops: transformedHops, // Add transformed hop info
      improvement: transformedImprovement,
      dexComparisons: transformedDexComparisons,
      isAmountOut: !!amountOut,
    };

    return NextResponse.json(transformedData, {
      headers: {
        "Cache-Control": "no-store, must-revalidate, max-age=0",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    console.error("Swap quote GET handler error:", error);
    // Check if error is a FetchError or similar network issue
    const errorMessage = error instanceof Error ? error.message : "Internal server error";
    return NextResponse.json({ success: false, error: `Failed to fetch swap quote: ${errorMessage}` }, { status: 500 });
  }
}

// --- Helper Functions ---

// Updated to handle ApiAllocation which now includes tokenIn/tokenOut
function transformAllocation(allocation: ApiAllocation): SwapPath {
  // Ensure percentage is a string for the frontend type
  const percentageString =
    typeof allocation.percentage === "number" ? allocation.percentage.toString() : allocation.percentage;

  return {
    routerIndex: allocation.routerIndex,
    routerName: allocation.routerName,
    fee: allocation.fee,
    feeDisplay: getFeeDisplayForAllocation(allocation.fee, allocation.routerName, allocation.stable),
    amountIn: allocation.amountIn,
    percentage: percentageString,
    poolAddress: allocation.poolAddress,
    stable: allocation.stable,
    tokenIn: allocation.tokenIn, // Pass through tokenIn from API allocation
    tokenOut: allocation.tokenOut, // Pass through tokenOut from API allocation
  };
}

function transformImprovement(bestPool: ApiSinglePool): SwapImprovement {
  const percentage = parseFloat(bestPool.percentImprovement?.replace("%", "") || "0");
  return {
    bestSingleDex: bestPool.routerName,
    percentage: isNaN(percentage) ? 0 : percentage, // Handle potential NaN
    tokenIncrease: bestPool.improvementValue || "0",
  };
}

function transformDexComparison(pool: ApiSinglePool): DexComparison {
  const improvementPercentage = parseFloat(pool.percentImprovement?.replace("%", "") || "0");
  const safeImprovementPercentage = isNaN(improvementPercentage) ? 0 : improvementPercentage;
  return {
    dexName: pool.routerName,
    expectedOutput: pool.amountOut || "",
    expectedInput: pool.amountIn || "",
    percentOfOptimal: (100 - safeImprovementPercentage).toFixed(TOKENS.DISPLAY_DECIMALS),
    improvementOver: pool.percentImprovement || "0%",
    fee: pool.fee || "0",
    feeDisplay: getFeeDisplayForDexComparison(pool.fee, pool.routerName, pool.stable),
  };
}
