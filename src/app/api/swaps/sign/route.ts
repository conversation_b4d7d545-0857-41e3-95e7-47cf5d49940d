import { NextRequest, NextResponse } from "next/server";
import { isAddress } from "viem";

// The actual external signature API URL should come from an environment variable
const EXTERNAL_SIGNATURE_SERVICE_URL = process.env.EXTERNAL_SIGNATURE_API_URL;

const ALLOWED_TRADE_TYPES = ["buy", "sell"];
const AMOUNT_REGEX = /^(?:0|[1-9]\d*)(?:\.\d+)?$/; // Matches positive numbers (integer or decimal)

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    // Extract parameters matching SignatureService
    const { userAddress, tokenAddress, amount, userBlockNumber, tradeType } = body;

    // Validate addresses
    if (!userAddress || !isAddress(userAddress)) {
      return NextResponse.json({ success: false, message: "Invalid or missing userAddress" }, { status: 400 });
    }
    if (!tokenAddress || !isAddress(tokenAddress)) {
      return NextResponse.json({ success: false, message: "Invalid or missing tokenAddress" }, { status: 400 });
    }
    // Validate tradeType
    if (!tradeType || !ALLOWED_TRADE_TYPES.includes(tradeType)) {
      return NextResponse.json(
        { success: false, message: "Invalid or missing tradeType (must be 'buy' or 'sell')" },
        { status: 400 },
      );
    }
    // Validate amount
    if (!amount || typeof amount !== "string" || !AMOUNT_REGEX.test(amount)) {
      return NextResponse.json(
        { success: false, message: "Invalid or missing amount (must be a positive number string)" },
        { status: 400 },
      );
    }

    // Get the real client IP from headers (forward directly without anonymization)
    const clientIp = req.headers.get("cf-connecting-ip") || req.headers.get("x-forwarded-for") || "unknown";

    // Log only non-sensitive, validated data
    console.log("Transaction authorization request (NextRequest):", {
      userAddress,
      tokenAddress,
      amount: amount.length > 10 ? `${amount.substring(0, 10)}...` : amount, // Basic truncation
      userBlockNumber,
      tradeType,
      clientIp,
      timestamp: new Date().toISOString(),
    });

    // Prepare payload for external API
    const externalPayload: Record<string, unknown> = {
      address: userAddress, // The external API expects 'address'
      tokenAddress,
      amount,
      userBlockNumber,
      tradeType, // Forwarding tradeType
    };
    if (clientIp) {
      externalPayload.clientIp = clientIp; // Forward raw IP address
    }

    if (!EXTERNAL_SIGNATURE_SERVICE_URL) {
      return NextResponse.json(
        { success: false, message: "External signature service URL not configured" },
        { status: 500 },
      );
    }

    console.log(`Forwarding request to ${EXTERNAL_SIGNATURE_SERVICE_URL}`);
    const externalResponse = await fetch(EXTERNAL_SIGNATURE_SERVICE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Potentially forward other relevant headers if needed by the external service
      },
      body: JSON.stringify(externalPayload),
    });

    const responseData = await externalResponse.json();

    if (!externalResponse.ok) {
      console.error("External API error response:", {
        status: externalResponse.status,
        data: responseData,
      });
      return NextResponse.json(
        {
          success: false,
          message: responseData.message || "Error from authorization service",
          error: responseData,
        },
        { status: externalResponse.status },
      );
    }

    console.log("Transaction authorization successful:", {
      userAddress,
      tokenAddress,
      hasSignature: !!responseData.signature,
      userBlockNumber: responseData.userBlockNumber, // Assuming external API might return this
      tradeType: responseData.tradeType, // Assuming external API might return this
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(responseData, { status: 200 });
  } catch (error: unknown) {
    console.error("Error in transaction authorization API (NextRequest):", error);

    console.log(error);

    // Generic error for issues not related to the external API call itself (e.g., JSON parsing failed)
    if (error instanceof SyntaxError) {
      return NextResponse.json({ success: false, message: "Invalid JSON payload" }, { status: 400 });
    }

    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
