/* eslint-disable no-var */
import { http } from "wagmi";
import { define<PERSON><PERSON><PERSON> } from "viem";
import { createConfig } from "wagmi";
import { connectorsForWallets } from "@rainbow-me/rainbowkit";
import { HYPER_EVM_CHAIN } from "@/lib/constants";
// Import specific wallets including Rabby
import {
  metaMaskWallet,
  rainbowWallet,
  coinbaseWallet,
  rabbyWallet,
  walletConnectWallet,
} from "@rainbow-me/rainbowkit/wallets";

// Define the HyperEVM chain
export const hyperEvm = defineChain(HYPER_EVM_CHAIN);

// Use a persistent projectId to prevent reconnections
const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID;

// Create a true global singleton to survive hot reloads
// Use the global object as a registry to preserve state between module evaluations
declare global {
  var __liquidswap_wagmi_config: ReturnType<typeof createConfig> | undefined;
  var __liquidswap_wallets_initialized: boolean | undefined;
}

// Safe initialization function for WalletConnect
function createWagmiConfig() {
  // Check if we've already created the wallets and connectors to avoid double init
  if (!global.__liquidswap_wallets_initialized) {
    console.log("[wagmi] First-time initialization of wallets");
    global.__liquidswap_wallets_initialized = true;
  } else {
    console.log("[wagmi] Using existing wallet initialization");
    // If we've already initialized wallets, just return the existing config
    if (global.__liquidswap_wagmi_config) {
      return global.__liquidswap_wagmi_config;
    }
  }

  // Create custom wallet list with Rabby included
  const connectors = connectorsForWallets(
    [
      {
        groupName: "Recommended",
        wallets: [rabbyWallet, metaMaskWallet, coinbaseWallet, rainbowWallet],
      },
      {
        groupName: "Others",
        wallets: [walletConnectWallet],
      },
    ],
    {
      appName: "LiquidLaunch",
      projectId: projectId!,
    },
  );

  // Create the config
  console.log("[wagmi] Creating new config");
  const newConfig = createConfig({
    chains: [hyperEvm],
    connectors,
    transports: {
      [hyperEvm.id]: http(),
    },
  });

  // Store in global registry
  global.__liquidswap_wagmi_config = newConfig;
  return newConfig;
}

// Export a stable config
export const config =
  typeof window !== "undefined" ? global.__liquidswap_wagmi_config || createWagmiConfig() : createWagmiConfig();
