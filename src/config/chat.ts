export const CHAT_CONFIG = {
  WEBSOCKET_URL: process.env.NEXT_PUBLIC_CHAT_WEBSOCKET_URL,
  API_BASE_URL: process.env.NEXT_PUBLIC_API_CHAT_SERVER_URL,

  // Room IDs
  GLOBAL_ROOM_ID: "0x0000000000000000000000000000000000000000",

  // Message limits
  MAX_MESSAGE_LENGTH: 500,
  MAX_MESSAGES_PER_ROOM: 1000,
  MESSAGE_HISTORY_LIMIT: 50,

  // UI Configuration
  DEFAULT_WINDOW_SIZE: { width: 360, height: 600 },
  MIN_WINDOW_SIZE: { width: 300, height: 300 },
  MAX_WINDOW_SIZE: { width: 800, height: 700 },
  MAX_WINDOWS: 5,

  // Timing
  TYPING_TIMEOUT: 3000, // 3 seconds
  RECONNECT_INTERVAL: 5000, // 5 seconds
  MESSAGE_RETRY_TIMEOUT: 10000, // 10 seconds

  // Features
  ENABLE_GIFS: true,
  ENABLE_REACTIONS: true,
  ENABLE_REPLIES: true,
  ENABLE_TYPING_INDICATORS: true,

  // Rate limiting
  MAX_MESSAGES_PER_MINUTE: 30,
  TYPING_DEBOUNCE_MS: 300,
} as const;

export const ROOM_TYPES = {
  GLOBAL: "global",
  TOKEN: "token",
  PRIVATE: "private",
} as const;

export const MESSAGE_TYPES = {
  TEXT: "text",
  GIF: "gif",
  SYSTEM: "system",
} as const;

export const NOTIFICATION_TYPES = {
  MENTION: "mention",
  REPLY: "reply",
  REACTION: "reaction",
  SYSTEM: "system",
} as const;

// Default chat rooms
export const DEFAULT_ROOMS = [
  {
    id: CHAT_CONFIG.GLOBAL_ROOM_ID,
    tokenAddress: CHAT_CONFIG.GLOBAL_ROOM_ID,
    name: "Global",
    type: "global" as const,
    participantCount: 0,
  },
];

// Helper functions for room IDs
export const getRoomId = {
  global: () => CHAT_CONFIG.GLOBAL_ROOM_ID,
  token: (tokenAddress: string) => tokenAddress.toLowerCase(),
  private: (userId1: string, userId2: string) => {
    const sorted = [userId1, userId2].sort();
    return `private:${sorted[0]}:${sorted[1]}`;
  },
};

export const parseRoomId = (roomId: string) => {
  if (roomId === CHAT_CONFIG.GLOBAL_ROOM_ID) {
    return { type: "global" as const };
  }

  if (roomId.startsWith("0x") && roomId.length === 42) {
    return {
      type: "token" as const,
      tokenAddress: roomId.toLowerCase(),
    };
  }

  if (roomId.startsWith("private:")) {
    const parts = roomId.replace("private:", "").split(":");
    return {
      type: "private" as const,
      userIds: parts,
    };
  }

  throw new Error(`Invalid room ID format: ${roomId}`);
};
