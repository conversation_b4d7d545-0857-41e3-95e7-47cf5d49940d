import { useState, useEffect, useCallback } from "react";
import { getBondedTokenDetails } from "@/services/dexscreenerService";
import { TokenDetails, BondedTokenDetails } from "@/lib/api/types";
import { useDexScreenerUpdater } from "@/hooks/useUpdater";

interface UseBondedTokenDetailsProps {
  tokenAddress: string;
  enabled?: boolean;
  originalTokenDetails?: TokenDetails | null;
}

interface UseBondedTokenDetailsReturn {
  bondedTokenDetails: TokenDetails | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to fetch comprehensive token details for bonded tokens from DexScreener
 * and transform them to match the standard TokenDetails interface.
 */
export const useBondedTokenDetails = ({
  tokenAddress,
  enabled = true,
  originalTokenDetails,
}: UseBondedTokenDetailsProps): UseBondedTokenDetailsReturn => {
  const [bondedTokenDetails, setBondedTokenDetails] = useState<TokenDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Transform BondedTokenDetails from DexScreener to TokenDetails interface
   */
  const transformBondedTokenDetails = useCallback(
    (dexData: BondedTokenDetails, originalTokenDetails?: TokenDetails | null): TokenDetails => {
      // Use original token details as base and overlay DexScreener data
      const baseDetails = originalTokenDetails || {
        address: dexData.address,
        name: dexData.name,
        symbol: dexData.symbol,
        decimals: 18, // Default, will be overridden if original exists
        creator: "", // Will be overridden if original exists
        creationTimestamp: dexData.pairCreatedAt || Date.now() / 1000,
        totalSupply: "0", // Will be overridden if original exists
        holderCount: "0", // Will be overridden if original exists
        holders: [], // Will be overridden if original exists
        top10HoldersPercentage: "0", // Will be overridden if original exists
        isBonded: true,
        isFrozen: false,
        pair: dexData.pairAddress,
        latestActivityTimestamp: Date.now() / 1000,
      };

      return {
        ...baseDetails,
        // Always preserve blockchain data from original token details
        creator: originalTokenDetails?.creator || baseDetails.creator,
        decimals: originalTokenDetails?.decimals || baseDetails.decimals,
        totalSupply: originalTokenDetails?.totalSupply || baseDetails.totalSupply,
        holderCount: originalTokenDetails?.holderCount || baseDetails.holderCount,
        holders: originalTokenDetails?.holders || baseDetails.holders,
        top10HoldersPercentage: originalTokenDetails?.top10HoldersPercentage || baseDetails.top10HoldersPercentage,
        creationTimestamp: originalTokenDetails?.creationTimestamp || baseDetails.creationTimestamp,
        isFrozen: originalTokenDetails?.isFrozen || baseDetails.isFrozen,

        // Override with DexScreener data - this is the key part
        metadata: {
          name: dexData.name,
          symbol: dexData.symbol,
          image_uri: dexData.metadata.imageUrl,
          website: dexData.metadata.websites?.[0]?.url,
          twitter: dexData.metadata.socials?.find((s) => s.type === "twitter")?.url,
          telegram: dexData.metadata.socials?.find((s) => s.type === "telegram")?.url,
          discord: dexData.metadata.socials?.find((s) => s.type === "discord")?.url,
          // Preserve original metadata fields that DexScreener doesn't provide
          description: originalTokenDetails?.metadata?.description,
          ...(originalTokenDetails?.metadata && {
            // Only add fields that don't conflict with DexScreener data
            ...(originalTokenDetails.metadata.description && {
              description: originalTokenDetails.metadata.description,
            }),
          }),
        },
        // ALWAYS use DexScreener price data for bonded tokens
        price: {
          usd: dexData.price.usd.toString(),
          hype: dexData.price.native.toString(),
        },
        // ALWAYS use DexScreener market cap for bonded tokens
        marketCap: {
          usd: dexData.marketCap.usd.toString(),
          hype: (dexData.marketCap.usd / Math.max(dexData.price.native, 0.000001)).toString(),
        },
        // ALWAYS use DexScreener liquidity for bonded tokens
        liquidity: {
          usd: dexData.liquidity.usd.toString(),
          hype: dexData.liquidity.base.toString(),
        },
        // ALWAYS use DexScreener financials for bonded tokens
        financials: {
          price: {
            usd: dexData.price.usd.toString(),
            hype: dexData.price.native.toString(),
          },
          marketCap: {
            usd: dexData.marketCap.usd.toString(),
            hype: (dexData.marketCap.usd / Math.max(dexData.price.native, 0.000001)).toString(),
          },
          liquidity: {
            usd: dexData.liquidity.usd.toString(),
            hype: dexData.liquidity.base.toString(),
          },
          volume24h: {
            usd: dexData.volume.h24.toString(),
            hype: (dexData.volume.h24 / Math.max(dexData.price.native, 0.000001)).toString(),
          },
          priceChange24h: dexData.priceChange.h24.toString(),
          trades24h: {
            buys: Math.round(dexData.trades.h24 * 0.6), // Approximate split
            sells: Math.round(dexData.trades.h24 * 0.4),
            total: dexData.trades.h24,
          },
        },
        timeframes: {
          "24h": {
            buys: Math.round(dexData.trades.h24 * 0.6),
            sells: Math.round(dexData.trades.h24 * 0.4),
            volume: dexData.volume.h24,
            priceChange: dexData.priceChange.h24,
          },
        },
        // For bonded tokens, ALWAYS use DexScreener data for bonding info
        bonding: {
          progress: "100", // Always 100% for bonded tokens
          hypeProgress: "100",
          tokensSold: "100",
          tokensSoldPercentage: "100",
          hypeNeededFor100: "0", // Already bonded
          currentHypeInPool: dexData.liquidity.base.toString(),
          bondsAtMcap: dexData.marketCap.usd.toString(), // Use current market cap, not original bond target
          isBonded: true,
          constants: originalTokenDetails?.bonding?.constants || {
            initialSupply: "1000000000",
            targetReserves: "0",
            targetBought: "0",
            minHypeReserves: "0",
            maxHypeReserves: "0",
            targetMcapHypeForBonding: dexData.marketCap.usd.toString(), // Use current market cap
          },
        },
        supplyData: originalTokenDetails?.supplyData || {
          tokenReserves: "0",
          hypeReserves: dexData.liquidity.base.toString(),
          totalSupply: "1000000000",
          holders: "0",
        },
      };
    },
    [],
  );

  const fetchBondedTokenDetails = useCallback(async () => {
    // Early return if not enabled or no token address
    if (!enabled || !tokenAddress) {
      console.log("[useBondedTokenDetails] Skipping fetch - disabled or no address:", { tokenAddress, enabled });
      return;
    }

    // Additional safety check - only fetch for bonded tokens
    const isBonded = originalTokenDetails?.isBonded;
    if (isBonded === false) {
      console.log("[useBondedTokenDetails] Skipping fetch - token is not bonded:", tokenAddress);
      return;
    }

    console.log(`[useBondedTokenDetails] Fetching DexScreener data for bonded token ${tokenAddress}...`);
    setIsLoading(true);
    setError(null);

    try {
      const dexData = await getBondedTokenDetails(tokenAddress);

      if (dexData) {
        const transformedData = transformBondedTokenDetails(dexData, originalTokenDetails);
        console.log("[useBondedTokenDetails] Successfully transformed data with creator:", {
          creator: transformedData.creator,
          marketCap: transformedData.marketCap.usd,
          volume24h: transformedData.financials.volume24h.usd,
        });
        setBondedTokenDetails(transformedData);
      } else {
        // If no DexScreener data found, fall back to original data
        console.warn(`[useBondedTokenDetails] No DexScreener data found for token: ${tokenAddress}`);
        setBondedTokenDetails(originalTokenDetails || null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch bonded token details";
      setError(errorMessage);
      console.error("[useBondedTokenDetails] Error fetching bonded token details:", err);

      // Fall back to original data on error
      setBondedTokenDetails(originalTokenDetails || null);
    } finally {
      setIsLoading(false);
    }
  }, [tokenAddress, enabled, transformBondedTokenDetails, originalTokenDetails]);

  const refetch = useCallback(async () => {
    await fetchBondedTokenDetails();
  }, [fetchBondedTokenDetails]);

  // Only fetch data when enabled and token address exists
  useEffect(() => {
    if (enabled && tokenAddress) {
      fetchBondedTokenDetails();
    }
  }, [fetchBondedTokenDetails, enabled, tokenAddress]);

  // Setup real-time polling only for bonded tokens
  useDexScreenerUpdater(tokenAddress || "", fetchBondedTokenDetails, {
    enabled: !!tokenAddress && enabled && originalTokenDetails?.isBonded !== false,
    interval: 5000,
    autoStart: !!tokenAddress && enabled && originalTokenDetails?.isBonded !== false,
  });

  return {
    bondedTokenDetails,
    isLoading,
    error,
    refetch,
  };
};
