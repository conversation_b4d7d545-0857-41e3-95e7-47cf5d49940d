import { useState, useEffect, useCallback } from "react";
import { useAccount, useWalletClient, usePublicClient } from "wagmi";
import { type Address, type Hex, parseUnits, formatUnits } from "viem";
import { toast } from "sonner";
import { createSwapErrorMessage } from "@/lib/utils/error-messages";
import { TOKENS } from "@/lib/constants";
import { useBalanceStore } from "@/stores/balanceStore";
import { MULTIHOP_ROUTER_ABI } from "@/lib/contracts/abi";

// Standard ERC20 ABI for approve, allowance, and balanceOf functions
const ERC20_ABI = [
  {
    name: "approve",
    type: "function",
    stateMutability: "nonpayable",
    inputs: [
      { name: "spender", type: "address" },
      { name: "amount", type: "uint256" },
    ],
    outputs: [{ name: "", type: "bool" }],
  },
  {
    name: "allowance",
    type: "function",
    stateMutability: "view",
    inputs: [
      { name: "owner", type: "address" },
      { name: "spender", type: "address" },
    ],
    outputs: [{ name: "", type: "uint256" }],
  },
  {
    name: "balanceOf",
    type: "function",
    stateMutability: "view",
    inputs: [{ name: "account", type: "address" }],
    outputs: [{ name: "", type: "uint256" }],
  },
] as const;

// Types for bonded token swaps (simplified version of the aggregator types)
interface BondedSwap {
  tokenIn: `0x${string}`;
  tokenOut: `0x${string}`;
  routerIndex: number;
  fee: number;
  amountIn: bigint;
  stable: boolean;
}

// Import the existing SwapQuote type from your DEX aggregator
import type { SwapQuote } from "@/types/swap";

// Utility function to parse quote output consistently
const parseQuoteOutput = (output: string | number | bigint, decimals: number): bigint => {
  try {
    if (typeof output === "bigint") {
      return output;
    }

    if (typeof output === "number") {
      return parseUnits(output.toString(), decimals);
    }

    if (typeof output === "string") {
      if (output.includes(".")) {
        // Handle decimal strings - parse as float first
        const decimalValue = parseFloat(output);
        return parseUnits(decimalValue.toString(), decimals);
      } else {
        // Handle integer strings - convert directly
        return parseUnits(output, decimals);
      }
    }

    throw new Error(`Unsupported output type: ${typeof output}`);
  } catch (error) {
    throw new Error(
      `Failed to parse quote output "${output}": ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

interface UseBondedTokenSwapProps {
  inputAmountRaw: string;
  isBuy: boolean;
  tokenAddress: Address;
  hypeTokenDecimals: number;
  targetTokenDecimals: number;
  hypeBalance?: bigint;
  targetTokenBalance?: bigint;
  displayTargetSymbol: string;
  slippagePercent: number;
  onTransactionSuccess: (txHash: Hex) => void;
  isBonded: boolean;
}

interface UseBondedTokenSwapResult {
  handleSwap: () => Promise<void>;
  isProcessing: boolean;
  isConfirming: boolean;
  uiError: string | null;
  currentTxHash: Hex | null;
  isLoadingButton: boolean;
  swapButtonText: string;
  isAmountValid: boolean;
  clearError: () => void;
  estimatedOutput: string;
  isLoadingEstimate: boolean;
  bondedTokenBalance?: bigint;
}

// Placeholder router contract details - replace with actual values
const MULTIHOP_ROUTER_ADDRESS = process.env.NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS as `0x${string}`;

if (!MULTIHOP_ROUTER_ADDRESS) {
  throw new Error("NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS environment variable must be set");
}

export function useBondedTokenSwap({
  inputAmountRaw,
  isBuy,
  tokenAddress,
  hypeTokenDecimals,
  targetTokenDecimals,
  hypeBalance,
  targetTokenBalance,
  displayTargetSymbol,
  slippagePercent,
  onTransactionSuccess,
  isBonded,
}: UseBondedTokenSwapProps): UseBondedTokenSwapResult {
  const { address: userAddress, isConnected } = useAccount();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();

  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isConfirming, setIsConfirming] = useState<boolean>(false);
  const [uiError, setUiError] = useState<string | null>(null);
  const [currentTxHash, setCurrentTxHash] = useState<Hex | null>(null);
  const [estimatedOutput, setEstimatedOutput] = useState<string>("");
  const [isLoadingEstimate, setIsLoadingEstimate] = useState<boolean>(false);
  const [lastSuccessfulQuote, setLastSuccessfulQuote] = useState<SwapQuote | null>(null);
  const [isApproving, setIsApproving] = useState<boolean>(false);
  const [needsApproval, setNeedsApproval] = useState<boolean>(false);
  const [bondedTokenBalance, setBondedTokenBalance] = useState<bigint | undefined>(undefined);

  const { updateSpecificTokenBalances } = useBalanceStore();

  // Amount to swap, parsed from raw input string
  let amountToSwap: bigint = BigInt(0);
  let isAmountValid = false;
  try {
    amountToSwap = parseUnits(inputAmountRaw.trim() || "0", isBuy ? hypeTokenDecimals : targetTokenDecimals);
    isAmountValid = amountToSwap > BigInt(0);
  } catch {
    // keep isAmountValid = false
  }

  // For bonded tokens, we simplify the swap to always be:
  // Buy: Native HYPE -> Target Token
  // Sell: Target Token -> Native HYPE
  const inputToken = isBuy ? TOKENS.WHYPE_ADDRESS : tokenAddress;
  const outputToken = isBuy ? tokenAddress : TOKENS.WHYPE_ADDRESS;

  // Get swap quote for bonded token using existing DEX aggregator
  const getSwapQuote = useCallback(
    async (amount: bigint): Promise<SwapQuote | null> => {
      if (!amount || amount <= BigInt(0)) return null;

      // Convert amount from wei to human-readable format for the API
      const inputDecimals = isBuy ? hypeTokenDecimals : targetTokenDecimals;
      const humanReadableAmount = formatUnits(amount, inputDecimals);

      try {
        setIsLoadingEstimate(true);

        const queryParams = new URLSearchParams({
          inputToken,
          outputToken,
          amount: humanReadableAmount,
        });

        const response = await fetch(`/api/swaps/quote?${queryParams.toString()}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("[useBondedTokenSwap] API error response:", errorText);
          throw new Error(`Quote failed: ${response.statusText} - ${errorText}`);
        }

        const quote: SwapQuote = await response.json();

        return quote;
      } catch (error) {
        console.error("[useBondedTokenSwap] Error getting quote:", error);
        return null;
      } finally {
        setIsLoadingEstimate(false);
      }
    },
    [inputToken, outputToken, isBuy, tokenAddress, hypeTokenDecimals, targetTokenDecimals]
  );

  // Update estimated output when input changes and on regular intervals
  useEffect(() => {
    // Guard: Only fetch quotes for bonded tokens
    if (!isBonded) {
      setEstimatedOutput("");
      setLastSuccessfulQuote(null);
      return;
    }

    if (!isAmountValid || amountToSwap <= BigInt(0)) {
      setEstimatedOutput("");
      setLastSuccessfulQuote(null);
      return;
    }

    let cancelled = false;

    const fetchQuote = async () => {
      if (cancelled) return;

      const quote = await getSwapQuote(amountToSwap);

      if (cancelled) return;

      if (quote?.success && quote.estimatedTotalOutput) {
        const outputDecimals = isBuy ? targetTokenDecimals : hypeTokenDecimals;

        try {
          const bigIntValue = parseQuoteOutput(quote.estimatedTotalOutput, outputDecimals);
          const formattedOutput = formatUnits(bigIntValue, outputDecimals);

          setEstimatedOutput(formattedOutput);
          setLastSuccessfulQuote(quote);
        } catch (conversionError) {
          console.error("[useBondedTokenSwap] Error converting output:", conversionError);
          setEstimatedOutput("");
          setUiError(
            `Error processing quote: ${conversionError instanceof Error ? conversionError.message : "Unknown error"}`
          );
        }
      } else {
        setEstimatedOutput("");
        setLastSuccessfulQuote(null);
        if (quote?.error) {
          console.error("[useBondedTokenSwap] Quote API error:", quote.error);
          setUiError(`Quote error: ${quote.error}`);
        }
      }
    };

    // Initial quote fetch
    fetchQuote().catch((error) => {
      if (!cancelled) {
        console.error("[useBondedTokenSwap] Quote error:", error);
        setEstimatedOutput("");
        setLastSuccessfulQuote(null);
      }
    });

    // Set up interval to refresh quote every 10 seconds
    const quoteInterval = setInterval(() => {
      if (!cancelled) {
        fetchQuote().catch((error) => {
          console.error("[useBondedTokenSwap] Quote refresh error:", error);
        });
      }
    }, 10000);

    return () => {
      cancelled = true;
      clearInterval(quoteInterval);
    };
  }, [
    amountToSwap,
    isAmountValid,
    isBuy,
    getSwapQuote,
    targetTokenDecimals,
    hypeTokenDecimals,
    inputAmountRaw,
    isBonded,
  ]);

  // Check if approval is needed for sell transactions
  const checkApprovalNeeded = useCallback(
    async (amount: bigint): Promise<boolean> => {
      if (!publicClient || !userAddress || isBuy || !isBonded || amount <= BigInt(0)) {
        return false;
      }

      try {
        const currentAllowance = await publicClient.readContract({
          address: tokenAddress,
          abi: ERC20_ABI,
          functionName: "allowance",
          args: [userAddress, MULTIHOP_ROUTER_ADDRESS],
        });

        return currentAllowance < amount;
      } catch (error) {
        console.error("[useBondedTokenSwap] Error checking allowance:", error);
        return false;
      }
    },
    [publicClient, userAddress, isBuy, isBonded, tokenAddress]
  );

  // Check approval status for sell transactions
  useEffect(() => {
    if (!isBonded || isBuy || !isAmountValid || amountToSwap <= BigInt(0)) {
      setNeedsApproval(false);
      return;
    }

    let cancelled = false;

    checkApprovalNeeded(amountToSwap)
      .then((needed) => {
        if (!cancelled) {
          setNeedsApproval(needed);
        }
      })
      .catch((error) => {
        console.error("[useBondedTokenSwap] Error checking approval:", error);
        if (!cancelled) {
          setNeedsApproval(false);
        }
      });

    return () => {
      cancelled = true;
    };
  }, [amountToSwap, isBuy, isBonded, isAmountValid, checkApprovalNeeded]);

  // Fetch bonded token balance directly from chain
  const fetchBondedTokenBalance = useCallback(async () => {
    if (!isBonded || !publicClient || !userAddress) {
      setBondedTokenBalance(undefined);
      return;
    }

    try {
      const balance = await publicClient.readContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: "balanceOf",
        args: [userAddress],
      });
      setBondedTokenBalance(balance);
    } catch (error) {
      console.error("[useBondedTokenSwap] Error fetching bonded token balance:", error);
      setBondedTokenBalance(undefined);
    }
  }, [isBonded, publicClient, userAddress, tokenAddress]);

  // Fetch bonded token balance on mount and when relevant deps change
  useEffect(() => {
    fetchBondedTokenBalance();
  }, [fetchBondedTokenBalance]);

  // Check and handle token approval for sell transactions
  const ensureTokenApproval = useCallback(
    async (tokenAddress: Address, spenderAddress: Address, amount: bigint) => {
      if (!walletClient || !publicClient || !userAddress) {
        throw new Error("Wallet not connected");
      }

      const currentAllowance = await publicClient.readContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: "allowance",
        args: [userAddress, spenderAddress],
      });

      if (currentAllowance < amount) {
        setIsApproving(true);

        try {
          const approvalHash = await walletClient.writeContract({
            address: tokenAddress,
            abi: ERC20_ABI,
            functionName: "approve",
            args: [spenderAddress, amount],
          });

          const approvalReceipt = await publicClient.waitForTransactionReceipt({ hash: approvalHash });

          if (approvalReceipt.status !== "success") {
            throw new Error("Token approval transaction failed");
          }

          // Update approval status after successful approval
          setNeedsApproval(false);

          // Show success toast
          toast.success(`${displayTargetSymbol} approval successful!`, {
            duration: 3000,
          });
        } finally {
          setIsApproving(false);
        }
      }
    },
    [walletClient, publicClient, userAddress, displayTargetSymbol]
  );

  // Execute the swap
  const executeSwap = useCallback(
    async (swaps: BondedSwap[], totalAmountIn: bigint, expectedOutput: bigint) => {
      if (!walletClient || !publicClient) {
        throw new Error("Wallet not connected");
      }

      // For sell transactions, ensure token approval first
      if (!isBuy) {
        await ensureTokenApproval(tokenAddress, MULTIHOP_ROUTER_ADDRESS, totalAmountIn);
      }

      // Calculate minimum output with slippage
      const minAmountOut = BigInt(Math.floor(Number(expectedOutput) * (1 - slippagePercent / 100)));

      // Build token path for the swap
      // IMPORTANT: For quotes we use WHYPE, but for transactions we use dead address for native HYPE
      const tokenPath: `0x${string}`[] = [];

      if (isBuy) {
        // Buy: WHYPE -> Target Token (matches quote)
        tokenPath.push(inputToken as `0x${string}`); // WHYPE
        tokenPath.push(outputToken as `0x${string}`); // Target Token
      } else {
        // Sell: Target Token -> Dead Address (for native HYPE)
        // Quote uses WHYPE but transaction uses dead address
        tokenPath.push(inputToken as `0x${string}`); // Target Token
        tokenPath.push(TOKENS.NATIVE_HYPE_ADDRESS as `0x${string}`); // Dead address for native HYPE
      }

      const hopSwaps = [
        swaps.map((swap) => ({
          tokenIn: swap.tokenIn,
          tokenOut: swap.tokenOut,
          routerIndex: swap.routerIndex,
          fee: swap.fee,
          amountIn: swap.amountIn,
          stable: swap.stable,
        })),
      ];

      try {
        const shouldSendNativeValue =
          isBuy && (inputToken === TOKENS.WHYPE_ADDRESS || inputToken === TOKENS.NATIVE_HYPE_ADDRESS);
        const valueToSend = shouldSendNativeValue ? totalAmountIn : BigInt(0);

        const hash = await walletClient.writeContract({
          address: MULTIHOP_ROUTER_ADDRESS,
          abi: MULTIHOP_ROUTER_ABI,
          functionName: "executeMultiHopSwap",
          args: [tokenPath, totalAmountIn, minAmountOut, hopSwaps],
          value: valueToSend,
        });

        setCurrentTxHash(hash);
        setIsConfirming(true);

        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // This won't work too well because the token is bonded but it will
          // update the HYPE balance ?.. :P
          const tokensToUpdate = [TOKENS.NATIVE_HYPE_IDENTIFIER, tokenAddress];
          updateSpecificTokenBalances(tokensToUpdate).catch((error) =>
            console.error("[useBondedTokenSwap] Failed to update balances:", error)
          );

          //   // Also refresh bonded token balance
          fetchBondedTokenBalance();

          onTransactionSuccess(hash);
        } else {
          throw new Error("Transaction reverted");
        }

        return { hash, receipt };
      } catch (contractError) {
        console.error("[useBondedTokenSwap] Contract execution error:", contractError);
        throw contractError;
      }
    },
    [
      walletClient,
      publicClient,
      slippagePercent,
      isBuy,
      tokenAddress,
      updateSpecificTokenBalances,
      onTransactionSuccess,
      ensureTokenApproval,
      fetchBondedTokenBalance,
    ]
  );

  const handleSwap = useCallback(async () => {
    if (!isConnected || !userAddress) {
      setUiError("Please connect your wallet.");
      return;
    }
    if (!isAmountValid) {
      setUiError("Please enter a valid amount.");
      return;
    }

    setIsProcessing(true);
    setUiError(null);
    setCurrentTxHash(null);
    setIsConfirming(false);
    setIsApproving(false);

    try {
      // Check balances
      if (isBuy) {
        if (hypeBalance === undefined || hypeBalance < amountToSwap) {
          setUiError("Insufficient HYPE balance.");
          setIsProcessing(false);
          return;
        }
      } else {
        // For bonded tokens, use the fetched bonded token balance
        const effectiveTargetBalance = isBonded ? bondedTokenBalance : targetTokenBalance;
        if (effectiveTargetBalance === undefined || effectiveTargetBalance < amountToSwap) {
          setUiError(`Insufficient ${displayTargetSymbol} balance.`);
          setIsProcessing(false);
          return;
        }
      }

      let quote: SwapQuote;

      if (!lastSuccessfulQuote || !lastSuccessfulQuote.success) {
        const freshQuote = await getSwapQuote(amountToSwap);
        if (!freshQuote || !freshQuote.success) {
          throw new Error(freshQuote?.error || "Failed to get swap quote");
        }
        quote = freshQuote;
      } else {
        quote = lastSuccessfulQuote;
      }

      // Fix: totalAmountIn should match the input amount we're swapping
      // Since we're sending the human-readable amount to the API (e.g., "1" for 1 HYPE),
      // but we need to send the actual wei amount in the transaction
      const totalAmountIn: bigint = amountToSwap;

      const swaps: BondedSwap[] = quote.paths.map((path) => {
        // Parse the specific amountIn for this path
        let pathAmountIn: bigint;
        try {
          if (typeof path.amountIn === "string" && path.amountIn.includes(".")) {
            // If it's a decimal string, parse as float then convert to wei
            const decimalValue = parseFloat(path.amountIn);
            pathAmountIn = parseUnits(decimalValue.toString(), isBuy ? hypeTokenDecimals : targetTokenDecimals);
          } else {
            // If it's already in wei or integer format
            pathAmountIn = parseUnits(path.amountIn.toString(), isBuy ? hypeTokenDecimals : targetTokenDecimals);
          }
        } catch (parseError) {
          console.error("[useBondedTokenSwap] Error parsing path amountIn, falling back to proportional:", parseError);
          // Fallback: use totalAmountIn divided by number of paths
          pathAmountIn = totalAmountIn / BigInt(quote.paths.length);
        }

        return {
          tokenIn: path.tokenIn as `0x${string}`,
          tokenOut: path.tokenOut as `0x${string}`,
          routerIndex: path.routerIndex,
          fee: path.fee,
          amountIn: pathAmountIn,
          stable: path.stable,
        };
      });

      // Parse the expected output using the utility function
      let expectedOutput: bigint;
      try {
        expectedOutput = parseQuoteOutput(quote.estimatedTotalOutput, isBuy ? targetTokenDecimals : hypeTokenDecimals);
      } catch (outputError) {
        console.error("[useBondedTokenSwap] Error parsing expected output:", outputError);
        throw new Error(`Invalid expected output format: ${quote.estimatedTotalOutput}`);
      }

      console.log("[useBondedTokenSwap] Final swap parameters:", {
        swapsCount: swaps.length,
        totalAmountIn: totalAmountIn.toString(),
        totalAmountInHumanReadable: formatUnits(totalAmountIn, isBuy ? hypeTokenDecimals : targetTokenDecimals),
        expectedOutput: expectedOutput.toString(),
        expectedOutputHumanReadable: formatUnits(expectedOutput, isBuy ? targetTokenDecimals : hypeTokenDecimals),
        multihopRouterAddress: MULTIHOP_ROUTER_ADDRESS,
        originalInputAmount: inputAmountRaw,
      });

      // Execute the swap
      await executeSwap(swaps, totalAmountIn, expectedOutput);
    } catch (error: unknown) {
      const friendlyMessage = createSwapErrorMessage(error, displayTargetSymbol);
      setUiError(friendlyMessage);
      console.error("[useBondedTokenSwap] Swap error:", error);
    } finally {
      setIsProcessing(false);
      setIsConfirming(false);
      setIsApproving(false);
    }
  }, [
    isConnected,
    userAddress,
    isAmountValid,
    amountToSwap,
    isBuy,
    hypeBalance,
    targetTokenBalance,
    displayTargetSymbol,
    getSwapQuote,
    executeSwap,
    hypeTokenDecimals,
    targetTokenDecimals,
    inputAmountRaw,
    estimatedOutput,
    lastSuccessfulQuote,
    isLoadingEstimate,
  ]);

  // Generate button text
  let swapButtonText = "Swap";
  if (!isConnected) swapButtonText = "Connect Wallet";
  else if (!isAmountValid && !(isProcessing || isConfirming)) swapButtonText = "Enter an Amount";
  else if (isApproving) swapButtonText = "Approving...";
  else if (isProcessing && !isConfirming && !isApproving) swapButtonText = "Processing...";
  else if (isConfirming) swapButtonText = "Confirming...";
  else if (!lastSuccessfulQuote && isLoadingEstimate) swapButtonText = "Getting Quote...";
  else if (isBuy) swapButtonText = `Buy ${displayTargetSymbol}`;
  else if (needsApproval) swapButtonText = `Approve ${displayTargetSymbol}`;
  else swapButtonText = `Sell ${displayTargetSymbol}`;

  // Allow swapping if we have a stored quote, even if currently fetching a new one
  const hasUsableQuote = lastSuccessfulQuote && lastSuccessfulQuote.success;
  const isLoadingButton =
    !isConnected ||
    isProcessing ||
    isConfirming ||
    isApproving ||
    (!isAmountValid && !(isProcessing || isConfirming)) ||
    (!hasUsableQuote && isLoadingEstimate); // Only block if no stored quote AND currently loading

  const clearError = () => {
    setUiError(null);
    setIsApproving(false);
  };

  return {
    handleSwap,
    isProcessing,
    isConfirming,
    uiError,
    currentTxHash,
    isLoadingButton,
    swapButtonText,
    isAmountValid,
    clearError,
    estimatedOutput,
    isLoadingEstimate,
    bondedTokenBalance,
  };
}
