import { useEffect, useCallback } from "react";
import { useUpdaterStore } from "@/stores/updaterStore";
import { UpdaterConfig } from "@/services/updaterService";

interface UseUpdaterOptions {
  enabled?: boolean;
  interval?: number;
  autoStart?: boolean;
}

export function useSwapsUpdater(tokenAddress: string, callback: () => Promise<void>, options: UseUpdaterOptions = {}) {
  const { enabled = true, interval = 5000, autoStart = true } = options;
  const { startSwapsPolling, stopSwapsPolling } = useUpdaterStore();

  const start = useCallback(() => {
    if (tokenAddress) {
      const config: Partial<UpdaterConfig> = {
        enabled,
        interval,
      };
      startSwapsPolling(tokenAddress, callback, config);
    }
  }, [tokenAddress, enabled, interval, startSwapsPolling, callback]);

  const stop = useCallback(() => {
    if (tokenAddress) {
      stopSwapsPolling(tokenAddress);
    }
  }, [tokenAddress, stopSwapsPolling]);

  useEffect(() => {
    if (autoStart && enabled && tokenAddress) {
      console.log(`[useSwapsUpdater] Starting swaps polling for token: ${tokenAddress}`);
      start();
    }

    return () => {
      stop();
    };
  }, [tokenAddress, enabled, autoStart, callback, interval, start, stop]);

  return {
    start,
    stop,
  };
}

export function useTokensUpdater(callback: () => Promise<void>, options: UseUpdaterOptions = {}) {
  const { enabled = true, interval = 5000, autoStart = true } = options;
  const { startTokensPolling, stopTokensPolling } = useUpdaterStore();

  const start = useCallback(() => {
    const config: Partial<UpdaterConfig> = {
      enabled,
      interval,
    };
    startTokensPolling(callback, config);
  }, [enabled, interval, startTokensPolling, callback]);

  const stop = useCallback(() => {
    stopTokensPolling();
  }, [stopTokensPolling]);

  useEffect(() => {
    if (autoStart && enabled) {
      console.log("[useTokensUpdater] Starting tokens polling");
      start();
    }

    return () => {
      stop();
    };
  }, [enabled, autoStart, callback, interval, start, stop]);

  return {
    start,
    stop,
  };
}

export function useChartDataUpdater(
  tokenAddress: string,
  callback: () => Promise<void>,
  options: UseUpdaterOptions = {}
) {
  const { enabled = true, interval = 5000, autoStart = true } = options;
  const { startChartDataPolling, stopChartDataPolling } = useUpdaterStore();

  const start = useCallback(() => {
    if (tokenAddress) {
      const config: Partial<UpdaterConfig> = {
        enabled,
        interval,
      };
      startChartDataPolling(tokenAddress, callback, config);
    }
  }, [tokenAddress, enabled, interval, startChartDataPolling, callback]);

  const stop = useCallback(() => {
    if (tokenAddress) {
      stopChartDataPolling(tokenAddress);
    }
  }, [tokenAddress, stopChartDataPolling]);

  useEffect(() => {
    if (autoStart && enabled && tokenAddress) {
      start();
    }

    return () => {
      stop();
    };
  }, [tokenAddress, enabled, autoStart, callback, interval, start, stop]);

  return {
    start,
    stop,
  };
}

export function useLatestSwapsUpdater(callback: () => Promise<void>, options: UseUpdaterOptions = {}) {
  const { enabled = true, interval = 10000, autoStart = true } = options;
  const { startLatestSwapsPolling, stopLatestSwapsPolling } = useUpdaterStore();

  const start = useCallback(() => {
    const config: Partial<UpdaterConfig> = {
      enabled,
      interval,
    };
    startLatestSwapsPolling(callback, config);
  }, [enabled, interval, startLatestSwapsPolling, callback]);

  const stop = useCallback(() => {
    stopLatestSwapsPolling();
  }, [stopLatestSwapsPolling]);

  useEffect(() => {
    if (autoStart && enabled) {
      console.log("[useLatestSwapsUpdater] Starting latest swaps polling");
      start();
    }

    return () => {
      stop();
    };
  }, [enabled, autoStart, callback, interval, start, stop]);

  return {
    start,
    stop,
  };
}

export function useDexScreenerUpdater(
  tokenAddress: string,
  callback: () => Promise<void>,
  options: UseUpdaterOptions = {}
) {
  const { enabled = true, interval = 5000, autoStart = true } = options;
  const { startDexScreenerPolling, stopDexScreenerPolling } = useUpdaterStore();

  const start = useCallback(() => {
    if (tokenAddress) {
      const config: Partial<UpdaterConfig> = {
        enabled,
        interval,
      };
      startDexScreenerPolling(tokenAddress, callback, config);
    }
  }, [tokenAddress, enabled, interval, startDexScreenerPolling, callback]);

  const stop = useCallback(() => {
    if (tokenAddress) {
      stopDexScreenerPolling(tokenAddress);
    }
  }, [tokenAddress, stopDexScreenerPolling]);

  useEffect(() => {
    if (autoStart && enabled && tokenAddress) {
      console.log(`[useDexScreenerUpdater] Starting DexScreener polling for token: ${tokenAddress}`);
      start();
    }

    return () => {
      stop();
    };
  }, [tokenAddress, enabled, autoStart, callback, interval, start, stop]);

  return {
    start,
    stop,
  };
}
// General updater hook for custom use cases
