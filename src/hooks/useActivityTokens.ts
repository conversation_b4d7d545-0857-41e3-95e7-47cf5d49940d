import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { WalletSwap } from "@/lib/api/types";
import { LiquidLaunchApiService } from "@/lib/api";

const apiService = new LiquidLaunchApiService();

interface EnrichedWalletSwap extends WalletSwap {
  token_image_uri?: string;
}

/**
 * Hook to enrich wallet swaps with token images from API
 */
export function useActivityTokens(swaps: WalletSwap[]) {
  // Get unique token addresses from swaps
  const uniqueTokenAddresses = useMemo(() => {
    const addresses = new Set<string>();
    swaps.forEach((swap) => {
      addresses.add(swap.token.toLowerCase());
    });
    return Array.from(addresses);
  }, [swaps]);

  // Fetch token details for all unique tokens
  const {
    data: tokenDetailsMap,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["activityTokenDetails", uniqueTokenAddresses],
    queryFn: async () => {
      if (uniqueTokenAddresses.length === 0) return {};

      const results: Record<string, { image_uri?: string }> = {};

      // Fetch token details for each unique token
      const promises = uniqueTokenAddresses.map(async (address) => {
        try {
          const tokenDetails = await apiService.token.getTokenDetails(address);
          return {
            address: address.toLowerCase(),
            image_uri: tokenDetails.metadata?.image_uri,
          };
        } catch (error) {
          console.warn(`[useActivityTokens] Failed to fetch details for token ${address}:`, error);
          return {
            address: address.toLowerCase(),
            image_uri: undefined,
          };
        }
      });

      const tokenResults = await Promise.all(promises);

      tokenResults.forEach((result) => {
        results[result.address] = {
          image_uri: result.image_uri,
        };
      });

      return results;
    },
    enabled: uniqueTokenAddresses.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes - token metadata doesn't change often
    refetchOnWindowFocus: false,
  });

  // Enrich swaps with token images
  const enrichedSwaps = useMemo<EnrichedWalletSwap[]>(() => {
    if (!tokenDetailsMap) return swaps;

    return swaps.map((swap) => ({
      ...swap,
      token_image_uri: tokenDetailsMap[swap.token.toLowerCase()]?.image_uri,
    }));
  }, [swaps, tokenDetailsMap]);

  return {
    enrichedSwaps,
    isLoading,
    error,
  };
}
