import { useEffect, useCallback, useRef } from "react";
import { useAccount } from "wagmi";
import { useSession } from "next-auth/react";
import { useChatStore } from "@/stores/chatStore";
import { useBalanceStore } from "@/stores/balanceStore";
import { socketManager } from "@/services/socketManager";
import { ChatMessage, ChatUser, SendMessagePayload, TypingPayload } from "@/types/chat";
import { CHAT_CONFIG, getRoomId } from "@/config/chat";
import { formatUnits } from "viem";
import { useBalance } from "wagmi";
import { MessageResponse, MessageReaction } from "@/lib/api/types";
import { convertApiMessageToInternal, validateChatAuthentication } from "@/lib/chatUtils";

export const useChat = (currentRoomId?: string) => {
  const { address } = useAccount();
  const { data: session, status: sessionStatus } = useSession();
  const typingTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});
  const currentSocketRoomRef = useRef<string | undefined>(undefined);

  const { findToken } = useBalanceStore();

  // Use wagmi's useBalance hook like your old code for the current room
  const { data: tokenBalance } = useBalance({
    address: address as `0x${string}`,
    token: currentRoomId && currentRoomId !== CHAT_CONFIG.GLOBAL_ROOM_ID ? (currentRoomId as `0x${string}`) : undefined,
    query: {
      enabled: !!address && !!currentRoomId && currentRoomId !== CHAT_CONFIG.GLOBAL_ROOM_ID,
    },
  });

  // Helper functions for balance calculation - match your old code pattern
  const getFormattedBalance = useCallback(
    (roomId: string) => {
      if (roomId === CHAT_CONFIG.GLOBAL_ROOM_ID) return "0";

      // Use wagmi balance data if available for current room (like your old code)
      if (roomId === currentRoomId && tokenBalance?.value) {
        try {
          return formatUnits(tokenBalance.value, tokenBalance.decimals);
        } catch (error) {
          console.error("Error formatting wagmi balance:", error);
        }
      }

      // Fall back to balance store
      const token = findToken(roomId);
      if (!token || !token.balance) return "0";

      try {
        return formatUnits(BigInt(token.balance), token.decimals);
      } catch (error) {
        console.error("Error formatting balance:", error);
        return "0";
      }
    },
    [findToken, currentRoomId, tokenBalance]
  );

  const calculateBalancePercent = useCallback(
    (roomId: string) => {
      if (roomId === CHAT_CONFIG.GLOBAL_ROOM_ID) return 0;

      // Use wagmi balance data if available for current room (like your old code)
      if (roomId === currentRoomId && tokenBalance?.value) {
        try {
          // Using a default total supply of 1B tokens with 6 decimals (like your old code)
          const TOTAL_SUPPLY = BigInt("1000000000000000"); // 1B tokens with 6 decimals
          const percentage = (Number(tokenBalance.value) * 100) / Number(TOTAL_SUPPLY);
          return Number(percentage.toFixed(4));
        } catch (error) {
          console.error("Error calculating wagmi balance percentage:", error);
        }
      }

      // Fall back to balance store
      const token = findToken(roomId);
      if (!token || !token.balance) return 0;

      try {
        // Using a default total supply of 1B tokens with 6 decimals
        const TOTAL_SUPPLY = BigInt("1000000000000000"); // 1B tokens with 6 decimals
        const balance = BigInt(token.balance);
        const percentage = (Number(balance) * 100) / Number(TOTAL_SUPPLY);
        return Number(percentage.toFixed(4));
      } catch (error) {
        console.error("Error calculating balance percentage:", error);
        return 0;
      }
    },
    [findToken, currentRoomId, tokenBalance]
  );

  const {
    isConnected,
    currentUser,
    activeRooms,
    messages,
    windows,
    typingUsers,
    unreadCounts,
    setConnectionState,
    setCurrentUser,
    addMessage,
    addPendingMessage,
    confirmMessage,
    updateMessage,
    setTyping,
    removeTyping,
    incrementUnread,
    updateRoomParticipantCount,
  } = useChatStore();

  // Initialize socket connection
  const connect = useCallback(async () => {
    if (!currentRoomId) return;

    // Prevent multiple simultaneous connections
    if (isConnecting.current) {
      console.log("⏳ Connection already in progress, skipping...");
      return;
    }

    isConnecting.current = true;

    try {
      console.log("🔌 Connecting to chat server for room:", currentRoomId);

      // Create fresh socket connection with correct tokenAddress
      const socket = socketManager.connect(currentRoomId);

      // Wait for connection to be established
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error("Connection timeout"));
        }, 10000);

        socket.on("connect", () => {
          clearTimeout(timeout);
          resolve();
        });

        socket.on("connect_error", (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      // Auto-join the current room after connection and handle any pending room changes
      const normalizedCurrentRoomId = currentRoomId.toLowerCase();

      // If we have a tracked room that's different from current, leave it first
      if (currentSocketRoomRef.current && currentSocketRoomRef.current !== normalizedCurrentRoomId) {
        console.log("Leaving stale room on reconnect:", currentSocketRoomRef.current);
        socket.emit("leaveRoom", { tokenAddress: currentSocketRoomRef.current });
      }

      // Join the current room and update tracking
      currentSocketRoomRef.current = normalizedCurrentRoomId;
      socket.emit("joinRoom", { tokenAddress: normalizedCurrentRoomId });

      setConnectionState(true);
      isConnecting.current = false;
      console.log("✅ Chat connection established for room:", currentRoomId);

      // Set up event listeners
      socket.on("message", (message: MessageResponse) => {
        console.log("Received message:", message);
        // Use the same conversion logic as message history for consistency
        const formattedMessage = convertApiMessageToInternal(message);

        // Check for duplicate messages before adding (both in current messages and pending)
        const existingMessages = messages[formattedMessage.roomId] || [];
        const { pendingMessages } = useChatStore.getState();
        const isDuplicate =
          existingMessages.some((m) => m.id === formattedMessage.id) ||
          Object.values(pendingMessages).some((m) => m.id === formattedMessage.id);

        if (!isDuplicate) {
          console.log("Adding new message:", formattedMessage.id, "to room:", formattedMessage.roomId);
          addMessage(formattedMessage);

          // Increment unread count if not in active window
          const activeWindow = windows.find((w) => w.roomId === formattedMessage.roomId && !w.isMinimized);
          if (!activeWindow) {
            incrementUnread(formattedMessage.roomId);
          }
        } else {
          console.warn("Duplicate message detected, skipping:", {
            messageId: formattedMessage.id,
            roomId: formattedMessage.roomId,
            content: formattedMessage.content.substring(0, 50),
            existingInMessages: existingMessages.some((m) => m.id === formattedMessage.id),
            existingInPending: Object.values(pendingMessages).some((m) => m.id === formattedMessage.id),
          });
        }
      });

      socket.on("messageConfirmation", ({ tempId, message }: { tempId: number; message: MessageResponse }) => {
        console.log("Message confirmation:", tempId, message);
        // Use the same conversion logic as message history for consistency
        const formattedMessage = convertApiMessageToInternal(message);

        confirmMessage(tempId.toString(), formattedMessage);
      });

      socket.on("reaction", ({ messageId, emoji, userAddress, action }: MessageReaction) => {
        // Get current messages from store state
        const { messages: currentMessages } = useChatStore.getState();

        // Find the message across all rooms
        let targetMessage: ChatMessage | undefined;
        let targetRoomId: string | undefined;

        for (const [roomId, roomMessages] of Object.entries(currentMessages)) {
          const found = roomMessages.find((m) => m.id === messageId.toString());
          if (found) {
            targetMessage = found;
            targetRoomId = roomId;
            break;
          }
        }

        if (targetMessage && targetRoomId) {
          const reactions = { ...targetMessage.reactions };

          if (action === "add") {
            if (!reactions[emoji]) {
              reactions[emoji] = [];
            }
            if (!reactions[emoji].includes(userAddress)) {
              reactions[emoji].push(userAddress);
            }
          } else if (action === "remove") {
            if (reactions[emoji]) {
              reactions[emoji] = reactions[emoji].filter((addr) => addr !== userAddress);
              if (reactions[emoji].length === 0) {
                delete reactions[emoji];
              }
            }
          }

          updateMessage(messageId.toString(), { reactions });
        }
      });

      socket.on(
        "reactionUpdate",
        ({ messageId, reactions }: { messageId: string; reactions: { [emoji: string]: string[] } }) => {
          console.log("Received reaction update:", { messageId, reactions });

          // Get current messages from store state
          const { messages: currentMessages } = useChatStore.getState();

          // Find the message across all rooms
          let targetMessage: ChatMessage | undefined;
          let targetRoomId: string | undefined;

          for (const [roomId, roomMessages] of Object.entries(currentMessages)) {
            const found = roomMessages.find((m) => m.id === messageId.toString());
            if (found) {
              targetMessage = found;
              targetRoomId = roomId;
              break;
            }
          }

          if (targetMessage && targetRoomId) {
            // Update the message with the new reactions object
            updateMessage(messageId.toString(), { reactions });
          } else {
            console.warn("Message not found for reaction update:", messageId);
          }
        }
      );

      socket.on("participantCount", (count: number) => {
        console.log("Participant count:", count, "for room:", currentRoomId);
        // Update participant count for current room
        if (currentRoomId) {
          updateRoomParticipantCount(currentRoomId, count);
        }
      });

      socket.on("typingStart", ({ roomId, user }: { roomId: string; user: ChatUser }) => {
        if (user.id !== address) {
          setTyping(roomId, user);

          // Clear typing after timeout
          if (typingTimeoutRef.current[`${roomId}-${user.id}`]) {
            clearTimeout(typingTimeoutRef.current[`${roomId}-${user.id}`]);
          }

          typingTimeoutRef.current[`${roomId}-${user.id}`] = setTimeout(() => {
            removeTyping(roomId, user.id);
            delete typingTimeoutRef.current[`${roomId}-${user.id}`];
          }, CHAT_CONFIG.TYPING_TIMEOUT);
        }
      });

      socket.on("typingStop", ({ roomId, userId }: { roomId: string; userId: string }) => {
        removeTyping(roomId, userId);
        if (typingTimeoutRef.current[`${roomId}-${userId}`]) {
          clearTimeout(typingTimeoutRef.current[`${roomId}-${userId}`]);
          delete typingTimeoutRef.current[`${roomId}-${userId}`];
        }
      });

      socket.on("joinedRoom", (data) => {
        console.log("Successfully joined room:", data);
      });

      socket.on("leftRoom", (data) => {
        console.log("Successfully left room:", data);
      });

      socket.on("error", ({ message, code }: { message: string; code?: string }) => {
        console.error("Chat error:", message, code);

        // If this is a message save error, we need to handle pending messages
        if (message === "Failed to save message") {
          // Find and mark any pending messages as failed
          const { pendingMessages, markMessageFailed } = useChatStore.getState();
          Object.entries(pendingMessages).forEach(([tempId, pendingMessage]) => {
            console.log("Marking pending message as failed due to server error:", tempId);
            markMessageFailed(tempId, pendingMessage.roomId);
          });
        }
      });

      // Set current user only if authenticated
      if (address && sessionStatus === "authenticated") {
        const user: ChatUser = {
          id: address,
          address,
          isOnline: true,
        };
        setCurrentUser(user);
      }
    } catch (error) {
      console.error("❌ Failed to connect to chat:", error);
      setConnectionState(false);
      isConnecting.current = false;
    }
  }, [
    currentRoomId,
    sessionStatus,
    address,
    addMessage,
    setConnectionState,
    setCurrentUser,
    confirmMessage,
    updateMessage,
    setTyping,
    removeTyping,
    incrementUnread,
    updateRoomParticipantCount,
    windows,
    messages,
  ]);

  // Disconnect socket
  const disconnect = useCallback(() => {
    console.log("🔌 Disconnecting from chat");

    // Clear all typing timeouts
    Object.values(typingTimeoutRef.current).forEach((timeout) => clearTimeout(timeout));
    typingTimeoutRef.current = {};

    // Clear room tracking since we're disconnecting
    currentSocketRoomRef.current = undefined;

    // Disconnect socket (this will clean up all listeners automatically)
    socketManager.disconnect();
    setConnectionState(false);
  }, [setConnectionState]);

  // Send message with proper confirmation handling
  const sendMessage = useCallback(
    async (payload: SendMessagePayload): Promise<string> => {
      const socket = socketManager.getCurrentSocket();
      if (!socket?.connected || !address) {
        throw new Error("Not connected to chat");
      }

      // Validate authentication and address match
      const authValidation = validateChatAuthentication(sessionStatus, session, address);
      if (!authValidation.isValid) {
        throw new Error(authValidation.error || "Authentication failed");
      }

      const tempId = Date.now();
      const normalizedRoomId = payload.roomId.toLowerCase();

      // Create optimistic message for immediate UI feedback
      const tempMessage: ChatMessage = {
        id: tempId.toString(),
        content: payload.type === "gif" ? payload.metadata?.gifUrl || "" : payload.content,
        userId: address,
        user: currentUser || {
          id: address,
          address,
          isOnline: true,
        },
        roomId: normalizedRoomId,
        timestamp: new Date(),
        type: payload.type || "text",
        replyTo: payload.replyTo,
        reactions: {},
        isConfirmed: false,
        isReaction: false,
        balance: getFormattedBalance(normalizedRoomId),
        balancePercent: calculateBalancePercent(normalizedRoomId),
        metadata: payload.metadata,
      };

      // Add pending message for immediate UI feedback
      addPendingMessage(tempMessage);

      // Prepare message payload (keep working format)
      const messagePayload = {
        username: address,
        text: payload.type === "gif" ? payload.metadata?.gifUrl || "" : payload.content,
        tokenaddress: normalizedRoomId,
        balance: getFormattedBalance(normalizedRoomId),
        balancepercent: calculateBalancePercent(normalizedRoomId),
        replyTo: payload.replyTo
          ? {
              id: payload.replyTo.id,
              username: payload.replyTo.username,
              message: payload.replyTo.message,
              tokenaddress: payload.replyTo.tokenaddress,
            }
          : null,
        isGif: payload.type === "gif",
        tempId,
      };

      return new Promise((resolve, reject) => {
        // Set up confirmation timeout (10 seconds)
        const confirmationTimeout = setTimeout(() => {
          console.warn("Message confirmation timeout for tempId:", tempId);
          // Clean up listeners
          socket.off("messageConfirmation", handleConfirmation);
          socket.off("error", handleError);
          // Mark message as failed
          const { markMessageFailed } = useChatStore.getState();
          markMessageFailed(tempId.toString(), normalizedRoomId);
          reject(new Error("Message confirmation timeout"));
        }, 10000);

        // Set up one-time confirmation listener
        const handleConfirmation = ({
          tempId: confirmedTempId,
          message,
        }: {
          tempId: number;
          message: MessageResponse;
        }) => {
          if (confirmedTempId === tempId) {
            clearTimeout(confirmationTimeout);
            socket.off("messageConfirmation", handleConfirmation);
            socket.off("error", handleError);

            // Handle server returning "undefined" as string for tokenaddress
            let confirmedRoomId = normalizedRoomId; // Use our original roomId as fallback
            if (message.tokenaddress && message.tokenaddress !== "undefined") {
              confirmedRoomId = message.tokenaddress.toLowerCase();
            }

            // Convert confirmed message using the same logic as message history, then override specific fields
            const confirmedMessage = convertApiMessageToInternal(message);

            // Override roomId if server returned "undefined"
            if (confirmedMessage.roomId === "undefined" || !confirmedMessage.roomId) {
              confirmedMessage.roomId = confirmedRoomId;
            }

            // Preserve original payload metadata for GIFs
            if (payload.type === "gif" && payload.metadata) {
              confirmedMessage.metadata = { ...confirmedMessage.metadata, ...payload.metadata };
            }

            console.log("Message confirmed:", {
              tempId,
              originalRoomId: normalizedRoomId,
              serverTokenAddress: message.tokenaddress,
              finalRoomId: confirmedRoomId,
              confirmedMessage,
            });

            confirmMessage(tempId.toString(), confirmedMessage);
            resolve(tempId.toString());
          }
        };

        // Set up error listener for this specific message
        const handleError = ({ message: errorMessage }: { message: string }) => {
          if (errorMessage === "Failed to save message") {
            console.error("Server failed to save message for tempId:", tempId);
            clearTimeout(confirmationTimeout);
            socket.off("messageConfirmation", handleConfirmation);
            socket.off("error", handleError);
            // Mark message as failed
            const { markMessageFailed } = useChatStore.getState();
            markMessageFailed(tempId.toString(), normalizedRoomId);
            reject(new Error("Server failed to save message"));
          }
        };

        // Listen for confirmation and errors
        socket.on("messageConfirmation", handleConfirmation);
        socket.on("error", handleError);

        // Send the message
        console.log("Sending message with payload:", {
          ...messagePayload,
          socketConnected: socket.connected,
          currentRoomId: normalizedRoomId,
          userAddress: address,
        });
        socket.emit("message", messagePayload);
      });
    },
    [
      address,
      sessionStatus,
      session,
      currentUser,
      addPendingMessage,
      getFormattedBalance,
      calculateBalancePercent,
      confirmMessage,
    ]
  );

  // Join room with proper cleanup
  const joinRoom = useCallback(
    (roomId: string) => {
      const socket = socketManager.getCurrentSocket();
      const normalizedRoomId = roomId.toLowerCase();

      // Always leave previous room first if we were in one
      if (currentSocketRoomRef.current && currentSocketRoomRef.current !== normalizedRoomId) {
        if (socket?.connected) {
          console.log("Leaving previous room:", currentSocketRoomRef.current);
          socket.emit("leaveRoom", { tokenAddress: currentSocketRoomRef.current });
        } else {
          console.log("Queuing leave for previous room (disconnected):", currentSocketRoomRef.current);
        }
      }

      // Update current room reference immediately
      currentSocketRoomRef.current = normalizedRoomId;

      if (!socket?.connected) {
        console.warn("Cannot join room - socket not connected, will join on reconnect:", normalizedRoomId);
        return;
      }

      console.log("Joining room:", normalizedRoomId, "with userAddress:", address || "unauthenticated");
      socket.emit("joinRoom", { tokenAddress: normalizedRoomId });
    },
    [address]
  );

  // Leave room
  const leaveRoom = useCallback((roomId: string) => {
    const socket = socketManager.getCurrentSocket();
    const normalizedRoomId = roomId.toLowerCase();

    // Update current room reference
    if (currentSocketRoomRef.current === normalizedRoomId) {
      currentSocketRoomRef.current = undefined;
    }

    if (!socket?.connected) {
      console.log("Cannot leave room - socket not connected:", normalizedRoomId);
      return;
    }

    console.log("Leaving room:", normalizedRoomId);
    socket.emit("leaveRoom", { tokenAddress: normalizedRoomId });
  }, []);

  // Toggle reaction (add or remove)
  const toggleReaction = useCallback(
    (messageId: string, emoji: string, userAddress: string, action: "add" | "remove") => {
      const socket = socketManager.getCurrentSocket();
      if (!socket?.connected) return;

      // Validate authentication and address match
      const authValidation = validateChatAuthentication(sessionStatus, session, userAddress);
      if (!authValidation.isValid) {
        console.warn("Cannot toggle reaction:", authValidation.error);
        return;
      }

      const payload = {
        messageId,
        emoji,
        userAddress,
        action,
      };

      socket.emit("reaction", payload);
    },
    [sessionStatus, session]
  );

  // Send typing indicator
  const sendTyping = useCallback(
    (roomId: string, isTyping: boolean) => {
      const socket = socketManager.getCurrentSocket();
      if (!socket?.connected) return;

      // Validate authentication and address match
      const authValidation = validateChatAuthentication(sessionStatus, session, address);
      if (!authValidation.isValid) {
        return;
      }

      const payload: TypingPayload = {
        roomId: roomId.toLowerCase(),
        isTyping,
      };

      socket.emit("typing", payload);
    },
    [sessionStatus, session, address]
  );

  // Retry failed message
  const retryMessage = useCallback(
    async (messageId: string, roomId: string): Promise<string> => {
      const { messages, updateMessage } = useChatStore.getState();
      const roomMessages = messages[roomId];
      const failedMessage = roomMessages?.find((m) => m.id === messageId);

      if (!failedMessage) {
        throw new Error("Message not found");
      }

      // Clear failed state
      updateMessage(messageId, {
        metadata: {
          ...failedMessage.metadata,
          failed: false,
          error: undefined,
        },
      });

      // Resend the message
      const payload: SendMessagePayload = {
        content: failedMessage.content,
        roomId: failedMessage.roomId,
        type: failedMessage.type as "text" | "gif",
        replyTo: failedMessage.replyTo,
        metadata: failedMessage.metadata,
      };

      try {
        return await sendMessage(payload);
      } catch (error) {
        // Mark as failed again if retry fails
        updateMessage(messageId, {
          metadata: {
            ...failedMessage.metadata,
            failed: true,
            error: error instanceof Error ? error.message : "Retry failed",
          },
        });
        throw error;
      }
    },
    [sendMessage]
  );

  // Connect/reconnect when room changes
  const previousRoomId = useRef<string | undefined>(undefined);
  const isReconnecting = useRef(false);
  const isConnecting = useRef(false);

  useEffect(() => {
    // Connect when we have currentRoomId and room has changed (allow unauthenticated connections for live updates)
    if (currentRoomId && previousRoomId.current !== currentRoomId && !isReconnecting.current) {
      console.log("🔄 Room changed from", previousRoomId.current || "none", "to", currentRoomId);
      isReconnecting.current = true;

      // Always disconnect first to ensure clean state
      disconnect();

      // Connect to new room
      connect().finally(() => {
        isReconnecting.current = false;
      });
    }

    // Update the previous room ID
    previousRoomId.current = currentRoomId;
  }, [currentRoomId, connect, disconnect]);

  // Initial connection for unauthenticated users
  useEffect(() => {
    // If we have a room but no connection, and we're not already connecting, connect
    if (currentRoomId && !isConnected && !isConnecting.current && !isReconnecting.current) {
      console.log(
        "🔌 Initial connection for room:",
        currentRoomId,
        "authenticated:",
        sessionStatus === "authenticated"
      );
      connect();
    }
  }, [currentRoomId, isConnected, sessionStatus, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      Object.values(typingTimeoutRef.current).forEach((timeout) => clearTimeout(timeout));
    };
  }, []);

  return {
    // State
    isConnected,
    currentUser,
    activeRooms,
    messages,
    windows,
    typingUsers,
    unreadCounts,

    // Actions
    connect,
    disconnect,
    sendMessage,
    retryMessage,
    joinRoom,
    leaveRoom,
    toggleReaction,
    sendTyping,

    // Utilities
    getRoomId,
  };
};
