"use client";

import React, { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAccount } from "wagmi";
import { getWalletSwapsAction } from "@/app/actions/wallet.actions";
import { ApiError } from "@/lib/api";
import { WalletSwapsResponse } from "@/lib/api/types";
import { Skeleton } from "@/components/ui/skeleton";
import { TimeAgoCell } from "@/components/ui/time-ago-cell";
import { formatTokenValue } from "@/lib/utils";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { useBalanceStore } from "@/stores/balanceStore";
import { ScientificPrice } from "@/components/ui/scientific-price";
import { TOKENS } from "@/lib/constants";
import { IMAGES } from "@/lib/constants-images";
import Image from "next/image";
import Link from "next/link";
import { TrendingUp, TrendingDown, Activity as ActivityIcon } from "lucide-react";
import { useActivityTokens } from "@/hooks/useActivityTokens";

interface ActivityProps {
  onClose?: () => void;
}

export function Activity({ onClose }: ActivityProps) {
  const { address: userAddress, isConnected } = useAccount();
  const { showUsd } = useCurrencyFormatter();
  const hypePriceUsd = useBalanceStore((state) => state.getTokenPrice(TOKENS.NATIVE_HYPE_IDENTIFIER));

  const queryKey = useMemo(() => ["userActivity", userAddress] as const, [userAddress]);

  const {
    data: response,
    isLoading: queryIsLoading,
    error,
  } = useQuery<WalletSwapsResponse, ApiError, WalletSwapsResponse, typeof queryKey>({
    queryKey: queryKey,
    queryFn: async () => {
      if (!userAddress) {
        return {
          swaps: [],
          pagination: { currentPage: 1, totalPages: 0, totalSwaps: 0, limit: 20 },
        };
      }
      return getWalletSwapsAction(userAddress.toLowerCase(), {
        page: 1,
        limit: 20,
        sort_order: "desc",
      });
    },
    placeholderData: (previousData) => previousData,
    enabled: !!(isConnected && userAddress),
    refetchOnWindowFocus: false,
  });

  // Enrich swaps with token images
  const { enrichedSwaps, isLoading: isTokensLoading } = useActivityTokens(response?.swaps || []);

  // Not connected state
  if (!isConnected) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center px-4">
        <ActivityIcon className="w-12 h-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Connect Your Wallet</h3>
        <p className="text-sm text-muted-foreground">
          Connect your wallet to view your trading activity and transaction history.
        </p>
      </div>
    );
  }

  // Loading state
  if (queryIsLoading || isTokensLoading) {
    return (
      <div className="p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ActivityIcon className="w-5 h-5" />
            <h3 className="text-lg font-semibold">Recent Activity</h3>
          </div>
        </div>
        <div className="space-y-3">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="flex items-center gap-3 p-3 rounded-lg bg-card/50">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-32" />
              </div>
              <div className="text-right space-y-1">
                <Skeleton className="h-4 w-16 ml-auto" />
                <Skeleton className="h-3 w-12 ml-auto" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center px-4">
        <ActivityIcon className="w-12 h-12 text-red-400 mb-4" />
        <h3 className="text-lg font-semibold text-red-400 mb-2">Error Loading Activity</h3>
        <p className="text-sm text-muted-foreground">{error.message}</p>
      </div>
    );
  }

  // No data state
  if (!response || response.swaps.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center px-4">
        <ActivityIcon className="w-12 h-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Activity Yet</h3>
        <p className="text-sm text-muted-foreground">
          Your trading activity will appear here once you start making transactions.
        </p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <ActivityIcon className="w-5 h-5" />
          <h3 className="text-lg font-semibold">Recent Activity</h3>
        </div>
        <span className="text-sm text-muted-foreground">{response.pagination.totalSwaps} total swaps</span>
      </div>

      <div className="space-y-2">
        {enrichedSwaps.map((swap) => {
          const isBuy = swap.type === "purchase";
          const tokenAmount = formatTokenValue(swap.token_amount, 6);
          const hypeAmount = formatTokenValue(swap.hype_amount, 4);
          const pricePerToken =
            swap.token_amount && parseFloat(swap.token_amount) > 0
              ? parseFloat(swap.hype_amount) / parseFloat(swap.token_amount)
              : 0;

          const displayValue =
            showUsd && hypePriceUsd ? (parseFloat(swap.hype_amount) * Number(hypePriceUsd)).toFixed(2) : hypeAmount;

          const displayCurrency = showUsd && hypePriceUsd ? "USD" : "HYPE";

          return (
            <Link key={swap.tx_hash} href={`/token/${swap.token}`} onClick={onClose} className="block group">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-card/30 hover:bg-card/50 transition-colors border border-border/30 hover:border-border/60">
                {/* Token Icon */}
                <div className="relative flex-shrink-0">
                  <div className="w-10 h-10 rounded-full border border-border/50 bg-muted overflow-hidden">
                    <Image
                      src={swap.token_image_uri || IMAGES.SWIRL}
                      alt={swap.token_name}
                      width={40}
                      height={40}
                      className="object-cover rounded-full"
                    />
                  </div>
                  {/* Buy/Sell indicator */}
                  <div className="absolute -bottom-1 -right-1">
                    {isBuy ? (
                      <TrendingUp className="w-4 h-4 text-green-400 bg-background/90 rounded-full p-0.5" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-400 bg-background/90 rounded-full p-0.5" />
                    )}
                  </div>
                </div>

                {/* Swap Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span
                      className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                        isBuy
                          ? "bg-green-500/10 text-green-400 border border-green-500/30"
                          : "bg-red-500/10 text-red-400 border border-red-500/30"
                      }`}
                    >
                      {isBuy ? "Buy" : "Sell"}
                    </span>
                    <span className="text-sm font-medium truncate">{swap.token_symbol}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {tokenAmount} {swap.token_symbol} • <ScientificPrice price={pricePerToken} />
                  </div>
                </div>

                {/* Value and Time */}
                <div className="text-right flex-shrink-0">
                  <div className="text-sm font-medium">
                    {showUsd && hypePriceUsd ? "$" : ""}
                    {displayValue} {!showUsd || !hypePriceUsd ? displayCurrency : ""}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <TimeAgoCell timestamp={swap.timestamp} />
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* View More Link if there are more swaps */}
      {response.pagination.totalSwaps > 20 && (
        <div className="mt-4 text-center">
          <span className="text-sm text-muted-foreground">
            Showing latest 20 of {response.pagination.totalSwaps} swaps
          </span>
        </div>
      )}
    </div>
  );
}
