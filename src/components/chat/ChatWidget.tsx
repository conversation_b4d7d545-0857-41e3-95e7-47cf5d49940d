"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Users, GripVertical, Maximize2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Drawer, DrawerContent, DrawerHeader, DrawerTrigger } from "@/components/ui/drawer";
import { useChatStore } from "@/stores/chatStore";
import { useTokensStore } from "@/stores/tokensStore";
import { useChat } from "@/hooks/useChat";
import { useChatHistory } from "@/hooks/useChatHistory";
import { useChatResize } from "@/hooks/useChatResize";
import { useView } from "@/hooks/useView";
import { ChatMessage } from "@/types/chat";
import { ChatInput, ChatInputRef } from "./ChatInput";
import { MessageList } from "./MessageList";
import { ChatRoomSelector } from "./ChatRoomSelector";
import { TypingIndicator } from "./TypingIndicator";
import { cn } from "@/lib/utils";
import { getUserDisplayNameById } from "@/lib/chatUtils";
import { CHAT_CONFIG } from "@/config/chat";
import { TokenDetails } from "@/lib/api/types";
import { useSession } from "next-auth/react";
import { Minus } from "lucide-react";
import { useAccount } from "wagmi";
import { useConnectModal } from "@rainbow-me/rainbowkit";
import { SignInModal } from "@/components/wallet/SignInModal";
import { MobileChatContent } from "./MobileChatContent";

interface ChatWidgetProps {
  roomId?: string;
  className?: string;
  defaultMinimized?: boolean;
  tokenDetails?: TokenDetails | null;
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({
  roomId = CHAT_CONFIG.GLOBAL_ROOM_ID,
  className,
  defaultMinimized = false,
  tokenDetails,
}) => {
  // Ensure room ID is always lowercase for consistency
  const normalizedRoomId = roomId.toLowerCase();
  const [currentRoomId, setCurrentRoomId] = useState(normalizedRoomId);
  const [showRoomSelector, setShowRoomSelector] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [closedDrawerForSignIn, setClosedDrawerForSignIn] = useState(false);
  const chatInputRef = React.useRef<ChatInputRef>(null);

  // Header height - measured from actual rendered component
  const HEADER_HEIGHT = 62;

  const { status: sessionStatus } = useSession();
  const { isMobile } = useView();
  const { address, chainId, isConnected: isWalletConnected } = useAccount();
  const { openConnectModal } = useConnectModal();

  const {
    isConnected,
    activeRooms,
    messages,
    messageHistory,
    typingUsers,
    unreadCounts,
    replyingTo,
    clearUnread,
    setReplyingTo,
    clearReply,
    chatSettings,
    setChatMinimized,
    setChatDimensions,
  } = useChatStore();

  // Not using store here, automatically open chat on page load (Should this be disabled on mobile?)
  const [isMinimized, setIsMinimized] = useState(defaultMinimized);

  // Resize functionality with persisted dimensions
  const { dimensions, chatRef, handleResizeStart, isResizing } = useChatResize({
    initialWidth: chatSettings.hasCustomSize ? chatSettings.dimensions.width : CHAT_CONFIG.DEFAULT_WINDOW_SIZE.width,
    initialHeight: chatSettings.hasCustomSize ? chatSettings.dimensions.height : CHAT_CONFIG.DEFAULT_WINDOW_SIZE.height,
  });

  const { sendMessage, joinRoom, leaveRoom, sendTyping } = useChat(currentRoomId);
  const { loadTokenChatHistory, loadGlobalChatHistory, loading: historyLoading } = useChatHistory();
  const { findToken } = useTokensStore();

  const currentReply = replyingTo[currentRoomId];

  const currentRoom = activeRooms[currentRoomId];
  const roomMessages = messages[currentRoomId] || [];
  const roomTypingUsers = typingUsers[currentRoomId] || [];
  const unreadCount = unreadCounts[currentRoomId] || 0;
  const roomHistory = messageHistory[currentRoomId];
  const hasLoadedInitial = roomHistory?.hasLoadedInitial || false;

  // Determine user capabilities based on session and connection
  const isAuthenticated = sessionStatus === "authenticated";
  const isSessionLoading = sessionStatus === "loading";
  const canSendMessages = isAuthenticated && isConnected;

  // Get the proper room name
  const getRoomName = () => {
    if (currentRoomId === CHAT_CONFIG.GLOBAL_ROOM_ID) {
      return "Global";
    } else {
      // First try to use tokenDetails prop (for token pages)
      if (tokenDetails && tokenDetails.address.toLowerCase() === currentRoomId) {
        return tokenDetails.symbol;
      }
      // Fall back to finding token in store (for other cases)
      const token = findToken(currentRoomId);
      return token?.symbol || "Token";
    }
  };

  // Join room when component mounts or room changes
  useEffect(() => {
    let isMounted = true;

    if (currentRoomId) {
      // Always load chat history, even when disconnected
      // Check if we've already loaded initial history for this room
      const roomHistory = messageHistory[currentRoomId];
      const hasLoadedInitial = roomHistory?.hasLoadedInitial || false;

      if (!hasLoadedInitial) {
        if (currentRoomId === CHAT_CONFIG.GLOBAL_ROOM_ID) {
          if (isMounted) loadGlobalChatHistory({ limit: 50 });
        } else {
          if (isMounted) loadTokenChatHistory(currentRoomId, { limit: 50 });
        }
      }

      // Join room if connected (regardless of authentication for live updates)
      if (isConnected) {
        joinRoom(currentRoomId);
      }
    }

    return () => {
      isMounted = false;
    };
  }, [isConnected, currentRoomId, joinRoom, loadTokenChatHistory, loadGlobalChatHistory, messageHistory]);

  // Clear unread count when chat is opened
  useEffect(() => {
    if ((!isMinimized || mobileDrawerOpen) && currentRoomId) {
      clearUnread(currentRoomId);
    }
  }, [isMinimized, mobileDrawerOpen, currentRoomId, clearUnread]);

  // Save dimensions to store when they change (after user resize)
  useEffect(() => {
    // Only save if dimensions have actually changed from initial values
    const expectedWidth = chatSettings.hasCustomSize
      ? chatSettings.dimensions.width
      : CHAT_CONFIG.DEFAULT_WINDOW_SIZE.width;
    const expectedHeight = chatSettings.hasCustomSize
      ? chatSettings.dimensions.height
      : CHAT_CONFIG.DEFAULT_WINDOW_SIZE.height;
    const hasChanged = dimensions.width !== expectedWidth || dimensions.height !== expectedHeight;

    if (hasChanged) {
      setChatDimensions(dimensions);
    }
  }, [dimensions, setChatDimensions, chatSettings]);

  // Re-open mobile drawer after successful sign-in
  useEffect(() => {
    if (closedDrawerForSignIn && isMobile && canSendMessages) {
      setMobileDrawerOpen(true);
      setClosedDrawerForSignIn(false);
    }
  }, [closedDrawerForSignIn, isMobile, canSendMessages]);

  const handleSendMessage = async (content: string, type?: "text" | "gif", metadata?: { gifUrl?: string }) => {
    try {
      await sendMessage({
        content,
        roomId: currentRoomId,
        type,
        replyTo: currentReply,
        metadata,
      });

      // Clear reply after sending
      if (currentReply) {
        clearReply(currentRoomId);
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleTyping = (isTyping: boolean) => {
    // Only send typing events when authenticated and connected
    if (canSendMessages) {
      sendTyping(currentRoomId, isTyping);
    }
  };

  const handleReply = (message: ChatMessage) => {
    setReplyingTo(currentRoomId, message);
    // Focus the input after setting reply
    setTimeout(() => {
      chatInputRef.current?.focus();
    }, 100);
  };

  const handleCancelReply = () => {
    clearReply(currentRoomId);
  };

  const handleRoomChange = (newRoomId: string) => {
    const normalizedNewRoomId = newRoomId.toLowerCase();
    if (currentRoomId !== normalizedNewRoomId) {
      // Always call leaveRoom - it will handle disconnected state properly
      leaveRoom(currentRoomId);
      setCurrentRoomId(normalizedNewRoomId);
      setShowRoomSelector(false);
    }
  };

  const handleMinimize = () => {
    setIsMinimized(true);
    setChatMinimized(true);
  };

  const handleMaximize = () => {
    setIsMinimized(false);
    setChatMinimized(false);
  };

  const handleSignIn = () => {
    if (isMobile && mobileDrawerOpen) {
      setMobileDrawerOpen(false);
      setClosedDrawerForSignIn(true);
    }
    if (!isWalletConnected) {
      openConnectModal?.();
    } else {
      setIsSignInModalOpen(true);
    }
  };

  return (
    <>
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className={cn("relative", className)}>
        {isMobile ? (
          // Mobile: Use Drawer with optimized chat bar instead of circle button
          <Drawer open={mobileDrawerOpen} onOpenChange={setMobileDrawerOpen}>
            <DrawerTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="fixed bottom-5 right-1 z-30 bg-primary/90 backdrop-blur-sm dark:border-border border border-border hover:bg-primary/80 text-primary-foreground shadow-lg px-3 py-2 rounded-full flex items-center"
              >
                <div className="flex items-center gap-2">
                  <div
                    className={cn(
                      "w-2 h-2 rounded-full",
                      isSessionLoading ? "bg-yellow-500" : canSendMessages ? "bg-green-500" : "bg-red-500",
                    )}
                  ></div>
                  <span className="text-sm font-medium truncate"># {getRoomName()}</span>
                </div>
                <div className="flex items-center gap-2">
                  {currentRoom?.participantCount && (
                    <span className="flex items-center gap-1 text-xs text-primary-foreground/70">
                      <span>{currentRoom.participantCount}</span>
                    </span>
                  )}
                  {unreadCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="text-xs px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center rounded-full"
                    >
                      {unreadCount > 99 ? "99+" : unreadCount}
                    </Badge>
                  )}
                </div>
              </Button>
            </DrawerTrigger>
            <DrawerContent className="bg-backgroundDark border-t border-border max-h-[85vh] h-[85vh] overflow-y-auto">
              <DrawerHeader className="text-center p-2"></DrawerHeader>

              <div className="flex-1 overflow-hidden">
                <MobileChatContent
                  isSessionLoading={isSessionLoading}
                  canSendMessages={canSendMessages}
                  showRoomSelector={showRoomSelector}
                  setShowRoomSelector={setShowRoomSelector}
                  isMinimized={isMinimized}
                  getRoomName={getRoomName}
                  unreadCount={unreadCount}
                  currentRoom={currentRoom}
                  handleConnectAndSignWallet={handleSignIn}
                  isAuthenticated={isAuthenticated}
                  currentRoomId={currentRoomId}
                  originalPageRoomId={normalizedRoomId}
                  handleRoomChange={handleRoomChange}
                  tokenDetails={tokenDetails}
                  roomMessages={roomMessages}
                  historyLoading={historyLoading}
                  hasLoadedInitial={hasLoadedInitial}
                  handleReply={handleReply}
                  roomTypingUsers={roomTypingUsers}
                  chatInputRef={chatInputRef}
                  handleSendMessage={handleSendMessage}
                  handleTyping={handleTyping}
                  currentReply={currentReply}
                  handleCancelReply={handleCancelReply}
                />
              </div>
            </DrawerContent>
          </Drawer>
        ) : (
          // Desktop chat widget
          <div
            ref={chatRef}
            style={{
              // Arbitrary values instead of "auto" to attempt to have a nice transition
              width: isMinimized ? "165px" : dimensions.width,
              height: isMinimized ? "40px" : dimensions.height,
            }}
            className={cn("relative", !isResizing && "transition-all duration-300 ease-in-out")}
          >
            <Card className="w-full h-full bg-card border-border pb-0 pt-0 overflow-hidden shadow-lg">
              {/* Header */}
              <div
                className={cn(
                  "flex items-center justify-between px-3 py-2 bg-card/50 backdrop-blur-sm cursor-pointer hover:bg-card/70 transition-colors group",
                  !isMinimized && "border-b border-border",
                )}
                onClick={isMinimized ? handleMaximize : handleMinimize}
              >
                <div className="flex items-center space-x-1">
                  <div className="flex items-center space-x-1">
                    <div
                      className={cn(
                        "w-2 h-2 rounded-full",
                        isSessionLoading ? "bg-yellow-500" : canSendMessages ? "bg-green-500" : "bg-red-500",
                      )}
                    ></div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent header click
                        if (isMinimized) {
                          handleMaximize();
                        } else {
                          setShowRoomSelector(!showRoomSelector);
                        }
                      }}
                      className={`text-sm font-medium text-foreground group-hover:text-primary transition-colors h-6 px-2 ${
                        isMinimized ? "dark:hover:bg-transparent" : ""
                      }`}
                    >
                      <div className="flex items-center space-x-1">
                        <span># {getRoomName()}</span>
                        {unreadCount > 0 && (
                          <Badge
                            variant="destructive"
                            className="text-xs px-1.5 py-0.5 min-w-[16px] h-[16px] dark:bg-destructive flex items-center justify-center"
                          >
                            {unreadCount > 99 ? "99+" : unreadCount}
                          </Badge>
                        )}
                      </div>
                      {currentRoom?.participantCount && (
                        <span className="flex items-center space-x-1 text-xs text-muted-foreground">
                          <Users className="size-3.5" />
                          <span>{currentRoom.participantCount}</span>
                        </span>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Right side - Authentication status, loading, or minimize/maximize hint */}
                <div className="flex items-center space-x-2">
                  {!canSendMessages && !isSessionLoading ? (
                    <Badge variant="destructive" className="text-xs">
                      {!isAuthenticated ? (
                        <div
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent header click
                            handleSignIn();
                          }}
                          className="cursor-pointer hover:opacity-80 transition-opacity"
                        >
                          Sign in to chat
                        </div>
                      ) : (
                        "Connecting..."
                      )}
                    </Badge>
                  ) : isSessionLoading ? (
                    <Badge variant="secondary" className="text-xs">
                      Loading...
                    </Badge>
                  ) : (
                    /* Show expand/minimize hint when authenticated and connected */
                    <div className="text-xs text-muted-foreground opacity-60 group-hover:text-white group-hover:opacity-100 transition-all duration-200">
                      {isMinimized ? (
                        <Maximize2 className={`w-3 h-3 ${unreadCount > 0 ? "hidden" : ""}`} />
                      ) : (
                        <Minus className="w-3 h-3" />
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Room Selector - Only show when not minimized */}
              <AnimatePresence>
                {showRoomSelector && !isMinimized && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-b border-border"
                  >
                    <ChatRoomSelector
                      currentRoomId={currentRoomId}
                      originalPageRoomId={normalizedRoomId}
                      onRoomSelect={handleRoomChange}
                      onClose={() => setShowRoomSelector(false)}
                      tokenDetails={tokenDetails}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Chat Content - Only show when not minimized */}
              {!isMinimized && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: dimensions.height - HEADER_HEIGHT, opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2, ease: "easeInOut" }}
                  className="flex flex-col overflow-hidden"
                >
                  {/* Messages - Takes remaining space with strict height control */}
                  <div
                    className="flex-1 bg-background/30"
                    style={{
                      minHeight: 0,
                      maxHeight: dimensions.height - HEADER_HEIGHT, // Fixed: subtract header height
                      overflow: "hidden",
                    }}
                  >
                    <MessageList
                      messages={roomMessages}
                      roomId={currentRoomId}
                      className="h-full"
                      loading={historyLoading}
                      hasLoadedInitial={hasLoadedInitial}
                      onReply={handleReply}
                      typingUsers={roomTypingUsers}
                      tokenCreator={tokenDetails?.creator}
                    />
                  </div>

                  {/* Typing Indicator - Fixed at bottom above input */}
                  {roomTypingUsers.length > 0 && (
                    <div className="px-4 py-2 border-t border-border/50 bg-background/50 flex-shrink-0">
                      <TypingIndicator
                        users={roomTypingUsers}
                        getUserDisplayName={(userId) => getUserDisplayNameById(userId, roomMessages)}
                      />
                    </div>
                  )}

                  {/* Input - Always locked at bottom */}
                  <div className="bg-background/50 flex-shrink-0">
                    <ChatInput
                      ref={chatInputRef}
                      onSendMessage={handleSendMessage}
                      onTyping={handleTyping}
                      replyingTo={currentReply}
                      onCancelReply={handleCancelReply}
                      placeholder={`Message #${getRoomName()}`}
                      disabled={!canSendMessages}
                    />
                  </div>
                </motion.div>
              )}

              {/* Resize Handle - Only show when not minimized */}
              {!isMinimized && (
                <div
                  className={cn(
                    "absolute top-0 left-0 cursor-nw-resize opacity-60 hover:opacity-100 transition-opacity z-10 flex items-center justify-center",
                    isMobile ? "w-10 h-10" : "w-6 h-6",
                  )}
                  onMouseDown={handleResizeStart}
                  onTouchStart={handleResizeStart}
                >
                  <GripVertical className={cn("text-muted-foreground rotate-45", isMobile ? "w-4 h-4" : "w-3 h-3")} />
                </div>
              )}
            </Card>
          </div>
        )}
      </motion.div>
      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
        address={address}
        chainId={chainId}
      />
    </>
  );
};
