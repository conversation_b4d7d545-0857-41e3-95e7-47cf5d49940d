"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Reply, <PERSON>, Smile, RotateCcw, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChatMessage } from "@/types/chat";
import { formatMessageTime, getUserDisplayName, getUserAvatarSeed, truncateAddress } from "@/lib/chatUtils";
import Avatar from "boring-avatars";
import { useChatReactions } from "@/hooks/useChatReactions";
import { useChat } from "@/hooks/useChat";
import { useAccount } from "wagmi";
import { cn } from "@/lib/utils";
import { CHAT_CONFIG } from "@/config/chat";
import { MessageContent } from "./MessageContent";

interface MessageBubbleProps {
  message: ChatMessage;
  isGrouped: boolean;
  roomId: string;
  onReply?: (message: ChatMessage) => void;
  onScrollToMessage?: (messageId: string) => void;
  onImageLoad?: () => void;
  className?: string;
  tokenCreator?: string; // Token creator address for dev badge
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isGrouped,
  roomId,
  onReply,
  onScrollToMessage,
  onImageLoad,
  className,
  tokenCreator,
}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [imageLoading, setImageLoading] = useState(message.type === "gif" && !!message.metadata?.gifUrl);
  const { address: userAddress } = useAccount();
  const { toggleReaction, hasUserReacted } = useChatReactions();
  const { retryMessage } = useChat();

  // Check if this message is from the current user
  const isOwnMessage = userAddress && message.user.address.toLowerCase() === userAddress.toLowerCase();
  const isFailed = message.metadata?.failed === true;

  // Check if this user is the token creator
  const isTokenCreator = tokenCreator && message.user.address.toLowerCase() === tokenCreator.toLowerCase();

  // Check if we should show balance info (not on global chat)
  const isGlobalChat = roomId === CHAT_CONFIG.GLOBAL_ROOM_ID;
  const shouldShowBalance = !isGlobalChat && message.balance && parseFloat(message.balance) > 0;

  // Check if user has no balance (only relevant for token-specific chats)
  const hasNoBalance = !isGlobalChat && (!message.balance || parseFloat(message.balance) === 0);

  const handleReaction = (emoji: string) => {
    if (!userAddress) return;
    toggleReaction(message.id, emoji);
  };

  const handleReplyClick = () => {
    if (onReply) {
      onReply(message);
    }
  };

  const handleRetry = async () => {
    if (!isOwnMessage || !isFailed) return;

    setIsRetrying(true);
    try {
      await retryMessage(message.id, roomId);
    } catch (error) {
      console.error("Failed to retry message:", error);
    } finally {
      setIsRetrying(false);
    }
  };

  const renderMessageContent = () => {
    if (message.type === "gif" && message.metadata?.gifUrl) {
      return (
        <div className="max-w-xs relative">
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted/50 rounded-lg">
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
          <img
            src={message.metadata.gifUrl}
            alt="GIF"
            className="rounded-lg max-w-full h-auto"
            onLoad={() => {
              setImageLoading(false);
              onImageLoad?.();
            }}
            onError={() => {
              setImageLoading(false);
            }}
          />
        </div>
      );
    }

    return <MessageContent content={message.content} />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "group relative py-1 transition-colors",
        // Subtle hover background for all messages - full width
        "hover:bg-accent/5",
        // Padding: normal for others, adjusted for own messages
        "pr-4 pl-3 py-[0.36rem]",
        // Failed messages styling
        isFailed && "bg-destructive/5 border-l-2 border-destructive/30",
        // Reduced prominence for users with no balance
        hasNoBalance && "opacity-60",
        className
      )}
    >
      <div className={cn("flex items-start space-x-3")}>
        {/* Avatar */}
        {!isGrouped && (
          <div
            className={cn(
              "w-10 h-10 rounded-full overflow-hidden border-2 mt-0.5 mb-2 flex-shrink-0",
              hasNoBalance ? "border-muted/30" : "border-primary/20"
            )}
          >
            <div className={cn(hasNoBalance && "opacity-60")}>
              <Avatar name={getUserAvatarSeed(message.user)} variant="beam" size={40} />
            </div>
          </div>
        )}
        {isGrouped && <div className="w-10" />}

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          {!isGrouped && (
            <div className={cn("flex items-baseline space-x-2")}>
              <div className="flex items-center space-x-2">
                <span
                  className={cn(
                    "text-sm font-semibold hover:underline cursor-pointer",
                    isOwnMessage ? "text-primary" : hasNoBalance ? "text-muted-foreground" : "text-foreground"
                  )}
                >
                  {getUserDisplayName(message.user)}
                </span>
                {isTokenCreator && (
                  <Badge
                    variant="secondary"
                    className="text-xs px-1.5 py-0.5 h-5 bg-primary/20 text-primary border-primary/30"
                  >
                    dev
                  </Badge>
                )}
                {hasNoBalance && (
                  <div className="w-1 h-1 rounded-full bg-muted-foreground/40" title="No token balance" />
                )}
              </div>
              {shouldShowBalance && (
                <div className="flex items-center space-x-1">
                  <div className="w-1.5 h-1.5 rounded-full bg-accent"></div>
                  {message.balancePercent > 0 && (
                    <span className="text-xs text-accent/70">({message.balancePercent.toFixed(2)}%)</span>
                  )}
                </div>
              )}
              <span className="text-xs text-muted-foreground/70 hover:text-muted-foreground transition-colors cursor-pointer">
                {formatMessageTime(message.timestamp)}
              </span>
              {!message.isConfirmed && !isFailed && (
                <div className="flex items-center space-x-1">
                  <div className="w-1 h-1 rounded-full bg-yellow-500 animate-pulse"></div>
                  <span className="text-xs text-yellow-500">Sending</span>
                </div>
              )}
              {isFailed && isOwnMessage && (
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="w-3 h-3 text-destructive" />
                    <span className="text-xs text-destructive">Failed</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-2 text-xs text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={handleRetry}
                    disabled={isRetrying}
                  >
                    {isRetrying ? (
                      <RotateCcw className="w-3 h-3 animate-spin" />
                    ) : (
                      <>
                        <RotateCcw className="w-3 h-3 mr-1" />
                        Retry
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Reply indicator */}
          {message.replyTo && (
            <div
              className="flex items-center space-x-2 mb-1 ml-1 mt-0.5 cursor-pointer hover:bg-accent/20 rounded-md p-1 -m-1 transition-colors"
              onClick={() => onScrollToMessage?.(message.replyTo!.id)}
              title="Click to jump to original message"
            >
              <div className="w-4 h-4 flex items-center justify-center">
                <Reply className="w-3 h-3 text-muted-foreground/50" />
              </div>
              <span className="text-xs text-muted-foreground/70 font-medium hover:text-foreground transition-colors">
                {truncateAddress(message.replyTo.username)}
              </span>
              <span className="text-xs text-muted-foreground/50 truncate max-w-xs">{message.replyTo.message}</span>
            </div>
          )}

          {/* Message Body */}
          <div className={cn(hasNoBalance && "text-muted-foreground/80")}>{renderMessageContent()}</div>

          {/* Reactions */}
          {Object.keys(message.reactions).length > 0 && (
            <div className={cn("flex flex-wrap gap-1 mt-2")}>
              {Object.entries(message.reactions).map(([emoji, users]) => (
                <button
                  key={emoji}
                  onClick={() => handleReaction(emoji)}
                  className={cn(
                    "inline-flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium transition-all hover:scale-105",
                    hasUserReacted(message.reactions, emoji, userAddress || "")
                      ? "bg-primary/15 text-primary border border-primary/30 shadow-sm"
                      : "bg-background/80 hover:bg-accent/50 border border-border/50"
                  )}
                >
                  <span className="text-sm">{emoji}</span>
                  <span className="text-xs font-semibold">{users.length}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Actions */}
        <div
          className={cn(
            "absolute top-0 opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-y-[-50%] right-1"
          )}
        >
          <div className="flex items-center space-x-1 bg-background border border-border rounded-lg shadow-lg px-1 py-1">
            {/* Heart button */}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-accent/50"
              onClick={() => handleReaction("❤️")}
            >
              <Heart className="w-4 h-4" />
            </Button>

            {/* Emoji picker */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-accent/50">
                  <Smile className="w-4 h-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-3 border-border shadow-xl" side="top">
                <div className="grid grid-cols-4 gap-2">
                  {["👍", "👎", "❤️", "😂", "😮", "😢", "😡", "🔥"].map((emoji) => (
                    <button
                      key={emoji}
                      onClick={() => handleReaction(emoji)}
                      className="w-10 h-10 flex items-center justify-center hover:bg-accent rounded-lg text-lg transition-all hover:scale-110"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            {/* Reply button */}
            {onReply && (
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-accent/50" onClick={handleReplyClick}>
                <Reply className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
