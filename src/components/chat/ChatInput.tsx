"use client";

import React, { useState, useRef, useCallback, useImperativeHandle } from "react";
import { Send, Smile, Image as ImageIcon, Reply, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { validateMessageContent, truncateAddress } from "@/lib/chatUtils";
import { CHAT_CONFIG } from "@/config/chat";
import { cn } from "@/lib/utils";
import { TextareaChat } from "@/components/ui/textarea-chat";

interface ChatInputProps {
  onSendMessage: (content: string, type?: "text" | "gif", metadata?: { gifUrl?: string }) => void;
  onTyping: (isTyping: boolean) => void;
  replyingTo?: {
    id: string;
    username: string;
    message: string;
    tokenaddress: string;
  } | null;
  onCancelReply?: () => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export interface ChatInputRef {
  focus: () => void;
}

export const ChatInput = React.forwardRef<ChatInputRef, ChatInputProps>(
  (
    {
      onSendMessage,
      onTyping,
      replyingTo,
      onCancelReply,
      placeholder = "Type a message...",
      disabled = false,
      className,
    },
    ref,
  ) => {
    const [message, setMessage] = useState("");
    const [isTyping, setIsTyping] = useState(false);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [showGifPicker, setShowGifPicker] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const typingTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

    // Expose focus method through ref
    useImperativeHandle(ref, () => ({
      focus: () => {
        textareaRef.current?.focus();
      },
    }));

    const handleInputChange = useCallback(
      (value: string) => {
        setMessage(value);

        // Handle typing indicator
        if (value.trim() && !isTyping) {
          setIsTyping(true);
          onTyping(true);
        }

        // Clear existing timeout
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }

        // Set new timeout to stop typing indicator
        typingTimeoutRef.current = setTimeout(() => {
          setIsTyping(false);
          onTyping(false);
        }, CHAT_CONFIG.TYPING_DEBOUNCE_MS);

        // Auto-resize textarea
        if (textareaRef.current) {
          textareaRef.current.style.height = "auto";
          textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
        }
      },
      [isTyping, onTyping],
    );

    const handleSendMessage = useCallback(() => {
      const trimmedMessage = message.trim();
      if (!trimmedMessage || disabled) return;

      const validation = validateMessageContent(trimmedMessage);
      if (!validation.isValid) {
        console.error("Invalid message:", validation.error);
        return;
      }

      onSendMessage(trimmedMessage);
      setMessage("");

      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        onTyping(false);
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Focus back to input
      textareaRef.current?.focus();
    }, [message, disabled, onSendMessage, isTyping, onTyping]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          handleSendMessage();
        }
      },
      [handleSendMessage],
    );

    const handleEmojiSelect = useCallback(
      (emoji: string) => {
        const newMessage = message + emoji;
        setMessage(newMessage);
        setShowEmojiPicker(false);
        textareaRef.current?.focus();
      },
      [message],
    );

    const handleGifSelect = useCallback(
      (gifUrl: string) => {
        onSendMessage("", "gif", { gifUrl });
        setShowGifPicker(false);
        textareaRef.current?.focus();
      },
      [onSendMessage],
    );

    // Cleanup timeout on unmount
    React.useEffect(() => {
      return () => {
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      };
    }, []);

    const isMessageValid = message.trim().length > 0 && message.length <= CHAT_CONFIG.MAX_MESSAGE_LENGTH;

    return (
      <div className={cn("bg-background/30 backdrop-blur-sm", className)}>
        {/* Reply indicator */}
        {replyingTo && (
          <div className="px-3 py-2 bg-accent/10 border-b border-border/30">
            <div className="flex items-start justify-between gap-2">
              <div className="flex items-start space-x-2 flex-1 min-w-0">
                <Reply className="w-3 h-3 text-muted-foreground mt-0.5 shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-xs text-muted-foreground">
                    Replying to{" "}
                    <span className="font-medium text-foreground">{truncateAddress(replyingTo.username)}</span>
                  </div>
                  <div className="text-xs text-foreground/70 truncate">{replyingTo.message}</div>
                </div>
              </div>
              {onCancelReply && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 shrink-0 hover:bg-destructive/20"
                  onClick={onCancelReply}
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
        )}

        <div className="p-3">
          <div className="flex items-end space-x-2 rounded-[0.4rem] shadow-[inset_0_0_0_1px_hsl(var(--border))] hover:shadow-[inset_0_0_0_2px_hsl(var(--primary)_/_0.3)] focus-within:shadow-[inset_0_0_0_2px_hsl(var(--primary)_/_0.6),0_0_0_1px_hsl(var(--primary)_/_0.6)] dark:bg-input/40 transition-shadow duration-200 hover:focus-within:shadow-[inset_0_0_0_2px_hsl(var(--primary)_/_0.6),0_0_0_1px_hsl(var(--primary)_/_0.6)]">
            {/* Message Input */}
            <div className="flex-1 relative">
              <TextareaChat
                ref={textareaRef}
                value={message}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={disabled}
                className="min-h-[40px] max-h-[100px] resize-none border-0 bg-transparent text-sm placeholder:text-muted-foreground/50 focus-visible:ring-0 focus-visible:ring-offset-0"
                rows={1}
              />

              {/* Character Count */}
              {message.length > CHAT_CONFIG.MAX_MESSAGE_LENGTH * 0.8 && (
                <div className="absolute bottom-1 right-12 text-xs text-muted-foreground/60">
                  {message.length}/{CHAT_CONFIG.MAX_MESSAGE_LENGTH}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-1 shrink-0">
              {/* Emoji Picker */}
              <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-accent/50" disabled={disabled}>
                    <Smile className="w-4 h-4 text-muted-foreground" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0 border-border shadow-xl" side="top">
                  <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                </PopoverContent>
              </Popover>

              {/* GIF Picker */}
              {CHAT_CONFIG.ENABLE_GIFS && (
                <Popover open={showGifPicker} onOpenChange={setShowGifPicker}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-accent/50" disabled={disabled}>
                      <ImageIcon className="w-4 h-4 text-muted-foreground" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0 border-border shadow-xl" side="top">
                    <GifPicker onGifSelect={handleGifSelect} />
                  </PopoverContent>
                </Popover>
              )}

              {/* Send Button */}
              <Button
                onClick={handleSendMessage}
                disabled={!isMessageValid || disabled}
                size="sm"
                className={cn(
                  "h-7 w-7 p-0 shrink-0 transition-all rounded-md",
                  isMessageValid
                    ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm"
                    : "bg-muted/50 text-muted-foreground cursor-not-allowed",
                )}
              >
                <Send className="w-3.5 h-3.5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ChatInput.displayName = "ChatInput";

// Simple emoji picker component
const EmojiPicker: React.FC<{ onEmojiSelect: (emoji: string) => void }> = ({ onEmojiSelect }) => {
  const emojiCategories = {
    "Frequently Used": ["😀", "😂", "❤️", "👍", "👎", "🔥", "💯", "🎉"],
    Smileys: [
      "😀",
      "😃",
      "😄",
      "😁",
      "😆",
      "😅",
      "😂",
      "🤣",
      "😊",
      "😇",
      "🙂",
      "🙃",
      "😉",
      "😌",
      "😍",
      "🥰",
      "😘",
      "😗",
      "😙",
      "😚",
      "😋",
      "😛",
      "😝",
      "😜",
    ],
    Gestures: [
      "👍",
      "👎",
      "👌",
      "✌️",
      "🤞",
      "🤟",
      "🤘",
      "🤙",
      "👈",
      "👉",
      "👆",
      "👇",
      "☝️",
      "👋",
      "🤚",
      "🖐️",
      "✋",
      "🖖",
      "👏",
      "🙌",
      "🤲",
      "🤝",
      "🙏",
    ],
    Hearts: [
      "❤️",
      "🧡",
      "💛",
      "💚",
      "💙",
      "💜",
      "🖤",
      "🤍",
      "🤎",
      "💔",
      "❣️",
      "💕",
      "💞",
      "💓",
      "💗",
      "💖",
      "💘",
      "💝",
      "💟",
    ],
    Objects: [
      "⭐",
      "🌟",
      "✨",
      "⚡",
      "☄️",
      "💥",
      "🔥",
      "🌪️",
      "🌈",
      "☀️",
      "🎉",
      "🎊",
      "💯",
      "💎",
      "🚀",
      "💰",
      "💸",
      "📈",
      "📉",
    ],
  };

  return (
    <div className="p-4 max-h-80 overflow-y-auto no-scrollbar">
      {Object.entries(emojiCategories).map(([category, emojis]) => (
        <div key={category} className="mb-4">
          <h3 className="text-xs font-semibold text-muted-foreground mb-2 uppercase tracking-wide">{category}</h3>
          <div className="grid grid-cols-8 gap-1">
            {emojis.map((emoji, index) => (
              <button
                key={`${category}-${index}`}
                onClick={() => onEmojiSelect(emoji)}
                className="w-9 h-9 flex items-center justify-center hover:bg-accent rounded-lg text-lg transition-all hover:scale-110 active:scale-95"
              >
                {emoji}
              </button>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Simple GIF picker component (placeholder)
const GifPicker: React.FC<{ onGifSelect: (gifUrl: string) => void }> = ({ onGifSelect }) => {
  const sampleGifs = [
    "https://media.giphy.com/media/3o7abKhOpu0NwenH3O/giphy.gif",
    "https://media.giphy.com/media/l0MYt5jPR6QX5pnqM/giphy.gif",
    "https://media.giphy.com/media/3o6Zt4HU9uwXmXSAuI/giphy.gif",
  ];

  return (
    <div className="p-4">
      <div className="text-sm text-muted-foreground mb-3">Popular GIFs</div>
      <div className="grid grid-cols-2 gap-2">
        {sampleGifs.map((gifUrl, index) => (
          <button
            key={index}
            onClick={() => onGifSelect(gifUrl)}
            className="aspect-square bg-muted rounded overflow-hidden hover:opacity-80 transition-opacity"
          >
            <img src={gifUrl} alt={`GIF ${index + 1}`} className="w-full h-full object-cover" />
          </button>
        ))}
      </div>
      <div className="text-xs text-muted-foreground mt-2 text-center">GIF integration coming soon</div>
    </div>
  );
};
