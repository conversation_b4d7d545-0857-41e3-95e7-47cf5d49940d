import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChatInput, ChatInputRef } from "./ChatInput";
import { MessageList } from "./MessageList";
import { ChatRoomSelector } from "./ChatRoomSelector";
import { TypingIndicator } from "./TypingIndicator";
import { cn } from "@/lib/utils";
import { getUserDisplayNameById } from "@/lib/chatUtils";
import { TokenDetails } from "@/lib/api/types";
import { ChatMessage, TypingIndicator as TypingIndicatorType } from "@/types/chat";

interface MobileChatContentProps {
  isSessionLoading: boolean;
  canSendMessages: boolean;
  showRoomSelector: boolean;
  setShowRoomSelector: (value: boolean) => void;
  isMinimized: boolean;
  getRoomName: () => string;
  unreadCount: number;
  currentRoom: { participantCount: number } | undefined;
  handleConnectAndSignWallet: () => void;
  isAuthenticated: boolean;
  currentRoomId: string;
  originalPageRoomId: string;
  handleRoomChange: (roomId: string) => void;
  tokenDetails: TokenDetails | null | undefined;
  roomMessages: ChatMessage[];
  historyLoading: boolean;
  hasLoadedInitial: boolean;
  handleReply: (message: ChatMessage) => void;
  roomTypingUsers: TypingIndicatorType[];
  chatInputRef: React.RefObject<ChatInputRef>;
  handleSendMessage: (content: string, type?: "text" | "gif", metadata?: { gifUrl?: string }) => void;
  handleTyping: (isTyping: boolean) => void;
  currentReply: ChatMessage["replyTo"];
  handleCancelReply: () => void;
}

export const MobileChatContent: React.FC<MobileChatContentProps> = ({
  isSessionLoading,
  canSendMessages,
  showRoomSelector,
  setShowRoomSelector,
  isMinimized,
  getRoomName,
  unreadCount,
  currentRoom,
  handleConnectAndSignWallet,
  isAuthenticated,
  currentRoomId,
  originalPageRoomId,
  handleRoomChange,
  tokenDetails,
  roomMessages,
  historyLoading,
  hasLoadedInitial,
  handleReply,
  roomTypingUsers,
  chatInputRef,
  handleSendMessage,
  handleTyping,
  currentReply,
  handleCancelReply,
}) => (
  <div className="h-full flex flex-col">
    <Card className="h-full bg-card border-border overflow-hidden shadow-lg pt-0 flex flex-col rounded-none">
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-border bg-card/50 backdrop-blur-sm flex-shrink-0">
        <div className="flex items-center space-x-1">
          <div className="flex items-center space-x-1">
            <div
              className={cn(
                "w-2 h-2 rounded-full",
                isSessionLoading ? "bg-yellow-500" : canSendMessages ? "bg-green-500" : "bg-red-500"
              )}
            ></div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowRoomSelector(!showRoomSelector)}
              className={`text-sm font-medium text-foreground hover:text-primary transition-colors h-6 px-2 ${
                isMinimized ? "dark:hover:bg-transparent" : ""
              }`}
            >
              <div className="flex items-center space-x-1">
                <span># {getRoomName()}</span>
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="text-xs px-1.5 py-0.5 min-w-[16px] h-[16px] dark:bg-destructive flex items-center justify-center"
                  >
                    {unreadCount > 99 ? "99+" : unreadCount}
                  </Badge>
                )}
              </div>
              {currentRoom?.participantCount && (
                <span className=" flex items-center space-x-1 text-xs text-muted-foreground">
                  <Users className="size-3.5" />
                  <span>{currentRoom.participantCount}</span>
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Right side - Authentication status or connection info */}
        <div className="flex items-center space-x-1">
          {!canSendMessages && !isSessionLoading ? (
            <Badge variant="destructive" className="text-xs">
              {!isAuthenticated ? (
                <div
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent header click
                    handleConnectAndSignWallet();
                  }}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                >
                  Sign in to chat
                </div>
              ) : (
                "Connecting..."
              )}
            </Badge>
          ) : isSessionLoading ? (
            <Badge variant="secondary" className="text-xs">
              Loading...
            </Badge>
          ) : null}
        </div>
      </div>

      {/* Room Selector */}
      <AnimatePresence>
        {showRoomSelector && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-border flex-shrink-0"
          >
            <ChatRoomSelector
              currentRoomId={currentRoomId}
              originalPageRoomId={originalPageRoomId}
              onRoomSelect={handleRoomChange}
              onClose={() => setShowRoomSelector(false)}
              tokenDetails={tokenDetails}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Content - Takes remaining space */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Messages - Takes remaining space with strict height control */}
        <div className="flex-1 bg-background/30 overflow-hidden">
          <MessageList
            messages={roomMessages}
            roomId={currentRoomId}
            className="h-full"
            loading={historyLoading}
            hasLoadedInitial={hasLoadedInitial}
            onReply={handleReply}
            typingUsers={roomTypingUsers}
            tokenCreator={tokenDetails?.creator}
          />
        </div>

        {/* Typing Indicator - Fixed at bottom above input */}
        {roomTypingUsers.length > 0 && (
          <div className="px-4 py-2 border-t border-border/50 bg-background/50 flex-shrink-0">
            <TypingIndicator
              users={roomTypingUsers}
              getUserDisplayName={(userId) => getUserDisplayNameById(userId, roomMessages)}
            />
          </div>
        )}

        {/* Input - Always locked at bottom */}
        <div className="bg-background/50 flex-shrink-0">
          <ChatInput
            ref={chatInputRef}
            onSendMessage={handleSendMessage}
            onTyping={handleTyping}
            replyingTo={currentReply}
            onCancelReply={handleCancelReply}
            placeholder={`Message #${getRoomName()}`}
            disabled={!canSendMessages}
          />
        </div>
      </div>
    </Card>
  </div>
);
