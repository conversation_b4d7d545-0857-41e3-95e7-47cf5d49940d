"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Search, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { searchTokens } from "@/app/actions";
import { SearchToken } from "@/lib/api/types";
import Image from "next/image";

interface TokenSearchProps {
  className?: string;
}

interface TokenItemProps {
  token: SearchToken;
  onSelect: (token: SearchToken) => void;
}

// Separate component for token items - better organization
function TokenItem({ token, onSelect }: TokenItemProps) {
  return (
    <CommandItem
      key={token.address}
      value={`${token.name} ${token.symbol} ${token.address}`}
      onSelect={() => onSelect(token)}
      className="flex items-center gap-4 p-4"
    >
      <div className="flex-shrink-0">
        {token.image_uri ? (
          <Image src={token.image_uri} alt={token.name} width={40} height={40} className="rounded-full" />
        ) : (
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/60 flex items-center justify-center">
            <span className="text-sm font-semibold text-white">{token.symbol.charAt(0).toUpperCase()}</span>
          </div>
        )}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-semibold text-foreground truncate">{token.name}</span>
          <span className="text-sm text-muted-foreground font-medium">{token.symbol}</span>
        </div>
        <span className="text-xs text-muted-foreground font-mono break-all">{token.address}</span>
      </div>
    </CommandItem>
  );
}

// Loading state component
function SearchLoading() {
  return (
    <div className="flex items-center justify-center py-8">
      <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
      <span className="ml-3 text-sm text-muted-foreground">Searching tokens...</span>
    </div>
  );
}

// Empty state component
function SearchEmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
      <div className="rounded-full bg-muted p-4 mb-4">
        <Search className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">Start searching for tokens</h3>
      <p className="text-sm text-muted-foreground max-w-sm">
        Enter a token name, symbol, or contract address to find tokens.
      </p>
    </div>
  );
}

export function TokenSearch({ className }: TokenSearchProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchToken[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Debounced search function
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await searchTokens({
        query: searchQuery.trim().toLowerCase(),
        limit: 10,
      });
      setResults(response.tokens);
    } catch (error) {
      console.error("Search error:", error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Debounce search queries
  useEffect(() => {
    const timer = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, performSearch]);

  // Handle token selection
  const handleSelectToken = useCallback(
    (token: SearchToken) => {
      setOpen(false);
      setQuery("");
      setResults([]);
      router.push(`/token/${token.address}`);
    },
    [router],
  );

  // Keyboard shortcut to open search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((prev) => !prev);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setQuery("");
      setResults([]);
      setIsLoading(false);
    }
  }, [open]);

  return (
    <>
      <Button
        variant="outline"
        className={`relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2 rounded-full ${className}`}
        onClick={() => setOpen(true)}
      >
        <Search className="h-4 w-4 xl:mr-2" />
        <span className="hidden xl:inline-flex">Search tokens...</span>
        <kbd className="pointer-events-none absolute right-3 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder="Search by token name, symbol, or address..."
          value={query}
          onValueChange={setQuery}
          className="border-0 focus:ring-0"
        />
        <CommandList className="max-h-[400px]">
          {isLoading && <SearchLoading />}

          {!isLoading && query && results.length === 0 && <CommandEmpty>No tokens found for "{query}"</CommandEmpty>}

          {!isLoading && results.length > 0 && (
            <CommandGroup heading="Search Results" className="p-2">
              {results.map((token) => (
                <TokenItem key={token.address} token={token} onSelect={handleSelectToken} />
              ))}
            </CommandGroup>
          )}

          {!query && <SearchEmptyState />}
        </CommandList>
      </CommandDialog>
    </>
  );
}
