"use client";

import React, { useState } from "react";
import { ExternalLink, Globe, Coins } from "lucide-react";
import { cn } from "@/lib/utils";
import { FaXTwitter } from "react-icons/fa6";
import { FaTelegram, FaDiscord } from "react-icons/fa";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface LinkEmbedProps {
  url: string;
  text?: string;
  className?: string;
}

interface LinkInfo {
  icon: React.ReactNode;
  domain: string;
  displayText: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

const getLinkInfo = (url: string, text?: string): LinkInfo => {
  try {
    const parsedUrl = new URL(url);
    const domain = parsedUrl.hostname.toLowerCase();
    const pathname = parsedUrl.pathname;

    // Helper function to create a smart display text for URLs
    const getSmartDisplayText = (url: string, text?: string): string => {
      if (text) return text;

      // If URL is too long, show domain + truncated path
      if (url.length > 50) {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;
        const path = urlObj.pathname;

        if (path.length > 20) {
          const truncatedPath = path.slice(0, 15) + "..." + path.slice(-5);
          return `${domain}${truncatedPath}`;
        }
        return `${domain}${path}`;
      }

      return url;
    };

    // X.com / Twitter links
    if (domain === "x.com" || domain === "twitter.com") {
      return {
        icon: <FaXTwitter className="w-4 h-4" />,
        domain: "x.com",
        displayText: getSmartDisplayText(url, text),
        color: "text-blue-400",
        bgColor: "bg-blue-500/10",
        borderColor: "border-blue-500/20",
      };
    }

    // LiquidLaunch token links
    if (domain.includes("liquidlaunch.app") && pathname.includes("/token/")) {
      const tokenAddress = pathname.split("/token/")[1];
      const shortAddress = tokenAddress ? `${tokenAddress.slice(0, 6)}...${tokenAddress.slice(-4)}` : "";

      return {
        icon: <Coins className="w-4 h-4" />,
        domain: "liquidlaunch.app",
        displayText: text || `Token ${shortAddress}`,
        color: "text-primary",
        bgColor: "bg-primary/10",
        borderColor: "border-primary/20",
      };
    }

    // GitHub links
    if (domain === "github.com") {
      return {
        icon: (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
          </svg>
        ),
        domain: "github.com",
        displayText: getSmartDisplayText(url, text),
        color: "text-gray-300",
        bgColor: "bg-gray-500/10",
        borderColor: "border-gray-500/20",
      };
    }

    // YouTube links
    if (domain === "youtube.com" || domain === "youtu.be") {
      return {
        icon: (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
          </svg>
        ),
        domain: "youtube.com",
        displayText: getSmartDisplayText(url, text),
        color: "text-red-500",
        bgColor: "bg-red-500/10",
        borderColor: "border-red-500/20",
      };
    }

    // Telegram links
    if (domain === "t.me" || domain === "telegram.me") {
      return {
        icon: <FaTelegram className="w-4 h-4" />,
        domain: "telegram",
        displayText: getSmartDisplayText(url, text),
        color: "text-blue-500",
        bgColor: "bg-blue-500/10",
        borderColor: "border-blue-500/20",
      };
    }

    // Discord links
    if (domain === "discord.gg" || domain === "discord.com") {
      return {
        icon: <FaDiscord className="w-4 h-4" />,
        domain: "discord",
        displayText: getSmartDisplayText(url, text),
        color: "text-indigo-400",
        bgColor: "bg-indigo-500/10",
        borderColor: "border-indigo-500/20",
      };
    }

    // Default for other links
    return {
      icon: <Globe className="w-4 h-4" />,
      domain: domain,
      displayText: getSmartDisplayText(url, text),
      color: "text-muted-foreground",
      bgColor: "bg-muted/50",
      borderColor: "border-border",
    };
  } catch {
    // Fallback for invalid URLs
    return {
      icon: <ExternalLink className="w-4 h-4" />,
      domain: "link",
      displayText: text || url,
      color: "text-muted-foreground",
      bgColor: "bg-muted/50",
      borderColor: "border-border",
    };
  }
};

export const LinkEmbed: React.FC<LinkEmbedProps> = ({ url, text, className }) => {
  const linkInfo = getLinkInfo(url, text);
  const [showExternalWarning, setShowExternalWarning] = useState(false);

  const isTrustedDomain = (url: string): boolean => {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.toLowerCase();

      // List of trusted domains that don't need warnings
      const trustedDomains = [
        "x.com",
        "twitter.com",
        "liquidlaunch.app",
        "donkey.liquidlaunch.app",
        "github.com",
        "youtube.com",
        "youtu.be",
      ];

      return trustedDomains.some((trusted) => domain === trusted || domain.endsWith(`.${trusted}`));
    } catch {
      return false;
    }
  };

  const needsWarning = (url: string): boolean => {
    return !isTrustedDomain(url);
  };

  const redirectToCorrectDomain = (url: string): string => {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.toLowerCase();

      // Redirect old liquidlaunch.app domain to new donkey.liquidlaunch.app domain
      if (domain === "liquidlaunch.app" || domain.endsWith(".liquidlaunch.app")) {
        // Replace the hostname while keeping the rest of the URL intact
        parsedUrl.hostname = domain.replace(/^(.*\.)?liquidlaunch\.app$/, (match, subdomain) => {
          return subdomain ? `donkey.liquidlaunch.app` : "donkey.liquidlaunch.app";
        });
        return parsedUrl.toString();
      }

      return url;
    } catch {
      return url;
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    const finalUrl = redirectToCorrectDomain(url);

    if (needsWarning(finalUrl)) {
      setShowExternalWarning(true);
    } else {
      window.open(finalUrl, "_blank", "noopener,noreferrer");
    }
  };

  const handleExternalConfirm = () => {
    setShowExternalWarning(false);
    const finalUrl = redirectToCorrectDomain(url);
    window.open(finalUrl, "_blank", "noopener,noreferrer");
  };

  const handleExternalCancel = () => {
    setShowExternalWarning(false);
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={cn(
          "inline-flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-[1.02] hover:shadow-sm w-full max-w-full",
          linkInfo.bgColor,
          linkInfo.borderColor,
          linkInfo.color,
          "hover:bg-opacity-80",
          className,
        )}
        title={`Open ${url}`}
      >
        <div className="flex-shrink-0">{linkInfo.icon}</div>
        <div className="flex flex-col items-start min-w-0 flex-1 overflow-hidden">
          <span className="text-xs opacity-70 font-medium truncate w-full">{linkInfo.domain}</span>
          <span className="text-sm font-medium truncate w-full">{linkInfo.displayText}</span>
        </div>
        <ExternalLink className="w-3 h-3 opacity-50 flex-shrink-0" />
      </button>

      <AlertDialog open={showExternalWarning} onOpenChange={setShowExternalWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Open External Link?</AlertDialogTitle>
            <AlertDialogDescription>
              You&apos;re about to open an external link. This will redirect you to a website outside of LiquidLaunch.
              <br />
              <br />
              <span className="font-mono text-sm bg-muted px-2 py-1 rounded break-all">{url}</span>
              <br />
              <br />
              Only proceed if you trust this link and its source.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleExternalCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleExternalConfirm}>Open Link</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
