"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { ArrowUpDown } from "lucide-react";
import SwapWidget from "@/app/token/[tokenAddress]/SwapComponents/SwapWidget";
import { TokenDetails } from "@/lib/api/types";
import { type Address } from "viem";

interface MobileSwapButtonProps {
  tokenDetails?: TokenDetails | null;
  tokenAddress: Address;
}

export default function MobileSwapButton({ tokenDetails, tokenAddress }: MobileSwapButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        <Button
          size="lg"
          className="fixed bottom-4 left-[45%] transform -translate-x-1/2 z-30 bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg px-8 py-3 rounded-full font-semibold text-base flex items-center gap-2 md:hidden"
        >
          <ArrowUpDown className="w-5 h-5" />
          Trade
        </Button>
      </DrawerTrigger>
      <DrawerContent className="bg-backgroundDark border-t border-border">
        <DrawerHeader className="text-center pb-2">
          <DrawerTitle className="text-foreground text-xl font-semibold">
            Trade {tokenDetails?.symbol || "Token"}
          </DrawerTitle>
        </DrawerHeader>

        <div className="px-4 pb-4">
          <SwapWidget
            tokenDetails={tokenDetails}
            tokenAddress={tokenAddress}
            className="border-none shadow-none bg-transparent"
          />
        </div>
      </DrawerContent>
    </Drawer>
  );
}
