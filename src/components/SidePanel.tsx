"use client";

import { useState, useRef, useEffect } from "react";
import { useAccount, useConnect, useDisconnect, Connector } from "wagmi";
import { signOut } from "next-auth/react";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Portfolio } from "@/components/wallet/Portfolio";
import { Activity } from "@/components/wallet/Activity";
import { X, LogOut, ChevronDown, Copy, Check } from "lucide-react";
import { cn, shortenAddress, copyToClipboard } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useView } from "@/hooks/useView";

interface SidePanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SidePanel({ isOpen, onClose }: SidePanelProps) {
  const { address } = useAccount();
  const { connect, connectors, isPending } = useConnect();
  const { disconnect } = useDisconnect();
  const [showOtherWallets, setShowOtherWallets] = useState(false);
  const [copied, setCopied] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useView();

  // Get recently used connectors from localStorage
  const getRecentConnectors = () => {
    try {
      const recent = localStorage.getItem("recentConnectors");
      return recent ? JSON.parse(recent) : [];
    } catch {
      return [];
    }
  };

  // Save connector to recent list
  const saveToRecent = (connectorId: string) => {
    try {
      const recent = new Set(getRecentConnectors());
      recent.add(connectorId);
      localStorage.setItem("recentConnectors", JSON.stringify(Array.from(recent).slice(-3)));
    } catch {
      // Ignore errors
    }
  };

  // Copy address to clipboard
  const handleCopyAddress = async () => {
    if (address) {
      const success = await copyToClipboard(address);
      if (success) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    }
  };

  const recentConnectorIds = getRecentConnectors();

  // Filter connectors by type
  const recentConnectors = connectors.filter((c) => recentConnectorIds.includes(c.id));
  const detectedConnectors = connectors.filter((c) => c.ready && !recentConnectorIds.includes(c.id));
  const otherConnectors = connectors.filter((c) => !c.ready && !recentConnectorIds.includes(c.id));

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const isSwapWidget = target.closest(".swap-widget-container");
      const isTokenSelector = target.closest("[role='dialog']");
      const isDropdownMenu = target.closest("[data-radix-popper-content-wrapper]");
      const isMenuContent = target.closest("[role='menu']");
      const isInsideContextMenu = target.closest("[data-state='open']");

      if (
        panelRef.current &&
        !panelRef.current.contains(target) &&
        !isSwapWidget &&
        !isTokenSelector &&
        !isDropdownMenu &&
        !isMenuContent &&
        !isInsideContextMenu &&
        isOpen
      ) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const renderWalletButton = (connector: Connector, status?: string) => (
    <Button
      key={connector.id}
      onClick={() => {
        connect({ connector });
        saveToRecent(connector.id);
      }}
      disabled={!connector.ready || isPending}
      variant="ghost"
      className="w-full justify-between items-center h-14 px-4 hover:bg-accent/10"
    >
      <div className="flex items-center gap-3">
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
          {connector.name.charAt(0)}
        </div>
        <div className="flex flex-col items-start">
          <span className="font-medium">{connector.name}</span>
          {!connector.ready && <span className="text-xs text-muted-foreground">Not installed</span>}
        </div>
      </div>
      {status && <span className="text-sm text-muted-foreground">{status}</span>}
    </Button>
  );

  const renderUnconnectedState = () => (
    <div className="flex flex-col h-screen">
      <div className="flex items-center justify-between p-4 border-b border-border/20 bg-background/95 backdrop-blur-md">
        <h2 className="text-xl font-semibold text-primary">Connect a wallet</h2>
        <Button variant="ghost" size="icon" className="rounded-full hover:bg-primary/10" onClick={onClose}>
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {recentConnectors.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-primary/80 mb-2">Recent</h3>
              {recentConnectors.map((c) => renderWalletButton(c))}
            </div>
          )}

          {detectedConnectors.length > 0 && (
            <div className="space-y-2">
              {recentConnectors.length > 0 && <Separator className="my-4 bg-border/20" />}
              <h3 className="text-sm font-medium text-primary/80 mb-2">Available</h3>
              {detectedConnectors.map((c) => renderWalletButton(c))}
            </div>
          )}

          {otherConnectors.length > 0 && (
            <>
              <Separator className="my-4 bg-border/20" />
              <Button
                variant="ghost"
                onClick={() => setShowOtherWallets(!showOtherWallets)}
                className="w-full flex justify-between items-center hover:bg-accent/10"
              >
                <span className="font-medium">Other wallets</span>
                <ChevronDown
                  className={cn("h-4 w-4 transition-transform", showOtherWallets && "transform rotate-180")}
                />
              </Button>

              {showOtherWallets && (
                <div className="space-y-2 mt-2">{otherConnectors.map((c) => renderWalletButton(c))}</div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );

  const renderConnectedState = () => {
    return (
      <div className="flex flex-col h-screen">
        <div className="flex items-center justify-between p-4 border-b border-border/30">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">
              {address ? address.substring(2, 3).toUpperCase() : "?"}
            </div>
            <span className="font-medium text-primary">{shortenAddress(address, isMobile)}</span>
          </div>
          <div className="flex items-center gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:text-primary hover:bg-primary/10 rounded-full"
                    onClick={handleCopyAddress}
                  >
                    {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-card/90 backdrop-blur-md border-border/30">
                  <p>{copied ? "Copied!" : "Copy address"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 hover:text-red-500 hover:bg-red-500/10 rounded-full"
                    onClick={() => {
                      handleDisconnect();
                    }}
                  >
                    <LogOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-card/90 backdrop-blur-md border-border/30">
                  <p>Disconnect</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Close button - only visible on mobile */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 hover:bg-primary/10 rounded-full ml-1"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="portfolio" className="flex flex-col h-full">
            <div className="px-4 py-2 border-b border-border/30">
              <TabsList className="w-full bg-card/50 rounded-lg">
                <TabsTrigger
                  value="portfolio"
                  className="flex-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary rounded-lg"
                >
                  Portfolio
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  className="flex-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary rounded-lg"
                >
                  Activity
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="portfolio" className="flex-1 overflow-auto m-0 p-0">
              <Portfolio onClose={onClose} />
            </TabsContent>

            <TabsContent value="activity" className="flex-1 overflow-auto m-0 p-0">
              <Activity onClose={onClose} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  };

  // Add a handleDisconnect function to handle both disconnection and sign out
  const handleDisconnect = () => {
    disconnect();
    signOut({ redirect: false }); // Explicitly sign out the next-auth session
    onClose();
  };

  return (
    // Wrap the panel and add an overlay
    <>
      {/* Overlay: Shown only when panel is open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 backdrop-blur-sm md:hidden" // Only shown on mobile (md:hidden)
          onClick={onClose} // Close panel on overlay click
        />
      )}

      {/* Side Panel */}
      <div
        ref={panelRef}
        className={cn(
          // Responsive width: full width on small screens, fixed width on md+
          "fixed right-0 top-0 h-screen w-full bg-backgroundDark shadow-xl border-l border-border/20 z-50 transform transition-transform duration-300 ease-in-out",
          "sm:w-[400px]", // Apply fixed width only on sm screens and up
          // Removed backdrop-blur-md from panel itself, moved to overlay
          isOpen ? "translate-x-0" : "translate-x-full",
        )}
      >
        {!address ? renderUnconnectedState() : renderConnectedState()}
      </div>
    </>
  );
}
