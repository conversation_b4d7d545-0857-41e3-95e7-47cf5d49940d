"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Download, X, Share } from "lucide-react";
import { toast } from "sonner";

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: ReadonlyArray<string>;
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isIOS, setIsIOS] = useState(false);

  useEffect(() => {
    // Detect platform
    const userAgent = navigator.userAgent.toLowerCase();
    const isIOSDevice =
      /iphone|ipad|ipod/.test(userAgent) || (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);

    setIsIOS(isIOSDevice);

    // Check if app is already installed
    const isAppInstalled =
      window.matchMedia("(display-mode: standalone)").matches ||
      (window.navigator as Navigator & { standalone?: boolean }).standalone === true;
    setIsInstalled(isAppInstalled);

    // For iOS, show manual install prompt if not installed and not in standalone mode
    if (isIOSDevice && !isAppInstalled) {
      // Check if user hasn't dismissed recently
      const lastDismissed = localStorage.getItem("pwa-install-dismissed");
      if (!lastDismissed || Date.now() - parseInt(lastDismissed) > 7 * 24 * 60 * 60 * 1000) {
        setShowInstallPrompt(true);
      }
    }

    // Listen for the beforeinstallprompt event (works on Android Chrome and desktop)
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Save the event so it can be triggered later
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Update UI to notify the user they can install the PWA
      setShowInstallPrompt(true);
    };

    // Listen for the app being installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
      toast.success("LiquidLaunch installed successfully!");
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    window.addEventListener("appinstalled", handleAppInstalled);

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
      window.removeEventListener("appinstalled", handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        // Show the install prompt
        await deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        const { outcome } = await deferredPrompt.userChoice;

        if (outcome === "accepted") {
          console.log("User accepted the install prompt");
        } else {
          console.log("User dismissed the install prompt");
        }

        // Clear the saved prompt since it can only be used once
        setDeferredPrompt(null);
        setShowInstallPrompt(false);
      } catch (error) {
        console.error("Error showing install prompt:", error);
        toast.error("Unable to install the app. Please try again.");
      }
    } else if (isIOS) {
      // For iOS, just show instructions since we can't trigger the prompt
      toast.info("To install: Tap the Share button below, then 'Add to Home Screen'", {
        duration: 8000,
      });
    }
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // Store dismissal in localStorage to avoid showing again too soon
    localStorage.setItem("pwa-install-dismissed", Date.now().toString());
  };

  // Don't show if app is already installed
  if (isInstalled || !showInstallPrompt) {
    return null;
  }

  // Check if user recently dismissed (within last 7 days)
  const lastDismissed = localStorage.getItem("pwa-install-dismissed");
  if (lastDismissed) {
    const daysSinceDismissed = (Date.now() - parseInt(lastDismissed)) / (1000 * 60 * 60 * 24);
    if (daysSinceDismissed < 7) {
      return null;
    }
  }

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 py-0 border border-border/50 bg-background/95 backdrop-blur-sm md:left-auto md:right-4 md:max-w-sm">
      <CardContent className="flex items-center gap-3 p-4">
        <div className="rounded-lg bg-primary/10 p-2">
          {isIOS ? <Share className="h-5 w-5 text-primary" /> : <Download className="h-5 w-5 text-primary" />}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-sm">Install LiquidLaunch</h3>
          <p className="text-xs text-muted-foreground">
            {isIOS ? "Tap Share, then 'Add to Home Screen' (From Safari)" : "Get quick access with our mobile app"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button size="sm" onClick={handleInstallClick} className="h-8 px-3 text-xs">
            {isIOS ? "Guide" : "Install"}
          </Button>
          <Button size="sm" variant="ghost" onClick={handleDismiss} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
