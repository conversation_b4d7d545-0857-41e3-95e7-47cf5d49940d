"use client";

import { useEffect } from "react";
import { toast } from "sonner";

export function ServiceWorkerRegistration() {
  useEffect(() => {
    // Only register service worker in production
    if (process.env.NODE_ENV !== "production") {
      console.log("Service Worker: Skipping registration in development mode");
      return;
    }

    // Service Worker registration
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/sw.js", {
          // Only update when the service worker file changes
          updateViaCache: "none",
        })
        .then((registration) => {
          console.log("Service Worker registered successfully:", registration);

          // Check for updates periodically (every 30 minutes)
          setInterval(
            () => {
              registration.update();
            },
            30 * 60 * 1000,
          );

          // Check for updates on page focus
          window.addEventListener("focus", () => {
            registration.update();
          });

          // Handle updates
          registration.addEventListener("updatefound", () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener("statechange", () => {
                if (newWorker.state === "installed" && navigator.serviceWorker.controller) {
                  // New content is available; show update notification
                  toast.info("App update available!", {
                    description: "Refresh to get the latest version",
                    action: {
                      label: "Refresh",
                      onClick: () => window.location.reload(),
                    },
                    duration: 10000,
                  });
                }
              });
            }
          });
        })
        .catch((error) => {
          console.error("Service Worker registration failed:", error);
          // Don't show error toast to users - service worker is optional
        });

      // Listen for messages from the service worker
      navigator.serviceWorker.addEventListener("message", (event) => {
        if (event.data && event.data.type === "UPDATE_AVAILABLE") {
          toast.info("App update available!", {
            description: "Refresh to get the latest version",
            action: {
              label: "Refresh",
              onClick: () => window.location.reload(),
            },
            duration: 10000,
          });
        }
      });
    } else {
      console.log("Service Worker: Not supported in this browser");
    }
  }, []);

  return null; // This component doesn't render anything
}
