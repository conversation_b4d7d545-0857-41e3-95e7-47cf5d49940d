"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { SignInButton } from "@/components/wallet/SignInButton";
import { usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sheet, <PERSON>etContent, SheetTrigger, SheetClose, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { MouseOverDropdown } from "@/components/shared/MouseOverDropdown";
import { IMAGES } from "@/lib/constants-images";
import { CurrencyToggle } from "@/components/CurrencyToggle";
import { TokenSearch } from "@/components/shared/TokenSearch";

// Define a consistent type for navigation links
interface NavLinkItem {
  name: string;
  href: string;
  icon?: React.ReactNode;
  description?: string;
  isExternal?: boolean;
}

export function NavBar() {
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();

  // Direct links for the main nav
  const mainNavLinks: NavLinkItem[] = [
    {
      name: "Tokens",
      href: "/",
    },
    {
      name: "Create",
      href: "/create",
    },
    {
      name: "LiquidSwap",
      href: "https://liqd.ag",
      isExternal: true,
    },
    {
      name: "Staking",
      href: "https://liqd.ag/staking",
      isExternal: true,
    },
    // {
    //   name: "Portfolio",
    //   href: "/portfolio",
    // },
    // {
    //   name: "Leaderboard",
    //   href: "/leaderboard",
    // },
    // {
    //   name: "Points",
    //   href: "/points",
    // },
  ];

  // Links for the 'More' dropdown
  const moreLinks: NavLinkItem[] = [
    // {
    //   name: "About",
    //   href: "/about",
    //   icon: <Info className="h-5 w-5" />,
    //   description: "Learn more about LiquidLaunch",
    // },
  ];

  // Combined links for the mobile sheet menu
  const mobileNavLinks: NavLinkItem[] = [...mainNavLinks, ...moreLinks];

  // Only show the UI after client-side hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <>
      <header className="top-0 z-40 w-full border-b border-border">
        <div className="flex h-16 items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-7 md:gap-6">
            <Link href="/" className="flex items-center gap-2">
              <img src={IMAGES.LOGO.LIQUID_LAUNCH} alt="LiquidLaunch" className="w-8 h-8 rounded-full" />
              <span className="text-white font-semibold font-press text-sm">LiquidLaunch</span>
            </Link>

            <nav className="hidden md:flex items-center gap-6">
              {mainNavLinks.map((link) => {
                const isActive = link.isExternal
                  ? false
                  : link.href === "/swap"
                    ? pathname === link.href || pathname === "/"
                    : pathname === link.href;

                return (
                  <Link
                    key={link.name}
                    href={link.href}
                    target={link.isExternal ? "_blank" : undefined}
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary relative group",
                      isActive ? "text-foreground" : "text-gray-400",
                    )}
                  >
                    {link.name}
                    <span className="absolute left-0 right-0 bottom-0 h-[2px] bg-primary scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                  </Link>
                );
              })}
              <MouseOverDropdown triggerText="More" items={moreLinks} />
            </nav>
          </div>

          <div className="flex items-center sm:gap-2 gap-1">
            {mounted && (
              <>
                <TokenSearch className="hidden sm:flex" />
                <CurrencyToggle />
                <SignInButton />
              </>
            )}

            <div className="md:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" className="rounded-full active:scale-95" size="icon">
                    <Menu className="h-6 w-6" />
                    <span className="sr-only">Toggle Menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[250px] bg-backgroundDark sm:w-[300px]">
                  <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                  <div className="p-4 border-b mb-4">
                    <SheetClose asChild>
                      <Link href="/" className="flex items-center shrink-0">
                        <img src={IMAGES.LOGO.LIQUID_LAUNCH} alt="LiquidLaunch Logo" width={107} height={24} />
                      </Link>
                    </SheetClose>
                  </div>
                  <div className="px-4 mb-4">
                    <TokenSearch className="w-full" />
                  </div>
                  <nav className="flex flex-col space-y-2 px-4">
                    {mobileNavLinks.map((link: NavLinkItem) => {
                      const isActive = link.isExternal
                        ? false
                        : link.href === "/swap"
                          ? pathname === link.href || pathname === "/"
                          : pathname === link.href;
                      return (
                        <SheetClose asChild key={link.name}>
                          <Link
                            href={link.href}
                            target={link.isExternal ? "_blank" : undefined}
                            className={cn(
                              "text-base font-medium transition-colors hover:text-primary py-2",
                              isActive ? "text-foreground" : "text-gray-400",
                            )}
                          >
                            {link.name}
                          </Link>
                        </SheetClose>
                      );
                    })}
                  </nav>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
