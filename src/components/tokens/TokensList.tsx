"use client";

import React, { useMemo, useEffect } from "react";
import { motion } from "framer-motion";
import { useTokensStore } from "@/stores/tokensStore";
import { TokenDetails } from "@/lib/api/types";
import { useInView } from "react-intersection-observer";
import { usePublicClient } from "wagmi";
import { ColumnDef } from "@tanstack/react-table";
import { shortenAddress } from "@/lib/utils";
import { formatTimeAgo } from "@/components/swaps/swapsUtils";
import { useBalanceStore } from "@/stores/balanceStore";
import { CurrencyDisplay } from "@/components/shared/CurrencyDisplay";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { ScientificPrice } from "@/components/ui/scientific-price";
import { SocialLinks } from "@/components/shared/SocialLinks";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import CustomLink from "@/components/shared/Link";
import Image from "next/image";
import { IMAGES } from "@/lib/constants-images";
import { TOKENS } from "@/lib/constants";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";
import TokensListSkeleton, { SkeletonRow } from "./TokensListSkeleton";
import { useJiggleAnimation } from "@/hooks/useJiggleAnimation";

const PAGINATION_SKELETON_COUNT = 6;

// Jiggle animation variants for table rows
const jiggleVariants = {
  idle: {
    x: 0,
    y: 0,
    scale: 1,
  },
  jiggle: {
    x: [0, -1, 1, -1, 1, 0],
    y: [0, -0.5, 0.5, -0.5, 0.5, 0],
    scale: [1, 1.01, 1, 1.01, 1, 1],
    transition: {
      duration: 0.5,
      ease: "easeInOut" as const,
      times: [0, 0.2, 0.4, 0.6, 0.8, 1],
    },
  },
};

// Animated table row component
interface AnimatedTokenRowProps {
  token: TokenDetails;
  columns: ColumnDef<TokenDetails>[];
}

const AnimatedTokenRow: React.FC<AnimatedTokenRowProps> = ({ token, columns }) => {
  const controls = useJiggleAnimation(token.latestActivityTimestamp, token.symbol, token.address);

  return (
    <motion.tr
      animate={controls}
      variants={jiggleVariants}
      initial="idle"
      className="border-t border-border hover:bg-muted/20 transition-colors"
    >
      {columns.map((column, colIdx) => {
        const cellValue = column.cell
          ? typeof column.cell === "function"
            ? (column.cell as (context: { row: { original: TokenDetails } }) => React.ReactNode)({
                row: { original: token },
              })
            : column.cell
          : "";
        return (
          <td key={`${token.address}-${colIdx}`} className="px-4 py-3 whitespace-nowrap">
            {cellValue}
          </td>
        );
      })}
    </motion.tr>
  );
};

const TokensList: React.FC = () => {
  const tokenIds = useTokensStore((state) => state.tokenIds);
  const tokensMap = useTokensStore((state) => state.tokens);
  const isLoading = useTokensStore((state) => state.loading);
  const isLoadingMore = useTokensStore((state) => state.isLoadingMore);
  const currentPage = useTokensStore((state) => state.currentPage);
  const totalPages = useTokensStore((state) => state.totalPages);
  const fetchMoreTokens = useTokensStore((state) => state.fetchMoreTokens);
  const error = useTokensStore((state) => state.error);
  const hypePriceUsd = useBalanceStore((state) => state.getTokenPrice(TOKENS.NATIVE_HYPE_IDENTIFIER));
  const { formatCurrency, showUsd } = useCurrencyFormatter();

  const publicClient = usePublicClient();

  const { ref: sentinelRef, inView } = useInView({
    threshold: 0.5,
    triggerOnce: false,
  });

  useEffect(() => {
    if (inView && !isLoading && !isLoadingMore && currentPage < totalPages && publicClient) {
      fetchMoreTokens();
    }
  }, [inView, isLoading, isLoadingMore, currentPage, totalPages, fetchMoreTokens, publicClient]);

  const columns = useMemo<ColumnDef<TokenDetails>[]>(() => {
    // Helper function to calculate USD price from HYPE price
    const calculateUsdPrice = (hypePrice: string | number | null | undefined): number | null => {
      if (!hypePrice || !showUsd || !hypePriceUsd) return null;

      const hypeAmount = typeof hypePrice === "string" ? parseFloat(hypePrice) : hypePrice;
      return hypeAmount * parseFloat(hypePriceUsd);
    };

    return [
      {
        accessorKey: "token",
        header: "Token",
        cell: ({ row }) => {
          const token = row.original;
          return (
            <div className="flex items-center gap-3 w-[200px]">
              <CustomLink
                href={`/token/${token.address}`}
                className="flex items-center gap-3 hover:opacity-80 transition-opacity min-w-0 flex-1"
              >
                <div className="w-10 h-10 rounded-full overflow-hidden border border-border bg-card flex-shrink-0">
                  <Image
                    src={token.metadata?.image_uri || IMAGES.SWIRL}
                    alt={token.symbol}
                    width={40}
                    height={40}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex flex-col min-w-0 flex-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="font-semibold text-base truncate cursor-pointer">{token.symbol}</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>{token.symbol}</span>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-muted-foreground text-sm truncate cursor-pointer">{token.name}</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>{token.name}</span>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </CustomLink>
              <SocialLinks
                website={token.metadata?.website}
                twitter={token.metadata?.twitter}
                telegram={token.metadata?.telegram}
                discord={token.metadata?.discord}
                variant="header"
                className="flex-shrink-0"
              />
            </div>
          );
        },
      },
      {
        accessorKey: "marketCap",
        header: "Market Cap",
        cell: ({ row }) => {
          const token = row.original;
          // Use financials data for more accurate information, fallback to direct marketCap
          const mcUsd = token.financials?.marketCap?.usd || token.marketCap?.usd;
          const mcHype = token.financials?.marketCap?.hype || token.marketCap?.hype;
          return <CurrencyDisplay {...formatCurrency({ usd: mcUsd, hype: mcHype })} />;
        },
      },
      {
        accessorKey: "price",
        header: "Price",
        cell: ({ row }) => {
          const token = row.original;
          const priceChange = token.financials?.priceChange24h ? parseFloat(token.financials.priceChange24h) : null;
          // Use financials data for more accurate information, fallback to direct price
          const priceHype = token.financials?.price?.hype || token.price?.hype;
          const priceUsd = calculateUsdPrice(priceHype);

          // Determine which price to display based on currency preference
          const displayPrice = showUsd ? priceUsd : priceHype;

          return (
            <div className="flex flex-col">
              {showUsd && !hypePriceUsd ? (
                <Skeleton className="h-4 w-16" />
              ) : displayPrice ? (
                <ScientificPrice price={displayPrice} usd={showUsd} />
              ) : (
                <span>-</span>
              )}
              {priceChange !== null && (
                <div
                  className={`flex items-center gap-1 text-xs ${
                    priceChange > 0 ? "text-green-400" : priceChange < 0 ? "text-red-400" : "text-muted-foreground"
                  }`}
                >
                  {priceChange > 0 ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : priceChange < 0 ? (
                    <TrendingDown className="w-3 h-3" />
                  ) : (
                    <Minus className="w-3 h-3" />
                  )}
                  {priceChange > 0 ? "+" : ""}
                  {priceChange.toFixed(2)}%
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "volume24h",
        header: "Vol (24h)",
        cell: ({ row }) => {
          const token = row.original;
          const volumeUsd = token.financials?.volume24h?.usd;
          const volumeHype = token.financials?.volume24h?.hype;

          return (
            <CurrencyDisplay {...formatCurrency({ usd: volumeUsd, hype: volumeHype })} className="font-mono text-sm" />
          );
        },
      },
      {
        accessorKey: "bonding",
        header: "Bonding",
        cell: ({ row }) => {
          const token = row.original;
          const progress = token.bonding?.progress ? parseFloat(token.bonding.progress) : 0;
          const isCompleted = progress >= 100;

          return (
            <div className="flex items-center gap-2 w-[140px]">
              <Progress value={progress} className="w-20 h-2" />
              <Badge variant={isCompleted ? "default" : "secondary"} className="text-xs w-12 justify-center">
                {progress.toFixed(1)}%
              </Badge>
            </div>
          );
        },
      },
      {
        accessorKey: "holders",
        header: "Holders",
        cell: ({ row }) => {
          const token = row.original;
          return <span className="font-mono text-sm">{token.holderCount ?? "-"}</span>;
        },
      },
      {
        accessorKey: "trades24h",
        header: "Trades (24h)",
        cell: ({ row }) => {
          const token = row.original;
          return <span className="font-mono text-sm">{token.financials?.trades24h?.total ?? "-"}</span>;
        },
      },
      {
        accessorKey: "age",
        header: "Age",
        cell: ({ row }) => {
          const token = row.original;
          return (
            <span className="text-sm text-muted-foreground">{formatTimeAgo(Number(token.creationTimestamp))}</span>
          );
        },
      },
      {
        accessorKey: "creator",
        header: "Creator",
        cell: ({ row }) => {
          const token = row.original;
          return (
            <Tooltip>
              <TooltipTrigger asChild>
                <CustomLink
                  href={`${process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL}/address/${token.creator}`}
                  target="_blank"
                  className="font-mono text-xs text-muted-foreground hover:text-foreground"
                >
                  {shortenAddress(token.creator, false)}
                </CustomLink>
              </TooltipTrigger>
              <TooltipContent>Creator Address</TooltipContent>
            </Tooltip>
          );
        },
      },
    ];
  }, [formatCurrency, showUsd, hypePriceUsd]);

  // Filter out tokens not yet in map
  const displayTokensDetails = tokenIds.map((id) => tokensMap[id]).filter(Boolean) as TokenDetails[];

  if (error) {
    return <div className="text-destructive text-center py-10">Error: {error}</div>;
  }

  // Show initial skeletons if main loading is true AND no tokens are displayable yet
  if (isLoading && tokenIds.length === 0) {
    return <TokensListSkeleton rowCount={18} />;
  }

  if (displayTokensDetails.length === 0 && !isLoading) {
    return <div className="text-muted-foreground text-center py-10">No tokens found.</div>;
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border-none bg-backgroundDark shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/40">
                {columns.map((column, idx) => {
                  const isFirst = idx === 0;
                  const isLast = idx === columns.length - 1;
                  return (
                    <th
                      key={column.id || idx}
                      className={`px-4 py-3 font-semibold text-muted-foreground whitespace-nowrap text-left ${
                        isFirst ? "rounded-tl-lg" : ""
                      } ${isLast ? "rounded-tr-lg" : ""}`}
                    >
                      {typeof column.header === "string" ? column.header : ""}
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody>
              {displayTokensDetails.length > 0 ? (
                displayTokensDetails.map((token) => (
                  <AnimatedTokenRow key={token.address} token={token} columns={columns} />
                ))
              ) : (
                <tr className="border-t border-border">
                  <td colSpan={columns.length} className="h-24 text-center text-muted-foreground px-4 py-3">
                    No tokens found.
                  </td>
                </tr>
              )}
              {isLoadingMore &&
                [...Array(PAGINATION_SKELETON_COUNT)].map((_, index) => (
                  <SkeletonRow key={`skeleton-pagination-${index}`} />
                ))}
            </tbody>
          </table>
        </div>
      </div>
      {!isLoading && !isLoadingMore && currentPage < totalPages && displayTokensDetails.length > 0 && (
        <div ref={sentinelRef} className="min-h-[20px]" aria-hidden="true" />
      )}
      {isLoadingMore && <div className="text-center py-4 text-muted-foreground">Loading more tokens...</div>}
    </div>
  );
};

export default TokensList;
