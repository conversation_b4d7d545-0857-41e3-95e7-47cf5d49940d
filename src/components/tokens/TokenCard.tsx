import React, { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { FiGlobe, FiTrendingUp } from "react-icons/fi";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTelegramPlane, FaD<PERSON>rd, FaClock, FaUsers } from "react-icons/fa";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { IMAGES } from "@/lib/constants-images";
import { CurrencyDisplay } from "@/components/shared/CurrencyDisplay";
import { useCurrencyFormatter } from "@/hooks/useCurrencyFormatter";
import { safeUrl } from "@/lib/utils";
import Image from "next/image";
import { TokenDetails } from "@/lib/api/types";
import { useJiggleAnimation } from "@/hooks/useJiggleAnimation";

export interface TokenCardProps {
  token: TokenDetails;
}

function timeAgo(ts: string) {
  const n = Number(ts);
  if (!n) return "-";

  // Handle both second and millisecond timestamps
  const seconds = n > 1e12 ? Math.floor(n / 1000) : n; // crude ms detection
  const diffSeconds = Math.floor(Date.now() / 1000) - seconds;

  if (diffSeconds < 60) return `<1m ago`;
  const diffMinutes = Math.floor(diffSeconds / 60);
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24) return `${diffHours}h ago`;
  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays}d ago`;
}

type SocialKey = keyof NonNullable<TokenDetails["metadata"]>;

const socialLinks: {
  key: SocialKey;
  icon: React.ReactElement;
  getUrl: (v: string) => string;
}[] = [
  {
    key: "website" as SocialKey,
    icon: <FiGlobe />,
    getUrl: (v: string) => v,
  },
  {
    key: "discord" as SocialKey,
    icon: <FaDiscord />,
    getUrl: (v: string) => v,
  },
  {
    key: "twitter" as SocialKey,
    icon: <FaTwitter />,
    getUrl: (v: string) => (v.startsWith("http") ? v : `https://twitter.com/${v}`),
  },
  {
    key: "telegram" as SocialKey,
    icon: <FaTelegramPlane />,
    getUrl: (v: string) => (v.startsWith("http") ? v : `https://t.me/${v}`),
  },
];

// Jiggle animation variants
const jiggleVariants = {
  idle: {
    x: 0,
    y: 0,
    rotate: 0,
    scale: 1,
  },
  jiggle: {
    x: [0, -2, 2, -2, 2, 0],
    y: [0, -1, 1, -1, 1, 0],
    rotate: [0, -0.5, 0.5, -0.5, 0.5, 0],
    scale: [1, 1.02, 1, 1.02, 1, 1],
    transition: {
      duration: 0.6,
      ease: [0.4, 0, 0.6, 1], // cubic-bezier equivalent of easeInOut
      times: [0, 0.2, 0.4, 0.6, 0.8, 1],
    },
  },
};

const TokenCard: React.FC<TokenCardProps> = ({ token }) => {
  const {
    address,
    creator,
    symbol,
    name,
    metadata,
    creationTimestamp,
    marketCap,
    financials,
    bonding,
    holderCount,
    latestActivityTimestamp,
  } = token;

  const [imgSrc, setImgSrc] = useState(metadata?.image_uri || IMAGES.SWIRL);
  const controls = useJiggleAnimation(latestActivityTimestamp, symbol, address);
  const { formatCurrency } = useCurrencyFormatter();

  // Use financials data for more accurate and comprehensive information
  const priceChange24h = financials?.priceChange24h ? parseFloat(financials.priceChange24h) : null;
  const bp = bonding?.progress ? parseFloat(bonding.progress) : 0;

  return (
    <motion.div animate={controls} variants={jiggleVariants} initial="idle">
      <Card
        isLink
        tokenAddress={address}
        className="bg-backgroundDark border border-transparent py-1 rounded-xl text-foreground relative overflow-hidden h-full flex flex-col"
      >
        <CardContent className="px-4 py-2.5 flex flex-col gap-3 h-full flex-grow">
          <div className="flex items-center gap-2 w-full">
            <div className="relative w-14 h-14 rounded-full overflow-hidden">
              <Image
                src={imgSrc}
                alt={name}
                className="bg-muted flex-shrink-0"
                fill
                onError={() => {
                  setImgSrc(IMAGES.SWIRL);
                }}
              />
            </div>
            <div className="flex-1 min-w-0 flex flex-col justify-center">
              <div className="flex items-center gap-2">
                <span className="text-offwhite font-semibold text-lg truncate" title={symbol}>
                  {symbol}
                </span>
                <span className="text-primary/80 font-mono text-xs">{creator ? creator.slice(2, 8) : ""}</span>
                <div className="flex gap-1.5 ml-auto">
                  {socialLinks.map(({ key, icon, getUrl }) => {
                    const val = metadata && (metadata[key as keyof typeof metadata] as string);
                    if (!val) return null;
                    const safeHref = safeUrl(getUrl(val));
                    if (!safeHref) return null;
                    return (
                      <button
                        key={key}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          window.open(safeHref, "_blank", "noopener,noreferrer");
                        }}
                        className="text-[0.9rem] transition-colors text-muted-foreground hover:text-primary cursor-pointer"
                        title={key.charAt(0).toUpperCase() + key.slice(1)}
                        type="button"
                      >
                        {icon}
                      </button>
                    );
                  })}
                </div>
              </div>
              <div className="flex text-sm flex-col">
                <span className="text-muted-foreground leading-tight truncate" title={name}>
                  {name}
                </span>
                {metadata?.description && (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <p className="text-muted-foreground/70 text-xs truncate hover:text-primary/70 w-full cursor-help">
                          {metadata.description}
                        </p>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs break-words text-xs">
                        {metadata.description}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
          </div>
          <div className="flex justify-around items-center dark:bg-muted/40 rounded-lg px-2 py-2 mt-auto text-sm font-medium">
            <div className="flex flex-col items-center text-center">
              <span className="text-muted-foreground/80 text-[0.65rem] uppercase tracking-wider">MCap</span>
              <CurrencyDisplay
                {...formatCurrency({
                  usd: financials?.marketCap?.usd || marketCap?.usd,
                  hype: financials?.marketCap?.hype || marketCap?.hype,
                })}
              />
            </div>
            <div className="flex flex-col items-center text-center">
              <span className="text-muted-foreground/80 text-[0.65rem] uppercase tracking-wider">Vol (24h)</span>
              <CurrencyDisplay
                {...formatCurrency({
                  usd: financials?.volume24h?.usd,
                  hype: financials?.volume24h?.hype,
                })}
                className="font-semibold text-sm"
              />
            </div>
            <div className="flex flex-col items-center text-center">
              <span className="text-muted-foreground/80 text-[0.65rem] uppercase tracking-wider">Chg (24h)</span>
              <span
                className={`font-semibold text-sm ${
                  priceChange24h !== null && priceChange24h !== 0
                    ? priceChange24h > 0
                      ? "text-green-500"
                      : "text-red-500"
                    : "text-foreground"
                }`}
              >
                {priceChange24h !== null ? `${priceChange24h > 0 ? "+" : ""}${priceChange24h.toFixed(2)}%` : "-"}
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-0.5 mt-2">
            <div className="flex justify-between items-center w-full text-xs">
              <span className="text-muted-foreground">Bonding</span>
              <span className="text-primary font-medium">{bp.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-1.5">
              <div className="bg-primary h-1.5 rounded-full" style={{ width: `${bp}%` }}></div>
            </div>
          </div>
          <div className="flex justify-between items-center mt-auto pt-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <FaClock /> {creationTimestamp ? timeAgo(creationTimestamp.toString()) : "-"}
            </div>
            <div className="flex items-center gap-1 relative right-4">
              <FaUsers /> {holderCount ?? "-"}
            </div>
            <div className="flex items-center gap-1">
              <FiTrendingUp /> {financials?.trades24h?.total ?? "-"}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default TokenCard;
