"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import { FaDiscord } from "react-icons/fa";
import Link from "next/link";
import { useSettingsStore } from "@/stores/settingsStore";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export const DisclaimerPopup = () => {
  const { termsAccepted, setTermsAccepted, isTermsModalOpen, closeTermsModal } = useSettingsStore();
  const [agreeChecked, setAgreeChecked] = useState(false);

  // Handle checkbox state reset when modal opens
  useEffect(() => {
    if (isTermsModalOpen) {
      setAgreeChecked(termsAccepted);
    }
  }, [isTermsModalOpen, termsAccepted]);

  const handleAcknowledge = () => {
    // Save to settings store
    setTermsAccepted(true);
    closeTermsModal();
  };

  return (
    <Dialog open={isTermsModalOpen} onOpenChange={closeTermsModal}>
      <DialogContent className="sm:max-w-[28rem] p-6 transition-all duration-300 hover:shadow-[0_0_10px_rgba(16,185,129,0.6)] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-semibold">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
            Early Access Warning
          </DialogTitle>
          <DialogDescription className="pt-4 text-base text-muted-foreground">
            <p className="mb-4">
              <strong>LiquidLaunch is currently in Early Access.</strong> This application is actively being developed
              and may contain bugs or undergo significant changes.
            </p>
            <p className="mb-4">By proceeding to use this application, you acknowledge and accept that:</p>
            <ul className="list-disc pl-5 space-y-2 mb-4 text-left">
              <li>There may be visual bugs and interface issues</li>
              <li>Data may not always be live and may require page refreshes</li>
              <li>Features and functionality may not work as intended</li>
              <li>The application is under active development and will continue to improve</li>
            </ul>
            <p className="mt-4 flex items-center gap-1">
              Please report any bugs in our{" "}
              <Link
                href="https://discord.gg/liquidlaunch"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center underline gap-1 text-muted-foreground hover:text-primary transition-colors font-medium"
              >
                <FaDiscord className="h-4 w-4" /> Discord!
              </Link>
            </p>
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center space-x-2 my-4">
          <Checkbox
            id="terms-agree"
            checked={agreeChecked}
            onCheckedChange={(checked) => setAgreeChecked(checked === true)}
          />
          <Label htmlFor="terms-agree" className="text-sm">
            I understand the risks and agree to continue
          </Label>
        </div>
        <DialogFooter className="flex justify-center sm:justify-center pt-2">
          <Button onClick={handleAcknowledge} className="w-full sm:w-auto" variant="default" disabled={!agreeChecked}>
            I Understand, Continue to App
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
