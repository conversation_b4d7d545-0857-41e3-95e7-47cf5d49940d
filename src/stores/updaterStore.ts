import { create } from "zustand";
import { updaterService, UpdaterConfig } from "@/services/updaterService";

interface UpdaterState {
  // State
  isGloballyEnabled: boolean;
  defaultInterval: number;

  // Actions
  setGloballyEnabled: (enabled: boolean) => void;
  setDefaultInterval: (interval: number) => void;

  // Swaps polling
  startSwapsPolling: (tokenAddress: string, callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => string;
  stopSwapsPolling: (tokenAddress: string) => void;

  // Tokens polling
  startTokensPolling: (callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => string;
  stopTokensPolling: () => void;

  // Latest swaps polling
  startLatestSwapsPolling: (callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => string;
  stopLatestSwapsPolling: () => void;

  // Chart data polling
  startChartDataPolling: (
    tokenAddress: string,
    callback: () => Promise<void>,
    config?: Partial<UpdaterConfig>
  ) => string;
  stopChartDataPolling: (tokenAddress: string) => void;

  // DexScreener polling
  startDexScreenerPolling: (
    tokenAddress: string,
    callback: () => Promise<void>,
    config?: Partial<UpdaterConfig>
  ) => string;
  stopDexScreenerPolling: (tokenAddress: string) => void;

  // General controls
  stopAllPolling: () => void;
}

export const useUpdaterStore = create<UpdaterState>((set, get) => ({
  // Initial state
  isGloballyEnabled: true,
  defaultInterval: 5000, // 5 seconds

  // Actions
  setGloballyEnabled: (enabled: boolean) => {
    set({ isGloballyEnabled: enabled });
    if (!enabled) {
      updaterService.stopAllPolling();
    }
  },

  setDefaultInterval: (interval: number) => {
    set({ defaultInterval: interval });
  },

  // Swaps polling
  startSwapsPolling: (tokenAddress: string, callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => {
    const { isGloballyEnabled, defaultInterval } = get();
    const finalConfig = {
      interval: defaultInterval,
      enabled: isGloballyEnabled,
      ...config,
    };

    return updaterService.startSwapsPolling(tokenAddress, callback, finalConfig);
  },

  stopSwapsPolling: (tokenAddress: string) => {
    const pollerId = `swaps-${tokenAddress}`;
    updaterService.stopPolling(pollerId);
  },

  // Tokens polling
  startTokensPolling: (callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => {
    const { isGloballyEnabled, defaultInterval } = get();
    const finalConfig = {
      interval: defaultInterval,
      enabled: isGloballyEnabled,
      ...config,
    };

    return updaterService.startTokensPolling(callback, finalConfig);
  },

  stopTokensPolling: () => {
    updaterService.stopPolling("tokens-list");
  },

  // Latest swaps polling
  startLatestSwapsPolling: (callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => {
    const { isGloballyEnabled, defaultInterval } = get();
    const finalConfig = {
      interval: defaultInterval,
      enabled: isGloballyEnabled,
      ...config,
    };

    return updaterService.startLatestSwapsPolling(callback, finalConfig);
  },

  stopLatestSwapsPolling: () => {
    updaterService.stopPolling("latest-swaps");
  },

  // Chart data polling
  startChartDataPolling: (tokenAddress: string, callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => {
    const { isGloballyEnabled, defaultInterval } = get();
    const finalConfig = {
      interval: defaultInterval,
      enabled: isGloballyEnabled,
      ...config,
    };

    return updaterService.startChartDataPolling(tokenAddress, callback, finalConfig);
  },

  stopChartDataPolling: (tokenAddress: string) => {
    const pollerId = `chart-${tokenAddress}`;
    updaterService.stopPolling(pollerId);
  },

  // DexScreener polling
  startDexScreenerPolling: (tokenAddress: string, callback: () => Promise<void>, config?: Partial<UpdaterConfig>) => {
    const { isGloballyEnabled, defaultInterval } = get();
    const finalConfig = {
      interval: defaultInterval,
      enabled: isGloballyEnabled,
      ...config,
    };

    return updaterService.startDexScreenerPolling(tokenAddress, callback, finalConfig);
  },

  stopDexScreenerPolling: (tokenAddress: string) => {
    updaterService.stopPolling(`dexscreener-${tokenAddress}`);
  },

  // General controls
  stopAllPolling: () => {
    updaterService.stopAllPolling();
  },
}));
