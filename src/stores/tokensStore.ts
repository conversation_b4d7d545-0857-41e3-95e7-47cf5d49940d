import { create } from "zustand";
import { ListTokensParams, ListTokensResponse, TokenDetails } from "@/lib/api/types";
import { PublicClient } from "viem";
import { getTokens } from "@/app/actions/tokens.actions";
import { WebSocketSwap } from "@/types/websocket";
import { useBalanceStore } from "@/stores/balanceStore";
import { TOKENS } from "@/lib/constants";
import { calculateBondingProgress } from "@/lib/bonding-utils";

const DEFAULT_LIST_PARAMS: Omit<ListTokensParams, "page" | "limit"> = {
  sort_by: "latest_activity",
  sort_order: "desc",
  bonding_status: "in_progress", // TODO: Default to 'all' until Pars fixes (Switch OFF state)
};
const DEFAULT_LIMIT = 18;

interface TokensState {
  tokens: Record<string, TokenDetails>;
  tokenIds: string[];
  loading: boolean; // For initial load or when filters change
  error: string | null;
  fetchTokens: (client: PublicClient, params?: ListTokensParams) => Promise<void>;
  fetchTokensSilently: (client: PublicClient, params?: ListTokensParams) => Promise<void>;
  refreshVisibleTokens: (client: PublicClient) => Promise<void>;

  // Pagination and state for current listing
  currentPage: number;
  totalPages: number;
  isLoadingMore: boolean;
  currentListParams: Omit<ListTokensParams, "page" | "limit">; // Store sort/filter, not page/limit
  fetchMoreTokens: () => Promise<void>;

  // Real-time updates from WebSocket
  updateTokenFromSwap: (swapData: WebSocketSwap) => void;

  // Single token management for token pages
  loadSingleToken: (token: TokenDetails) => void;
  getSingleToken: (address: string) => TokenDetails | undefined;

  // Utility functions
  findToken: (address: string) => TokenDetails | undefined;
}

export const useTokensStore = create<TokensState>((set, get) => ({
  tokens: {},
  tokenIds: [],
  loading: false,
  error: null,
  currentPage: 0, // Start at 0, will be 1 after first successful fetch
  totalPages: 0,
  isLoadingMore: false,
  currentListParams: DEFAULT_LIST_PARAMS,

  fetchTokens: async (client: PublicClient, params?: ListTokensParams) => {
    const { sort_by, sort_order, min_mcap, min_volume_24h, min_holders, min_swaps, max_age_days, bonding_status } =
      params || {};
    const newBaseParams: Omit<ListTokensParams, "page" | "limit"> = {
      sort_by: sort_by || DEFAULT_LIST_PARAMS.sort_by,
      sort_order: sort_order || DEFAULT_LIST_PARAMS.sort_order,
      min_mcap,
      min_volume_24h,
      min_holders,
      min_swaps,
      max_age_days,
      bonding_status: bonding_status === "bonded" ? "bonded" : DEFAULT_LIST_PARAMS.bonding_status,
    };

    set({
      loading: true,
      error: null,
      currentListParams: newBaseParams,
      tokenIds: [],
      tokens: {},
    }); // Reset tokens and ids

    try {
      const apiParamsForCall: ListTokensParams = {
        ...newBaseParams,
        page: 1,
        limit: params?.limit || DEFAULT_LIMIT,
      };
      const response: ListTokensResponse = await getTokens(apiParamsForCall);

      console.log("[TokensStore] fetchTokens (page 1) - API response:", response);

      const newTokensData: Record<string, TokenDetails> = {};
      const newTokenIdsData: string[] = [];

      if (response && response.tokens) {
        console.log(`[TokensStore] fetchTokens (page 1) - Processing ${response.tokens.length} tokens.`);
        for (const apiToken of response.tokens) {
          newTokensData[apiToken.address] = apiToken as unknown as TokenDetails;
          newTokenIdsData.push(apiToken.address);
        }
      } else {
        console.log("[TokensStore] fetchTokens (page 1) - No tokens in API response.");
      }

      set({
        tokens: newTokensData,
        tokenIds: newTokenIdsData,
        loading: false,
        error: null,
        currentPage: response?.pagination?.currentPage || 1,
        totalPages: response?.pagination?.totalPages || 1,
      });
    } catch (error: unknown) {
      console.error("[TokensStore] Error in fetchTokens:", error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : String(error),
      });
      return;
    }
  },

  fetchTokensSilently: async (client: PublicClient, params?: ListTokensParams) => {
    const { sort_by, sort_order, min_mcap, min_volume_24h, min_holders, min_swaps, max_age_days, bonding_status } =
      params || {};
    const newBaseParams: Omit<ListTokensParams, "page" | "limit"> = {
      sort_by: sort_by || DEFAULT_LIST_PARAMS.sort_by,
      sort_order: sort_order || DEFAULT_LIST_PARAMS.sort_order,
      min_mcap,
      min_volume_24h,
      min_holders,
      min_swaps,
      max_age_days,
      bonding_status: bonding_status === "bonded" ? "bonded" : DEFAULT_LIST_PARAMS.bonding_status,
    };

    // Don't set loading state for silent updates
    try {
      const apiParamsForCall: ListTokensParams = {
        ...newBaseParams,
        page: 1,
        limit: params?.limit || DEFAULT_LIMIT,
      };
      const response: ListTokensResponse = await getTokens(apiParamsForCall);

      const newTokensData: Record<string, TokenDetails> = {};
      const newTokenIdsData: string[] = [];

      if (response && response.tokens) {
        console.log(`[TokensStore] fetchTokensSilently - Processing ${response.tokens.length} tokens`);
        for (const apiToken of response.tokens) {
          newTokensData[apiToken.address] = apiToken as unknown as TokenDetails;
          newTokenIdsData.push(apiToken.address);
        }
      }

      // Update tokens and pagination info without changing loading state
      set({
        tokens: newTokensData,
        tokenIds: newTokenIdsData,
        currentListParams: newBaseParams,
        currentPage: response?.pagination?.currentPage || 1,
        totalPages: response?.pagination?.totalPages || 1,
        error: null, // Clear any previous errors
      });
    } catch (error: unknown) {
      console.error("[TokensStore] Error in fetchTokensSilently:", error);
      // Don't set error state for silent updates to avoid disrupting UI
      console.warn("[TokensStore] Silent update failed, keeping current data");
    }
  },

  refreshVisibleTokens: async (client: PublicClient) => {
    const { currentPage, currentListParams, tokenIds } = get();

    if (currentPage === 0 || tokenIds.length === 0) {
      // No tokens loaded yet, fall back to regular silent fetch
      return get().fetchTokensSilently(client);
    }

    // Calculate how many tokens we need to fetch to refresh all visible ones
    const tokensToFetch = tokenIds.length;
    // TODO: This is a temporary limit when scrolled far down pagination. This should be removed once we have a better pagination system.
    const maxApiLimit = 100;

    try {
      console.log(
        `[TokensStore] refreshVisibleTokens - Refreshing ${tokensToFetch} visible tokens (${currentPage} pages)`
      );

      // If we have more tokens than the API limit, just refresh the first page
      // This is a reasonable compromise for the refresh functionality
      const limitToUse = Math.min(tokensToFetch, maxApiLimit);

      const apiParamsForCall: ListTokensParams = {
        ...currentListParams,
        page: 1,
        limit: limitToUse,
      };

      const response: ListTokensResponse = await getTokens(apiParamsForCall);

      const newTokensData: Record<string, TokenDetails> = {};
      const newTokenIdsData: string[] = [];

      if (response && response.tokens) {
        console.log(`[TokensStore] refreshVisibleTokens - Processing ${response.tokens.length} refreshed tokens`);
        for (const apiToken of response.tokens) {
          newTokensData[apiToken.address] = apiToken as unknown as TokenDetails;
          newTokenIdsData.push(apiToken.address);
        }

        // If we couldn't fetch all visible tokens due to API limit, preserve existing tokens beyond the limit
        if (tokensToFetch > maxApiLimit) {
          const existingTokensToKeep = tokenIds.slice(maxApiLimit);
          for (const tokenId of existingTokensToKeep) {
            if (get().tokens[tokenId]) {
              newTokensData[tokenId] = get().tokens[tokenId];
              newTokenIdsData.push(tokenId);
            }
          }
        }

        // Update tokens while preserving pagination state
        set({
          tokens: newTokensData,
          tokenIds: newTokenIdsData,
          currentListParams: currentListParams,
          // Keep existing pagination state since we're just refreshing
          currentPage: Math.max(currentPage, response?.pagination?.currentPage || 1),
          totalPages: response?.pagination?.totalPages || get().totalPages,
          error: null,
        });
      }
    } catch (error: unknown) {
      console.error("[TokensStore] Error in refreshVisibleTokens:", error);
      console.warn("[TokensStore] Visible tokens refresh failed, keeping current data");
    }
  },

  fetchMoreTokens: async () => {
    const { currentPage, totalPages, isLoadingMore, currentListParams } = get();

    if (isLoadingMore || currentPage >= totalPages) return;

    set({ isLoadingMore: true });

    try {
      const nextPage = currentPage + 1;
      const apiParamsForCall: ListTokensParams = {
        ...currentListParams,
        page: nextPage,
        limit: DEFAULT_LIMIT,
      };
      const response: ListTokensResponse = await getTokens(apiParamsForCall);

      const additionalTokensData: Record<string, TokenDetails> = {};
      const additionalTokenIdsData: string[] = [];

      if (response && response.tokens) {
        console.log(`[TokensStore] fetchMoreTokens (page ${nextPage}) - Processing ${response.tokens.length} tokens.`);
        const existingIds = new Set(get().tokenIds);
        for (const apiToken of response.tokens) {
          if (existingIds.has(apiToken.address)) continue; // Avoid duplicates if API somehow sends them
          additionalTokensData[apiToken.address] = apiToken as unknown as TokenDetails;
          additionalTokenIdsData.push(apiToken.address);
        }
        set((state) => ({
          tokens: { ...state.tokens, ...additionalTokensData },
          tokenIds: [...state.tokenIds, ...additionalTokenIdsData],
          isLoadingMore: false,
          currentPage: response?.pagination?.currentPage || state.currentPage,
          totalPages: response?.pagination?.totalPages || state.totalPages,
        }));
      } else {
        console.log(`[TokensStore] fetchMoreTokens (page ${nextPage}) - No new tokens.`);
        set({ isLoadingMore: false });
      }
    } catch (error: unknown) {
      console.error("[TokensStore] Error in fetchMoreTokens:", error);
      set({
        isLoadingMore: false,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  },

  // Real-time updates from WebSocket
  updateTokenFromSwap: (swapData: WebSocketSwap) => {
    const tokenAddress = swapData.token.toLowerCase();
    const currentToken = get().tokens[tokenAddress];

    if (!currentToken) {
      // Don't log for every missing token to reduce console noise
      return;
    }

    // Extract values once - WebSocket data comes as strings
    const newMarketCapHype = parseFloat(swapData.market_cap_hype.toString());
    const newLiquidityHype = parseFloat(swapData.hype_reserves.toString());
    const swapVolumeHype = parseFloat(swapData.hype_amount.toString());

    // Check if values actually changed to avoid unnecessary updates
    const currentMarketCapHype = parseFloat(currentToken.marketCap?.hype || "0");
    const currentLiquidityHype = parseFloat(currentToken.liquidity?.hype || "0");

    if (
      Math.abs(currentMarketCapHype - newMarketCapHype) < 0.000001 &&
      Math.abs(currentLiquidityHype - newLiquidityHype) < 0.000001
    ) {
      // Values haven't changed significantly, skip update
      return;
    }

    // Get HYPE price in USD for USD calculations
    const hypePriceUsd = useBalanceStore.getState().getTokenPrice(TOKENS.NATIVE_HYPE_IDENTIFIER);
    const hypePriceNum = hypePriceUsd ? parseFloat(hypePriceUsd) : null;

    // Pre-calculate string values
    const newMarketCapHypeStr = newMarketCapHype.toString();
    const newLiquidityHypeStr = newLiquidityHype.toString();

    // Calculate USD values if HYPE price is available
    const newMarketCapUsdStr = hypePriceNum
      ? (newMarketCapHype * hypePriceNum).toString()
      : currentToken.marketCap?.usd;
    const newLiquidityUsdStr = hypePriceNum
      ? (newLiquidityHype * hypePriceNum).toString()
      : currentToken.liquidity?.usd;

    set((state) => {
      // Create minimal update object
      const tokenUpdates: Partial<TokenDetails> = {
        marketCap: {
          hype: newMarketCapHypeStr,
          usd: newMarketCapUsdStr || currentToken.marketCap?.usd || "0",
        },
        liquidity: {
          hype: newLiquidityHypeStr,
          usd: newLiquidityUsdStr || currentToken.liquidity?.usd || "0",
        },
      };

      // Only update financials if they exist
      if (currentToken.financials) {
        const currentVolume24h = parseFloat(currentToken.financials.volume24h?.hype || "0");
        const newVolume24h = currentVolume24h + swapVolumeHype;
        const newVolume24hUsdStr = hypePriceNum
          ? (newVolume24h * hypePriceNum).toString()
          : currentToken.financials.volume24h?.usd;

        tokenUpdates.financials = {
          ...currentToken.financials,
          marketCap: {
            hype: newMarketCapHypeStr,
            usd: newMarketCapUsdStr || currentToken.financials.marketCap?.usd || "0",
          },
          liquidity: {
            hype: newLiquidityHypeStr,
            usd: newLiquidityUsdStr || currentToken.financials.liquidity?.usd || "0",
          },
          volume24h: {
            hype: newVolume24h.toString(),
            usd: newVolume24hUsdStr || currentToken.financials.volume24h?.usd || "0",
          },
        };
      }

      // Only update bonding if it exists and has target
      if (currentToken.bonding?.constants?.targetMcapHypeForBonding) {
        // Use the correct bonding progress calculation based on HYPE reserves
        // Convert HYPE reserves to wei (multiply by 1e18) since calculateBondingProgress expects wei
        const hypeReservesInWei = newLiquidityHype * 1e18;
        const newProgress = calculateBondingProgress(hypeReservesInWei);

        tokenUpdates.bonding = {
          ...currentToken.bonding,
          progress: newProgress.toString(),
        };
      }

      // Single state update with merged token
      return {
        ...state,
        tokens: {
          ...state.tokens,
          [tokenAddress]: {
            ...currentToken,
            ...tokenUpdates,
          },
        },
      };
    });
  },

  // Single token management for token pages
  loadSingleToken: (token: TokenDetails) => {
    const tokenAddress = token.address.toLowerCase();
    const { tokens } = get();
    const existingToken = tokens[tokenAddress];

    if (existingToken) {
      console.log(`[TokensStore] Updating existing token with fresh API data: ${token.symbol} (${tokenAddress})`);
    } else {
      console.log(`[TokensStore] Loading new token: ${token.symbol} (${tokenAddress})`);
    }

    set((state) => ({
      ...state,
      tokens: {
        ...state.tokens,
        [tokenAddress]: token,
      },
    }));
  },

  getSingleToken: (address: string) => {
    const { tokens } = get();
    return tokens[address.toLowerCase()];
  },

  // Utility functions
  findToken: (address: string) => {
    const { tokens } = get();
    const normalizedAddress = address.toLowerCase();
    return Object.values(tokens).find((token) => token.address.toLowerCase() === normalizedAddress);
  },
}));
