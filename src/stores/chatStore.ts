import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";
import { ChatMessage, ChatRoom, ChatWindow, ChatUser, TypingIndicator, ChatNotification } from "@/types/chat";
import { CHAT_CONFIG, DEFAULT_ROOMS } from "@/config/chat";
import { messageContainsTelegramBotLink } from "@/lib/chatUtils";

interface ChatState {
  // Connection state
  isConnected: boolean;
  currentUserId?: string;
  currentUser?: ChatUser;

  // Rooms and messages
  activeRooms: Record<string, ChatRoom>;
  messages: Record<string, ChatMessage[]>;
  messageHistory: Record<string, { hasMore: boolean; loading: boolean; hasLoadedInitial: boolean }>;

  // UI state
  windows: ChatWindow[];
  activeWindowId?: string;
  isGlobalChatOpen: boolean;

  // Persistent UI settings
  chatSettings: {
    isMinimized: boolean;
    dimensions: {
      width: number;
      height: number;
    };
    hasCustomSize: boolean; // Track if user has resized
  };

  // Real-time features
  typingUsers: Record<string, TypingIndicator[]>;
  notifications: ChatNotification[];
  unreadCounts: Record<string, number>;

  // Pending messages (optimistic updates)
  pendingMessages: Record<string, ChatMessage>;

  // Reply state
  replyingTo: Record<
    string,
    {
      id: string;
      username: string;
      message: string;
      tokenaddress: string;
    } | null
  >;

  // Actions
  setConnectionState: (connected: boolean) => void;
  setCurrentUser: (user: ChatUser) => void;

  // Message management
  addMessage: (message: ChatMessage) => void;
  addPendingMessage: (message: ChatMessage) => void;
  confirmMessage: (tempId: string, confirmedMessage: ChatMessage) => void;
  markMessageFailed: (tempId: string, roomId: string) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  setMessages: (roomId: string, messages: ChatMessage[]) => void;
  loadMoreMessages: (roomId: string, messages: ChatMessage[], hasMore: boolean) => void;
  markInitialLoadComplete: (roomId: string) => void;

  // Room management
  addRoom: (room: ChatRoom) => void;
  updateRoom: (roomId: string, updates: Partial<ChatRoom>) => void;
  updateRoomParticipantCount: (roomId: string, count: number) => void;

  // Window management
  openWindow: (roomId: string) => void;
  closeWindow: (windowId: string) => void;
  minimizeWindow: (windowId: string) => void;
  maximizeWindow: (windowId: string) => void;
  focusWindow: (windowId: string) => void;
  updateWindowPosition: (windowId: string, position: { x: number; y: number }) => void;
  updateWindowSize: (windowId: string, size: { width: number; height: number }) => void;

  // Global chat
  toggleGlobalChat: () => void;

  // Typing indicators
  setTyping: (roomId: string, user: ChatUser) => void;
  removeTyping: (roomId: string, userId: string) => void;
  clearTyping: (roomId: string) => void;

  // Notifications
  addNotification: (notification: ChatNotification) => void;
  markNotificationRead: (notificationId: string) => void;
  clearNotifications: (roomId?: string) => void;

  // Unread counts
  incrementUnread: (roomId: string) => void;
  clearUnread: (roomId: string) => void;

  // Chat settings management
  setChatMinimized: (isMinimized: boolean) => void;
  setChatDimensions: (dimensions: { width: number; height: number }) => void;

  // Reply management
  setReplyingTo: (roomId: string, message: ChatMessage | null) => void;
  clearReply: (roomId: string) => void;

  // Utility
  reset: () => void;
}

const initialState = {
  isConnected: false,
  currentUserId: undefined,
  currentUser: undefined,
  activeRooms: DEFAULT_ROOMS.reduce(
    (acc, room) => {
      acc[room.id] = room as ChatRoom;
      return acc;
    },
    {} as Record<string, ChatRoom>,
  ),
  messages: {},
  messageHistory: {},
  windows: [],
  activeWindowId: undefined,
  isGlobalChatOpen: false,
  chatSettings: {
    isMinimized: false,
    dimensions: CHAT_CONFIG.DEFAULT_WINDOW_SIZE,
    hasCustomSize: false,
  },
  typingUsers: {},
  notifications: [],
  unreadCounts: {},
  pendingMessages: {},
  replyingTo: {},
};

export const useChatStore = create<ChatState>()(
  persist(
    immer((set) => ({
      ...initialState,

      setConnectionState: (connected: boolean) => {
        set((state) => {
          state.isConnected = connected;
          if (!connected) {
            state.typingUsers = {};
          }
        });
      },

      setCurrentUser: (user: ChatUser) => {
        set((state) => {
          state.currentUser = user;
          state.currentUserId = user.id;
        });
      },

      // Message management
      addMessage: (message: ChatMessage) => {
        set((state) => {
          // Filter out messages with Telegram bot links (including replies)
          if (messageContainsTelegramBotLink(message)) {
            console.warn("Blocked message with Telegram bot link:", message.content);
            return; // Don't add the message
          }

          if (!state.messages[message.roomId]) {
            state.messages[message.roomId] = [];
          }

          // Check if message already exists
          const existingIndex = state.messages[message.roomId].findIndex((m: ChatMessage) => m.id === message.id);
          if (existingIndex === -1) {
            state.messages[message.roomId].push(message);

            // Keep only the latest messages
            if (state.messages[message.roomId].length > CHAT_CONFIG.MAX_MESSAGES_PER_ROOM) {
              state.messages[message.roomId] = state.messages[message.roomId].slice(-CHAT_CONFIG.MAX_MESSAGES_PER_ROOM);
            }

            // Update room's last message
            if (state.activeRooms[message.roomId]) {
              state.activeRooms[message.roomId].lastMessage = message;
            }
          }
        });
      },

      addPendingMessage: (message: ChatMessage) => {
        set((state) => {
          state.pendingMessages[message.id] = message;

          // Also add to messages for immediate UI update
          if (!state.messages[message.roomId]) {
            state.messages[message.roomId] = [];
          }

          // Check if message already exists before adding
          const existingIndex = state.messages[message.roomId].findIndex((m) => m.id === message.id);
          if (existingIndex === -1) {
            state.messages[message.roomId].push(message);
          }
        });
      },

      confirmMessage: (tempId: string, confirmedMessage: ChatMessage) => {
        set((state) => {
          // Remove from pending
          delete state.pendingMessages[tempId];

          // Replace in messages array
          const roomMessages = state.messages[confirmedMessage.roomId];
          if (roomMessages) {
            const tempIndex = roomMessages.findIndex((m) => m.id === tempId);
            if (tempIndex !== -1) {
              roomMessages[tempIndex] = confirmedMessage;
            } else {
              // If temp message not found, check if confirmed message already exists
              const existingIndex = roomMessages.findIndex((m) => m.id === confirmedMessage.id);
              if (existingIndex === -1) {
                // Only add if it doesn't already exist
                roomMessages.push(confirmedMessage);
              }
            }
          }
        });
      },

      markMessageFailed: (tempId: string, roomId: string) => {
        set((state) => {
          // Remove from pending
          delete state.pendingMessages[tempId];

          // Mark message as failed in messages array
          const roomMessages = state.messages[roomId];
          if (roomMessages) {
            const tempIndex = roomMessages.findIndex((m) => m.id === tempId);
            if (tempIndex !== -1) {
              roomMessages[tempIndex] = {
                ...roomMessages[tempIndex],
                isConfirmed: false,
                metadata: {
                  ...roomMessages[tempIndex].metadata,
                  failed: true,
                  error: "Message failed to send",
                },
              };
            }
          }
        });
      },

      updateMessage: (messageId: string, updates: Partial<ChatMessage>) => {
        set((state) => {
          Object.values(state.messages).forEach((roomMessages) => {
            const messageIndex = roomMessages.findIndex((m: ChatMessage) => m.id === messageId);
            if (messageIndex !== -1) {
              Object.assign(roomMessages[messageIndex], updates);
            }
          });
        });
      },

      setMessages: (roomId: string, messages: ChatMessage[]) => {
        set((state) => {
          // Filter out messages with Telegram bot links (including replies)
          const filteredMessages = messages.filter((message) => {
            const hasTelegramBot = messageContainsTelegramBotLink(message);
            if (hasTelegramBot) {
              console.warn("Filtered out message with Telegram bot link:", message.content);
            }
            return !hasTelegramBot;
          });

          state.messages[roomId] = filteredMessages;
          // Mark initial load as complete when setting messages
          if (!state.messageHistory[roomId]) {
            state.messageHistory[roomId] = { hasMore: true, loading: false, hasLoadedInitial: true };
          } else {
            state.messageHistory[roomId].hasLoadedInitial = true;
          }
        });
      },

      loadMoreMessages: (roomId: string, messages: ChatMessage[], hasMore: boolean) => {
        set((state) => {
          if (!state.messages[roomId]) {
            state.messages[roomId] = [];
          }

          // Filter out messages with Telegram bot links (including replies)
          const filteredMessages = messages.filter((message) => {
            const hasTelegramBot = messageContainsTelegramBotLink(message);
            if (hasTelegramBot) {
              console.warn("Filtered out message with Telegram bot link:", message.content);
            }
            return !hasTelegramBot;
          });

          // Prepend older messages
          state.messages[roomId] = [...filteredMessages, ...state.messages[roomId]];

          if (state.messageHistory[roomId]) {
            state.messageHistory[roomId].hasMore = hasMore;
            state.messageHistory[roomId].loading = false;
          } else {
            state.messageHistory[roomId] = { hasMore, loading: false, hasLoadedInitial: true };
          }
        });
      },

      // Window management
      openWindow: (roomId: string) => {
        set((state) => {
          const existingWindow = state.windows.find((w) => w.roomId === roomId);
          if (existingWindow) {
            // Focus existing window
            state.activeWindowId = existingWindow.id;
            existingWindow.isMinimized = false;
            existingWindow.zIndex = Math.max(...state.windows.map((w) => w.zIndex), 0) + 1;
            return;
          }

          // Check max windows limit
          if (state.windows.length >= CHAT_CONFIG.MAX_WINDOWS) {
            // Close the oldest window
            const oldestWindow = state.windows.reduce((oldest, current) =>
              current.zIndex < oldest.zIndex ? current : oldest,
            );
            state.windows = state.windows.filter((w) => w.id !== oldestWindow.id);
          }

          // Create new window
          const windowId = `window-${roomId}-${Date.now()}`;
          const newWindow: ChatWindow = {
            id: windowId,
            roomId,
            isMinimized: false,
            isDocked: false,
            position: {
              x: 100 + state.windows.length * 50,
              y: 100 + state.windows.length * 50,
            },
            size: CHAT_CONFIG.DEFAULT_WINDOW_SIZE,
            zIndex: Math.max(...state.windows.map((w) => w.zIndex), 0) + 1,
          };

          state.windows.push(newWindow);
          state.activeWindowId = windowId;
        });
      },

      closeWindow: (windowId: string) => {
        set((state) => {
          state.windows = state.windows.filter((w) => w.id !== windowId);
          if (state.activeWindowId === windowId) {
            state.activeWindowId = state.windows[0]?.id;
          }
        });
      },

      minimizeWindow: (windowId: string) => {
        set((state) => {
          const window = state.windows.find((w) => w.id === windowId);
          if (window) {
            window.isMinimized = true;
          }
        });
      },

      maximizeWindow: (windowId: string) => {
        set((state) => {
          const window = state.windows.find((w) => w.id === windowId);
          if (window) {
            window.isMinimized = false;
          }
        });
      },

      focusWindow: (windowId: string) => {
        set((state) => {
          const window = state.windows.find((w) => w.id === windowId);
          if (window) {
            state.activeWindowId = windowId;
            window.zIndex = Math.max(...state.windows.map((w) => w.zIndex), 0) + 1;
          }
        });
      },

      updateWindowPosition: (windowId: string, position: { x: number; y: number }) => {
        set((state) => {
          const window = state.windows.find((w) => w.id === windowId);
          if (window) {
            window.position = position;
          }
        });
      },

      updateWindowSize: (windowId: string, size: { width: number; height: number }) => {
        set((state) => {
          const window = state.windows.find((w) => w.id === windowId);
          if (window) {
            window.size = size;
          }
        });
      },

      // Global chat
      toggleGlobalChat: () => {
        set((state) => {
          state.isGlobalChatOpen = !state.isGlobalChatOpen;
          if (state.isGlobalChatOpen) {
            // Clear unread count for global room
            delete state.unreadCounts[CHAT_CONFIG.GLOBAL_ROOM_ID];
          }
        });
      },

      // Typing indicators
      setTyping: (roomId: string, user: ChatUser) => {
        set((state) => {
          if (!state.typingUsers[roomId]) {
            state.typingUsers[roomId] = [];
          }

          // Remove existing typing indicator for this user
          state.typingUsers[roomId] = state.typingUsers[roomId].filter((t) => t.userId !== user.id);

          // Add new typing indicator
          state.typingUsers[roomId].push({
            userId: user.id,
            roomId,
            timestamp: new Date(),
          });
        });
      },

      removeTyping: (roomId: string, userId: string) => {
        set((state) => {
          if (state.typingUsers[roomId]) {
            state.typingUsers[roomId] = state.typingUsers[roomId].filter((t) => t.userId !== userId);
          }
        });
      },

      clearTyping: (roomId: string) => {
        set((state) => {
          delete state.typingUsers[roomId];
        });
      },

      // Notifications
      addNotification: (notification: ChatNotification) => {
        set((state) => {
          state.notifications.push(notification);

          // Keep only recent notifications
          if (state.notifications.length > 100) {
            state.notifications = state.notifications.slice(-100);
          }
        });
      },

      markNotificationRead: (notificationId: string) => {
        set((state) => {
          const notification = state.notifications.find((n) => n.id === notificationId);
          if (notification) {
            notification.isRead = true;
          }
        });
      },

      clearNotifications: (roomId?: string) => {
        set((state) => {
          if (roomId) {
            state.notifications = state.notifications.filter((n) => n.roomId !== roomId);
          } else {
            state.notifications = [];
          }
        });
      },

      // Unread counts
      incrementUnread: (roomId: string) => {
        set((state) => {
          state.unreadCounts[roomId] = (state.unreadCounts[roomId] || 0) + 1;
        });
      },

      clearUnread: (roomId: string) => {
        set((state) => {
          delete state.unreadCounts[roomId];
        });
      },

      // Chat settings management
      setChatMinimized: (isMinimized: boolean) => {
        set((state) => {
          state.chatSettings.isMinimized = isMinimized;
        });
      },

      setChatDimensions: (dimensions: { width: number; height: number }) => {
        set((state) => {
          state.chatSettings.dimensions = dimensions;
          state.chatSettings.hasCustomSize = true;
        });
      },

      // Reply management
      setReplyingTo: (roomId: string, message: ChatMessage | null) => {
        set((state) => {
          if (message) {
            state.replyingTo[roomId] = {
              id: message.id,
              username: message.user.address,
              message: message.content,
              tokenaddress: message.roomId,
            };
          } else {
            state.replyingTo[roomId] = null;
          }
        });
      },

      clearReply: (roomId: string) => {
        set((state) => {
          state.replyingTo[roomId] = null;
        });
      },

      markInitialLoadComplete: (roomId: string) => {
        set((state) => {
          if (!state.messageHistory[roomId]) {
            state.messageHistory[roomId] = { hasMore: true, loading: false, hasLoadedInitial: true };
          } else {
            state.messageHistory[roomId].hasLoadedInitial = true;
          }
        });
      },

      // Room management
      addRoom: (room: ChatRoom) => {
        set((state) => {
          state.activeRooms[room.id] = room;
        });
      },

      updateRoom: (roomId: string, updates: Partial<ChatRoom>) => {
        set((state) => {
          if (state.activeRooms[roomId]) {
            Object.assign(state.activeRooms[roomId], updates);
          }
        });
      },

      updateRoomParticipantCount: (roomId: string, count: number) => {
        set((state) => {
          if (state.activeRooms[roomId]) {
            state.activeRooms[roomId].participantCount = count;
          } else {
            // Create room if it doesn't exist
            state.activeRooms[roomId] = {
              id: roomId,
              name: roomId === CHAT_CONFIG.GLOBAL_ROOM_ID ? "Global" : "Token",
              type: roomId === CHAT_CONFIG.GLOBAL_ROOM_ID ? "global" : "token",
              tokenAddress: roomId,
              participantCount: count,
            };
          }
        });
      },

      // Utility
      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
    })),
    {
      name: "chat-storage",
      partialize: (state) => ({
        chatSettings: state.chatSettings,
      }),
    },
  ),
);
