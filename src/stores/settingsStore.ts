import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface SettingsState {
  // Terms acceptance for beta application
  termsAccepted: boolean;
  setTermsAccepted: (accepted: boolean) => void;

  // Terms modal control
  isTermsModalOpen: boolean;
  openTermsModal: () => void;
  closeTermsModal: () => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      // Terms not accepted by default
      termsAccepted: false,
      setTermsAccepted: (accepted: boolean) => set({ termsAccepted: accepted }),

      // Terms modal state - should be open if terms not accepted
      isTermsModalOpen: false,
      openTermsModal: () => set({ isTermsModalOpen: true }),
      closeTermsModal: () => set({ isTermsModalOpen: false }),
    }),
    {
      name: "liquidlaunchv2-settings", // LocalStorage key
      partialize: (state) => ({
        termsAccepted: state.termsAccepted,
        isTermsModalOpen: state.isTermsModalOpen,
      }),
      // Add onRehydrateStorage to properly initialize modal state
      onRehydrateStorage: () => (state) => {
        if (state && !state.termsAccepted) {
          state.isTermsModalOpen = true;
        }
      },
    }
  )
);
