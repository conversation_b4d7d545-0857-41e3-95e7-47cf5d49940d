import { create } from "zustand";
import { persist } from "zustand/middleware";
import { type TokenInfo } from "@/types/token";
import { type Address, formatUnits } from "viem";
import { getBatchTokenData, BatchTokenData, getNativeHypePrice } from "@/services/dexscreenerService";
import { TOKENS, HYPER_EVM_CHAIN } from "@/lib/constants";
import { IMAGES } from "@/lib/constants-images";
import { LiquidLaunchApiService } from "@/lib/api";
import type { TokenDetails } from "@/lib/api/types";

interface TokenResponse {
  success: boolean;
  data: {
    tokens: Array<{
      address: string;
      name: string;
      symbol: string;
      decimals: number;
      isERC20Verified: boolean;
      totalTransfers: number;
      transfers24h: number;
    }>;
    count: number;
    totalAvailable: number;
    searchApplied: boolean;
    limitApplied: boolean;
    serviceStatus: string;
    lastProcessedBlock: number;
  };
}

interface BalanceApiResponse {
  success: boolean;
  data: {
    wallet: string;
    tokens: Array<{
      token: string; // Address or "Native HYPE"
      balance: string; // Raw integer balance string
      name: string;
      symbol: string;
      decimals: number;
    }>;
    count: number;
  };
}

interface SpecificBalanceApiResponse {
  success: boolean;
  data: {
    tokens: Array<{
      token: string; // Address or "Native HYPE"
      balance: string; // Raw integer balance string
      name: string;
      symbol: string;
      decimals: number;
    }>;
  };
}

export interface CustomToken {
  id: string; // address
  symbol: string;
  name: string;
  decimals: number;
  logoUrl: string; // Default logo for custom tokens
  balance: string;
  displayBalance: string; // Made required
  isCustom: boolean;
}

interface BalanceState {
  customTokens: CustomToken[];
  addCustomToken: (token: CustomToken) => void;
  removeCustomToken: (tokenId: string) => void;
  getCustomTokens: () => CustomToken[];
  hasCustomToken: (tokenId: string) => boolean;
  debug: boolean;
  setDebug: (enabled: boolean) => void;

  // Centralized token data
  isBalancesLoading: boolean; // Tracks specifically the balance fetching state
  isListLoading: boolean; // Tracks specifically the /list endpoint loading state
  ownedTokens: TokenInfo[]; // Tokens the user owns (with balances)
  defaultTokens: TokenInfo[]; // Default list of tokens from API
  tokenPrices: Record<string, { priceUsd: string }>; // Updated structure: address -> { priceUsd: string }
  initializedForAddress?: Address | null; // Track last initialized address
  initialized: boolean; // <-- Add initialized flag

  // Initialization Action
  initialize: (sessionAddress?: Address) => Promise<void>;
  fetchDefaultTokens: () => Promise<void>; // Action to fetch default tokens
  findToken: (address: string) => TokenInfo | undefined;

  // New helper methods
  getTokenPrice: (address: string) => string | undefined;
  calculateTokenUsdValue: (token: Pick<TokenInfo, "address" | "balance" | "decimals">) => number;

  // Update balances after transactions
  updateSpecificTokenBalances: (tokenAddresses: string[]) => Promise<void>;

  // Internal action for fetching prices
  fetchAndSetTokenPrices: (tokenIds: string[]) => Promise<void>;

  // Fetch all token balances AND UPDATE logos/prices
  fetchAllTokenBalances: (showLoading?: boolean) => Promise<void>;
}

// Default logo for tokens that don't have one
const DEFAULT_LOGO = "/logos/default-token.png";

// Helper function to format token display balances consistently
const formatDisplayBalance = (balance: string, decimals: number): string => {
  try {
    return parseFloat(formatUnits(BigInt(balance), decimals)).toFixed(TOKENS.DISPLAY_DECIMALS);
  } catch (error) {
    console.error("Error formatting balance:", error);
    return "0.00";
  }
};

// Re-export the type
export type { TokenInfo };

// Define initial state object
const initialState = {
  customTokens: [],
  debug: true,
  isBalancesLoading: false,
  isListLoading: false,
  ownedTokens: [],
  defaultTokens: [],
  tokenPrices: {},
  initializedForAddress: null,
  initialized: false, // <-- Set initial value for initialized
};

export const useBalanceStore = create<BalanceState>()(
  persist(
    (set, get) => {
      // --- Internal Helper Functions ---

      // Helper 1: Fetch Raw Balance Data
      const _fetchRawBalanceData = async (address: Address): Promise<BalanceApiResponse | null> => {
        const { debug } = get();
        if (debug) console.log(`[_fetchRawBalanceData] Fetching raw balances for ${address}`);
        try {
          const response = await fetch(`/api/tokens/balances?wallet=${address}`);
          if (!response.ok) throw new Error(`Balance API failed: ${response.status}`);
          const data = (await response.json()) as BalanceApiResponse;
          if (!data?.success || !data.data?.tokens) throw new Error("Balance API invalid data");
          return data;
        } catch (error) {
          console.error("[_fetchRawBalanceData] Error:", error);
          return null;
        }
      };

      // Helper 2: Process Raw Balances into Base TokenInfo
      const _processRawBalances = (balanceData: BalanceApiResponse): TokenInfo[] => {
        const { debug } = get();
        if (!balanceData || !balanceData.data || !balanceData.data.tokens) return [];
        if (debug) console.log("[_processRawBalances] Processing raw balance data:", balanceData.data.tokens.length);
        return balanceData.data.tokens
          .map((balanceToken): TokenInfo | null => {
            const isNative = balanceToken.token === "Native HYPE";
            const tokenAddress = isNative ? "Native HYPE" : balanceToken.token;
            const name = balanceToken.name;
            const symbol = balanceToken.symbol;
            const decimals = balanceToken.decimals;
            if (isNaN(decimals)) {
              if (debug)
                console.warn(`[TokenStore] Token ${symbol} (${tokenAddress}) has invalid decimals: ${decimals}`);
              return null;
            }
            const logoURI = isNative ? IMAGES.HYPE : DEFAULT_LOGO;
            return {
              address: tokenAddress,
              name,
              symbol,
              decimals,
              logoURI,
              balance: balanceToken.balance,
              displayBalance: formatDisplayBalance(balanceToken.balance, decimals),
              chainId: HYPER_EVM_CHAIN.id,
              tags: [],
              isNative: isNative,
            };
          })
          .filter((token): token is TokenInfo => token !== null);
      };

      // Helper 3: Fetch Full Token Details and DexScreener data for bonded tokens only
      const _fetchMetadata = async (
        ownedTokensBase: TokenInfo[],
      ): Promise<{
        nativeHypePrice: string | null;
        tokenDetailsMap: Record<string, TokenDetails>;
        dexscreenerData: Record<string, BatchTokenData>;
      }> => {
        const { debug } = get();
        if (debug) console.log("[_fetchMetadata] Fetching full token details and DexScreener for bonded tokens only");

        const nativeHypeTokens = ownedTokensBase.filter((t) => t.isNative);
        const nonNativeTokens = ownedTokensBase.filter((t) => !t.isNative);
        const hasWhype = nonNativeTokens.some((t) => t.address.toLowerCase() === TOKENS.WHYPE_ADDRESS.toLowerCase());
        const shouldFetchHypePrice = nativeHypeTokens.length > 0 || hasWhype;

        let nativeHypePriceUsd: string | null = null;
        const tokenDetailsMap: Record<string, TokenDetails> = {};
        let dexscreenerData: Record<string, BatchTokenData> = {};

        // Initialize API service
        const apiService = new LiquidLaunchApiService();

        // Fetch HYPE price if needed
        if (shouldFetchHypePrice) {
          try {
            nativeHypePriceUsd = await getNativeHypePrice();
            if (debug) console.log("[_fetchMetadata] HYPE price fetched:", nativeHypePriceUsd);
          } catch (e) {
            console.error("[_fetchMetadata] Failed HYPE price fetch:", e);
          }
        }

        // Fetch full token details for all non-native tokens
        if (nonNativeTokens.length > 0) {
          if (debug) console.log("[_fetchMetadata] Fetching full token details for", nonNativeTokens.length, "tokens");

          const tokenDetailsPromises = nonNativeTokens.map(async (token) => {
            try {
              const tokenDetails = await apiService.token.getTokenDetails(token.address);
              return { address: token.address.toLowerCase(), details: tokenDetails };
            } catch (error) {
              console.error(`[_fetchMetadata] Failed to fetch details for token ${token.address}:`, error);
              return null;
            }
          });

          const tokenDetailsResults = await Promise.all(tokenDetailsPromises);

          // Build token details map
          tokenDetailsResults.forEach((result) => {
            if (result) {
              tokenDetailsMap[result.address] = result.details;
            }
          });

          if (debug)
            console.log("[_fetchMetadata] Token details fetched for", Object.keys(tokenDetailsMap).length, "tokens");

          // Find bonded tokens and fetch DexScreener data for them
          const bondedTokenAddresses = Object.entries(tokenDetailsMap)
            .filter(([, details]) => details.isBonded)
            .map(([address]) => address);

          if (bondedTokenAddresses.length > 0) {
            if (debug)
              console.log(
                "[_fetchMetadata] Fetching DexScreener data for",
                bondedTokenAddresses.length,
                "bonded tokens",
              );
            try {
              dexscreenerData = await getBatchTokenData(bondedTokenAddresses);
              if (debug) console.log("[_fetchMetadata] DexScreener data fetched for bonded tokens");
            } catch (error) {
              console.error("[_fetchMetadata] Failed to fetch DexScreener data for bonded tokens:", error);
            }
          } else {
            if (debug) console.log("[_fetchMetadata] No bonded tokens found, skipping DexScreener");
          }
        }

        if (debug)
          console.log("[_fetchMetadata] Metadata fetch complete:", {
            nativeHypePriceUsd,
            tokenDetailsCount: Object.keys(tokenDetailsMap).length,
            dexscreenerDataCount: Object.keys(dexscreenerData).length,
          });

        return {
          nativeHypePrice: nativeHypePriceUsd,
          tokenDetailsMap,
          dexscreenerData,
        };
      };

      // Helper 4: Combine base tokens with full token details and DexScreener data
      const _combineAndFinalizeTokens = (
        ownedTokensBase: TokenInfo[],
        nativeHypePrice: string | null,
        tokenDetailsMap: Record<string, TokenDetails>,
        dexscreenerData: Record<string, BatchTokenData>,
      ): { finalTokens: TokenInfo[]; finalPrices: Record<string, { priceUsd: string }> } => {
        const { debug } = get();
        if (debug) console.log("[_combineAndFinalizeTokens] Combining data with full token details...");
        const fetchedPrices: Record<string, { priceUsd: string }> = {};

        // Set HYPE prices first if available
        if (nativeHypePrice) {
          fetchedPrices[TOKENS.NATIVE_HYPE_IDENTIFIER] = { priceUsd: nativeHypePrice };
          fetchedPrices[TOKENS.WHYPE_ADDRESS.toLowerCase()] = { priceUsd: nativeHypePrice };
        }

        // Process tokens and merge with full token details
        const finalOwnedTokens = ownedTokensBase.map((token) => {
          if (token.isNative) {
            return token; // Native tokens are already complete
          }

          const lowerAddress = token.address.toLowerCase();
          const fullTokenDetails = tokenDetailsMap[lowerAddress];
          const dexscreenerInfo = dexscreenerData[lowerAddress];

          if (!fullTokenDetails) {
            // If we couldn't fetch full details, keep the basic token info
            if (debug) console.warn(`[_combineAndFinalizeTokens] No full details for token ${token.address}`);
            return token;
          }

          // Start with the basic token info and enhance it with full details
          const enhancedToken = { ...token };

          // Update logo from full token details or DexScreener (prioritize DexScreener for bonded tokens)
          if (fullTokenDetails.isBonded && dexscreenerInfo?.logoURI) {
            enhancedToken.logoURI = dexscreenerInfo.logoURI;
          } else if (fullTokenDetails.metadata?.image_uri) {
            enhancedToken.logoURI = fullTokenDetails.metadata.image_uri;
          }

          // Set price based on bonding status
          if (fullTokenDetails.isBonded && dexscreenerInfo?.priceUsd) {
            // Use DexScreener price for bonded tokens
            fetchedPrices[lowerAddress] = { priceUsd: dexscreenerInfo.priceUsd };
          } else if (fullTokenDetails.price?.usd) {
            // Use LiquidLaunch API price for non-bonded tokens
            fetchedPrices[lowerAddress] = { priceUsd: fullTokenDetails.price.usd };
          }

          // Add additional metadata from full token details
          enhancedToken.name = fullTokenDetails.name || token.name;
          enhancedToken.symbol = fullTokenDetails.symbol || token.symbol;

          return enhancedToken;
        });

        // Sort by value (now we have prices for both bonded and non-bonded tokens)
        const calculateValue = (token: TokenInfo): number => {
          const priceInfo = fetchedPrices[token.address.toLowerCase()];
          if (priceInfo?.priceUsd && token.balance && typeof token.decimals === "number") {
            try {
              const decimalBalance = formatUnits(BigInt(token.balance), token.decimals);
              const priceNum = parseFloat(priceInfo.priceUsd);
              const value = parseFloat(decimalBalance) * priceNum;
              return isNaN(value) ? 0 : value;
            } catch {
              return 0;
            }
          }
          return 0;
        };
        finalOwnedTokens.sort((a, b) => calculateValue(b) - calculateValue(a));

        if (debug) console.log("[_combineAndFinalizeTokens] Tokens sorted with full details and prices.");

        return { finalTokens: finalOwnedTokens, finalPrices: fetchedPrices };
      };

      // --- Public Actions ---

      return {
        ...initialState,

        setDebug: (enabled: boolean) => set({ debug: enabled }),

        addCustomToken: (token: CustomToken) => {
          const { customTokens, debug } = get();
          // Check if token already exists
          if (!customTokens.some((t) => t.id.toLowerCase() === token.id.toLowerCase())) {
            if (debug) console.log(`Adding custom token: ${token.symbol} (${token.id})`);
            set({ customTokens: [...customTokens, token] });
            // Fetch price for the newly added token (no await needed)
            get().fetchAndSetTokenPrices([token.id]);
            if (debug) console.log(`Triggered price fetch for new custom token: ${token.symbol}`);
          }
        },

        removeCustomToken: (tokenId: string) => {
          const { customTokens, debug, tokenPrices } = get();
          const lowerCaseTokenId = tokenId.toLowerCase();
          if (debug) console.log(`Removing custom token with ID: ${tokenId}`);

          // Prepare updated prices by removing the custom token's price
          const updatedPrices = { ...tokenPrices };
          if (updatedPrices[lowerCaseTokenId]) {
            delete updatedPrices[lowerCaseTokenId];
            if (debug) console.log(`Removed price entry for custom token: ${tokenId}`);
          }

          set({
            customTokens: customTokens.filter((t) => t.id.toLowerCase() !== lowerCaseTokenId),
            tokenPrices: updatedPrices, // Set the updated prices
          });
        },

        getCustomTokens: () => get().customTokens,

        hasCustomToken: (tokenId: string) =>
          get().customTokens.some((t) => t.id.toLowerCase() === tokenId.toLowerCase()),

        findToken: (address: string) => {
          const { ownedTokens, defaultTokens } = get();
          const lowerAddress = address.toLowerCase();

          // Special case for native HYPE
          if (lowerAddress === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase()) {
            return (
              ownedTokens.find((t) => t.isNative) ||
              ownedTokens.find((t) => t.address.toLowerCase() === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase())
            );
          }

          return (
            ownedTokens.find((t) => t.address.toLowerCase() === lowerAddress) ||
            defaultTokens.find((t) => t.address.toLowerCase() === lowerAddress)
          );
        },

        fetchDefaultTokens: async () => {
          const { debug } = get();
          if (debug) console.log("[TokenStore] Fetching default tokens...");

          set({ isListLoading: true }); // Set list loading state to true

          try {
            const response = await fetch("/api/tokens/list?limit=20");
            if (!response.ok) {
              throw new Error(`Default tokens API failed with status: ${response.status}`);
            }
            const data: TokenResponse = await response.json();

            if (!data.success || !data.data.tokens) {
              throw new Error("Default tokens API returned success: false or missing data");
            }

            const fetchedDefaultTokens: TokenInfo[] = data.data.tokens.map((apiToken) => ({
              address: apiToken.address,
              name: apiToken.name,
              symbol: apiToken.symbol,
              decimals: apiToken.decimals,
              // Assign native logo if it's the native token, otherwise default
              logoURI:
                apiToken.address.toLowerCase() === TOKENS.WHYPE_ADDRESS.toLowerCase() ? IMAGES.HYPE : DEFAULT_LOGO,
              balance: "0",
              displayBalance: "0.00",
              chainId: HYPER_EVM_CHAIN.id,
              tags: [],
            }));

            if (debug) console.log("[TokenStore] Fetched base default tokens:", fetchedDefaultTokens.length);

            const defaultTokenPrices: Record<string, { priceUsd: string }> = {};

            // --- Fetch WHYPE Price First (since it applies to both native and wrapped versions) ---
            // Always fetch the WHYPE price since we need it for both native and wrapped HYPE
            let nativeHypePriceUsd: string | null = null;

            try {
              if (debug) console.log("[TokenStore] Fetching HYPE/WHYPE price...");
              nativeHypePriceUsd = await getNativeHypePrice();
              if (debug) console.log("[TokenStore] HYPE/WHYPE price received:", nativeHypePriceUsd);

              // If we have a HYPE price, add it for both the native identifier and WHYPE address
              if (nativeHypePriceUsd) {
                defaultTokenPrices[TOKENS.NATIVE_HYPE_IDENTIFIER] = { priceUsd: nativeHypePriceUsd };
                defaultTokenPrices[TOKENS.WHYPE_ADDRESS.toLowerCase()] = { priceUsd: nativeHypePriceUsd };
              }
            } catch (error) {
              console.error("[TokenStore] Failed to fetch HYPE/WHYPE price:", error);
            }

            // Update state: set default tokens and merge the fetched prices
            set((state) => ({
              defaultTokens: fetchedDefaultTokens,
              // Merge new default prices with potentially existing prices
              // Make sure we prioritize keeping HYPE/WHYPE prices in sync
              tokenPrices: { ...state.tokenPrices, ...defaultTokenPrices },
              isListLoading: false, // Set list loading state to false on success
            }));
          } catch (error) {
            console.error("[TokenStore] Error fetching default tokens:", error);
            // On error, clear default tokens but maybe preserve existing prices?
            set({
              defaultTokens: [],
              isListLoading: false, // Set list loading state to false on error
            });
          }
        },

        updateSpecificTokenBalances: async (tokenAddresses: string[]) => {
          const { debug, initializedForAddress, ownedTokens } = get();

          if (!initializedForAddress) {
            if (debug) console.log("[TokenStore] Cannot update token balances: No wallet initialized");
            return;
          }

          if (tokenAddresses.length === 0) {
            if (debug) console.log("[TokenStore] No token addresses provided for balance update");
            return;
          }

          if (debug) console.log(`[TokenStore] Updating balances for tokens:`, tokenAddresses);

          // Store current balances for retry logic
          const preUpdateBalances = new Map<string, string | undefined>();
          tokenAddresses.forEach((tokenAddr) => {
            const currentToken = ownedTokens.find((t) => {
              const normalizedAddr = tokenAddr.toLowerCase();
              return (
                t.address.toLowerCase() === normalizedAddr ||
                (normalizedAddr === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase() && t.isNative)
              );
            });
            preUpdateBalances.set(tokenAddr.toLowerCase(), currentToken?.balance);
          });

          const updateBalancesWithRetry = async (retryCount = 0): Promise<void> => {
            const maxRetries = 3;
            const retryDelay = 5000; // 5 seconds

            try {
              // Fetch balances for specific tokens with cache busting
              const cacheBreaker = Date.now();
              const response = await fetch(
                `/api/tokens/specific-balances?wallet=${initializedForAddress}&tokens=${tokenAddresses.join(",")}&_t=${cacheBreaker}`,
              );

              if (!response.ok) {
                throw new Error(`Specific balance API failed with status: ${response.status}`);
              }

              const balanceData: SpecificBalanceApiResponse = await response.json();

              if (!balanceData.success || !balanceData.data?.tokens) {
                throw new Error("Specific balance API returned success: false or invalid/missing data");
              }

              // Update the token balances in the store
              const { ownedTokens: currentOwnedTokens } = get();
              const updatedOwnedTokens = [...currentOwnedTokens]; // Create a shallow copy for immutability
              let hasChanges = false;
              let allBalancesUnchanged = true;

              balanceData.data.tokens.forEach((balanceToken) => {
                // Validate that the token has the required fields
                if (
                  !balanceToken.token ||
                  !balanceToken.name ||
                  !balanceToken.symbol ||
                  typeof balanceToken.decimals !== "number"
                ) {
                  if (debug)
                    console.warn(`[TokenStore] Skipping invalid token in specific balance response:`, balanceToken);
                  return;
                }

                const isNative = balanceToken.token === TOKENS.NATIVE_HYPE_IDENTIFIER;
                const tokenAddress = isNative ? "Native HYPE" : balanceToken.token;
                const normalizedAddr = balanceToken.token.toLowerCase();

                // Check if this balance actually changed from pre-update
                const preUpdateBalance = preUpdateBalances.get(normalizedAddr);
                if (preUpdateBalance !== balanceToken.balance) {
                  allBalancesUnchanged = false;
                }

                // Find the token in the current owned tokens
                const tokenIndex = updatedOwnedTokens.findIndex(
                  (t) => t.address.toLowerCase() === tokenAddress.toLowerCase(),
                );

                if (tokenIndex !== -1) {
                  // Only update if the balance actually changed
                  if (updatedOwnedTokens[tokenIndex].balance !== balanceToken.balance) {
                    updatedOwnedTokens[tokenIndex] = {
                      ...updatedOwnedTokens[tokenIndex],
                      balance: balanceToken.balance,
                      displayBalance: formatDisplayBalance(balanceToken.balance as string, balanceToken.decimals),
                    };
                    hasChanges = true;
                  }
                } else {
                  // Token doesn't exist in owned tokens, add it
                  if (debug) console.log(`[TokenStore] Token ${tokenAddress} not found in owned tokens, adding it`);

                  // Create a new TokenInfo object for this token
                  const newToken = {
                    address: tokenAddress,
                    name: balanceToken.name,
                    symbol: balanceToken.symbol,
                    decimals: balanceToken.decimals,
                    logoURI: isNative ? IMAGES.HYPE : DEFAULT_LOGO, // Assign appropriate logo
                    balance: balanceToken.balance,
                    displayBalance: formatDisplayBalance(balanceToken.balance as string, balanceToken.decimals),
                    chainId: HYPER_EVM_CHAIN.id,
                    tags: [],
                    isNative: isNative,
                  };

                  updatedOwnedTokens.push(newToken);
                  hasChanges = true;
                  allBalancesUnchanged = false; // New token is definitely a change
                }
              });

              // Check if we should retry (balances haven't changed from pre-update state)
              if (allBalancesUnchanged && retryCount < maxRetries) {
                if (debug)
                  console.log(
                    `[TokenStore] Balances unchanged after update, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`,
                  );
                setTimeout(() => {
                  updateBalancesWithRetry(retryCount + 1);
                }, retryDelay);
                return;
              }

              // Update the store if there are changes
              if (hasChanges) {
                // Create a completely new array to ensure reference changes
                set({ ownedTokens: [...updatedOwnedTokens] });
                if (debug) console.log("[TokenStore] Specific balance update complete with changes");
              } else {
                if (debug) console.log("[TokenStore] No balance changes detected");
              }

              if (retryCount >= maxRetries && allBalancesUnchanged) {
                console.warn("[TokenStore] Max retries reached, balances may not have updated yet");
              } else if (!allBalancesUnchanged) {
                if (debug) console.log("[TokenStore] Balances successfully updated after transaction");
              }
            } catch (error) {
              console.error("[TokenStore] Error updating specific token balances:", error);
              if (retryCount < maxRetries) {
                if (debug)
                  console.log(
                    `[TokenStore] Retrying after error in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`,
                  );
                setTimeout(() => {
                  updateBalancesWithRetry(retryCount + 1);
                }, retryDelay);
              }
            }
          };

          await updateBalancesWithRetry();
        },

        fetchAndSetTokenPrices: async (tokenIds: string[]) => {
          const { debug } = get();
          if (!tokenIds || tokenIds.length === 0) {
            if (debug) console.log("[TokenStore] fetchAndSetTokenPrices called with empty list, skipping.");
            return;
          }

          const uniqueIds = Array.from(new Set(tokenIds.map((id) => id.toLowerCase())));
          if (debug)
            console.log("[TokenStore] fetchAndSetTokenPrices: Fetching full details for", uniqueIds.length, "tokens");

          try {
            const apiService = new LiquidLaunchApiService();
            const newPrices: Record<string, { priceUsd: string }> = {};
            let customTokensNeedUpdate = false;
            const updatedCustomTokens = [...get().customTokens];

            // Fetch full token details for all tokens
            const tokenDetailsPromises = uniqueIds.map(async (tokenId) => {
              try {
                const tokenDetails = await apiService.token.getTokenDetails(tokenId);
                return { address: tokenId, details: tokenDetails };
              } catch (error) {
                console.error(`[TokenStore] Failed to fetch details for token ${tokenId}:`, error);
                return null;
              }
            });

            const tokenDetailsResults = await Promise.all(tokenDetailsPromises);
            const tokenDetailsMap: Record<string, TokenDetails> = {};

            tokenDetailsResults.forEach((result) => {
              if (result) {
                tokenDetailsMap[result.address] = result.details;
              }
            });

            // Find bonded tokens and fetch DexScreener data for them
            const bondedTokenAddresses = Object.entries(tokenDetailsMap)
              .filter(([, details]) => details.isBonded)
              .map(([address]) => address);

            let dexscreenerData: Record<string, BatchTokenData> = {};
            if (bondedTokenAddresses.length > 0) {
              if (debug)
                console.log("[TokenStore] Fetching DexScreener data for", bondedTokenAddresses.length, "bonded tokens");
              try {
                dexscreenerData = await getBatchTokenData(bondedTokenAddresses);
              } catch (error) {
                console.error("[TokenStore] Failed to fetch DexScreener data:", error);
              }
            }

            // Process prices and logos
            for (const tokenId of uniqueIds) {
              const fullTokenDetails = tokenDetailsMap[tokenId];
              const dexscreenerInfo = dexscreenerData[tokenId];

              if (!fullTokenDetails) continue;

              // Set price based on bonding status
              if (fullTokenDetails.isBonded && dexscreenerInfo?.priceUsd) {
                // Use DexScreener price for bonded tokens
                newPrices[tokenId] = { priceUsd: dexscreenerInfo.priceUsd };
              } else if (fullTokenDetails.price?.usd) {
                // Use LiquidLaunch API price for non-bonded tokens
                newPrices[tokenId] = { priceUsd: fullTokenDetails.price.usd };
              }

              // Process Logo for Custom Tokens
              const logoUrl =
                fullTokenDetails.isBonded && dexscreenerInfo?.logoURI
                  ? dexscreenerInfo.logoURI
                  : fullTokenDetails.metadata?.image_uri;

              if (logoUrl) {
                const customTokenIndex = updatedCustomTokens.findIndex((ct) => ct.id.toLowerCase() === tokenId);
                if (customTokenIndex !== -1) {
                  const currentToken = updatedCustomTokens[customTokenIndex];
                  if (currentToken.logoUrl !== logoUrl) {
                    if (debug)
                      console.log(`[TokenStore] Updating logo for custom token ${currentToken.symbol} (${tokenId})`);
                    updatedCustomTokens[customTokenIndex] = {
                      ...currentToken,
                      logoUrl: logoUrl,
                    };
                    customTokensNeedUpdate = true;
                  }
                }
              }
            }

            // Update store
            if (Object.keys(newPrices).length > 0 && !customTokensNeedUpdate) {
              set((state) => ({
                tokenPrices: { ...state.tokenPrices, ...newPrices },
              }));
              if (debug) console.log("[TokenStore] Updated prices for:", Object.keys(newPrices).join(", "));
            } else if (customTokensNeedUpdate) {
              set((state) => ({
                tokenPrices: { ...state.tokenPrices, ...newPrices },
                customTokens: updatedCustomTokens,
              }));
              if (debug) console.log("[TokenStore] Updated prices and custom token logos");
            } else {
              if (debug) console.log("[TokenStore] No prices or logo updates found.");
            }
          } catch (error) {
            console.error("[TokenStore] Error in fetchAndSetTokenPrices:", error);
          }
        },

        fetchAllTokenBalances: async (showLoading = true) => {
          const { debug, initializedForAddress } = get();

          if (!initializedForAddress) {
            if (debug) console.log("[fetchAllTokenBalances] Cannot fetch balances: No wallet initialized");
            return;
          }

          if (showLoading) {
            set({ isBalancesLoading: true });
          }
          if (debug) console.log(`[fetchAllTokenBalances] Starting for address: ${initializedForAddress}`);

          try {
            // Step 1: Fetch Raw Balances
            const balanceData = await _fetchRawBalanceData(initializedForAddress);
            if (!balanceData) throw new Error("Failed to fetch raw balance data.");

            // Step 2: Process Raw Balances
            const ownedTokensBase = _processRawBalances(balanceData);
            if (ownedTokensBase.length === 0 && balanceData.data.tokens.length > 0) {
              console.warn("[fetchAllTokenBalances] Processed base tokens resulted in empty array despite raw data.");
            }
            if (debug) console.log("[fetchAllTokenBalances] Base tokens processed:", ownedTokensBase.length);

            // Step 3: Fetch Metadata
            const { nativeHypePrice, tokenDetailsMap, dexscreenerData } = await _fetchMetadata(ownedTokensBase);
            if (debug) console.log("[fetchAllTokenBalances] Metadata fetched.");

            // Step 4: Combine and Finalize
            const { finalTokens, finalPrices } = _combineAndFinalizeTokens(
              ownedTokensBase,
              nativeHypePrice,
              tokenDetailsMap,
              dexscreenerData,
            );
            if (debug)
              console.log("[fetchAllTokenBalances] Tokens combined and finalized.", {
                finalTokensCount: finalTokens.length,
                finalPricesCount: Object.keys(finalPrices).length,
              });

            // Step 5: Update State
            set((state) => ({
              ownedTokens: finalTokens,
              tokenPrices: { ...state.tokenPrices, ...finalPrices },
              isBalancesLoading: false,
            }));
            if (debug) console.log("[fetchAllTokenBalances] State updated successfully.");
          } catch (error) {
            console.error("[fetchAllTokenBalances] Error during execution:", error);
            set({ isBalancesLoading: false });
          }
        },

        initialize: async (sessionAddress?: Address) => {
          const {
            debug,
            initializedForAddress,
            fetchDefaultTokens,
            fetchAllTokenBalances /* removed fetchAndSetTokenPrices */,
          } = get();
          if (debug)
            console.log(
              `[TokenStore] Initialize called. Current Address: ${initializedForAddress}, New Address: ${sessionAddress}`,
            );

          if (initializedForAddress !== sessionAddress) {
            set({ ownedTokens: [], tokenPrices: {}, initializedForAddress: sessionAddress, initialized: false });
            if (debug) console.log(`[TokenStore] Resetting state for address change.`);
          }

          try {
            if (get().defaultTokens.length === 0) {
              await fetchDefaultTokens();
            }

            if (sessionAddress) {
              await fetchAllTokenBalances(true); // Pass true to show loading state during initialization
              // --- REMOVED separate fetchAndSetTokenPrices call ---
            }

            set({ initialized: true });
            if (debug) console.log(`[TokenStore] Initialization complete for address: ${sessionAddress}`);
          } catch (error) {
            console.error("[TokenStore] Initialization failed:", error);
            set({ initialized: false });
          }
        },

        getTokenPrice: (address: string): string | undefined => {
          const { tokenPrices } = get();
          const lowerCaseAddress = address?.toLowerCase();
          if (!lowerCaseAddress) return undefined;

          // Direct lookup first
          if (tokenPrices[lowerCaseAddress]?.priceUsd) {
            return tokenPrices[lowerCaseAddress].priceUsd;
          }

          // Handle HYPE/WHYPE fallbacks
          if (lowerCaseAddress === TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase()) {
            // If native HYPE price not found, try WHYPE price
            return tokenPrices[TOKENS.WHYPE_ADDRESS.toLowerCase()]?.priceUsd;
          }

          if (lowerCaseAddress === TOKENS.WHYPE_ADDRESS.toLowerCase()) {
            // If WHYPE price not found, try native HYPE price
            return tokenPrices[TOKENS.NATIVE_HYPE_IDENTIFIER.toLowerCase()]?.priceUsd;
          }

          return undefined;
        },

        calculateTokenUsdValue: (token: Pick<TokenInfo, "address" | "balance" | "decimals">): number => {
          if (!token?.address || !token.balance || typeof token.decimals !== "number") return 0;

          // Use the getTokenPrice helper which now handles HYPE/WHYPE fallbacks
          const priceUsd = get().getTokenPrice(token.address);

          if (priceUsd) {
            try {
              const decimalBalance = formatUnits(BigInt(token.balance), token.decimals);
              const priceNum = parseFloat(priceUsd);
              const value = parseFloat(decimalBalance) * priceNum;
              // Return 0 if calculation results in NaN (e.g., invalid inputs)
              return isNaN(value) ? 0 : value;
            } catch (e) {
              // Log error internally if needed, but return 0 for the UI
              console.error(`[TokenStore] Error calculating USD value for ${token.address}`, e);
              return 0;
            }
          }
          return 0;
        },
      }; // End of returned object
    },
    {
      name: "token-storage",
      partialize: (state) => ({ customTokens: state.customTokens }),
    },
  ),
);
