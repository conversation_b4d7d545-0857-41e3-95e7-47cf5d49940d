// Interface for the structure within the 'pairs' array from DexScreener API
import type { BondedTokenDetails } from "@/lib/api/types";

interface DexScreenerPair {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  labels?: string[]; // Added optional labels
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string; // Often WETH or WBNB
  };
  priceNative: string; // Price in terms of native currency (e.g., ETH, BNB)
  priceUsd?: string; // Price in USD
  txns: {
    m5: { buys: number; sells: number };
    h1: { buys: number; sells: number };
    h6: { buys: number; sells: number };
    h24: { buys: number; sells: number };
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd?: number; // Liquidity in USD
    base: number;
    quote: number;
  };
  fdv?: number; // Fully diluted valuation
  pairCreatedAt?: number;
  info?: {
    imageUrl?: string;
    websites?: { label: string; url: string }[];
    socials?: { type: string; url: string }[];
  };
  marketCap?: number; // Added optional marketCap
}

// Interface for the data returned by our batch function
export interface BatchTokenData {
  priceUsd?: string;
  logoURI?: string;
}

// --- Added: Types for HyperSwap Token List ---
interface HyperSwapTokenInfo {
  address: string;
  logoURI?: string;
  // Other fields ignored for now
}

interface HyperSwapTokenList {
  tokens: HyperSwapTokenInfo[];
  // Other fields ignored for now
}

// --- Constants ---
const MAX_ADDRESSES_PER_CALL = 30;
const DEXSCREENER_BATCH_URL = "https://api.dexscreener.com/tokens/v1/hyperevm/";
const NATIVE_HYPE_PRICE_URL = "https://api.dexscreener.com/tokens/v1/hyperliquid/0x0d01dc56dcaaca66ad901c959b4011ec";
const HYPERSWAP_LIST_URL =
  "https://raw.githubusercontent.com/HyperSwapX/hyperswap-token-list/refs/heads/main/tokens.json";

// --- HyperSwap Token List Cache Module ---

// eslint-disable-next-line @typescript-eslint/no-namespace
namespace TokenListCache {
  let cache: Record<string, string> | null = null; // Map<lowercase_address, logoURI>
  let isFetching = false;

  export const initialize = async (): Promise<void> => {
    if (cache !== null || isFetching) {
      return; // Already initialized/fetching or failed
    }
    isFetching = true;
    try {
      const response = await fetch(HYPERSWAP_LIST_URL);
      if (!response.ok) {
        throw new Error(`Failed to fetch HyperSwap token list: ${response.status}`);
      }
      const data: HyperSwapTokenList = await response.json();
      if (!data || !data.tokens) {
        throw new Error("Invalid HyperSwap token list format");
      }

      const logoMap: Record<string, string> = {};
      for (const token of data.tokens) {
        if (token.address && token.logoURI) {
          logoMap[token.address.toLowerCase()] = token.logoURI;
        }
      }
      cache = logoMap;
    } catch (error) {
      console.error("[TokenListCache] Initialization failed:", error);
      cache = {}; // Set to empty object on error to prevent retries
    } finally {
      isFetching = false;
    }
  };

  export const getLogo = (address: string): string | undefined => {
    const lowerAddress = address?.toLowerCase();
    if (!lowerAddress || !cache) {
      // Ensure cache is initialized before calling getLogo
      console.warn("[TokenListCache] Cache not initialized when getLogo called.");
      return undefined;
    }
    return cache[lowerAddress];
  };
}

// --- Internal DexScreener API Fetchers ---

/**
 * Fetches batch token data from DexScreener.
 */
const fetchDexScreenerBatch = async (addresses: string[]): Promise<DexScreenerPair[]> => {
  if (!addresses || addresses.length === 0) return [];

  const addressesParam = addresses.join(",");
  const url = `${DEXSCREENER_BATCH_URL}${addressesParam}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      console.error(`[DexScreener] Batch API request failed: ${response.status} ${response.statusText}`);
      return []; // Return empty on failure
    }
    const data: DexScreenerPair[] = await response.json();
    return data || []; // Ensure array return
  } catch (error) {
    console.error(`[DexScreener] Error fetching batch data:`, error);
    return [];
  }
};

/**
 * Fetches the specific native HYPE price data from DexScreener.
 */
const fetchNativeHypePriceData = async (): Promise<DexScreenerPair | null> => {
  try {
    const response = await fetch(NATIVE_HYPE_PRICE_URL);
    if (!response.ok) {
      console.error(`[DexScreener] Native HYPE price request failed: ${response.status} ${response.statusText}`);
      return null;
    }
    // Updated based on corrected understanding: API returns array directly
    const data: DexScreenerPair[] = await response.json();
    if (data && data.length > 0) {
      return data[0]; // Return the first pair object
    }
    console.warn(`[DexScreener] No pair data found in Native HYPE response array.`);
    return null;
  } catch (error) {
    console.error(`[DexScreener] Error fetching Native HYPE price data:`, error);
    return null;
  }
};

// --- Public Service Functions ---

/**
 * Fetches the USD price specifically for Native HYPE.
 * @returns The USD price string, or null if fetch fails or price is missing.
 */
export const getNativeHypePrice = async (): Promise<string | null> => {
  const pairData = await fetchNativeHypePriceData();
  if (pairData?.priceUsd) {
    return pairData.priceUsd;
  } else {
    console.warn(`[DexScreener] Native HYPE priceUsd not found in pair data.`);
    return null;
  }
};

/**
 * Fetches price and logo URI for multiple tokens.
 * Uses DexScreener batch API and falls back to HyperSwap token list for logos.
 * @param tokenAddresses Array of token addresses.
 * @returns A map of token addresses (lowercase) to their price and logo data.
 */
export const getBatchTokenData = async (tokenAddresses: string[]): Promise<Record<string, BatchTokenData>> => {
  // Ensure HyperSwap list is fetched/initialized first
  await TokenListCache.initialize();

  if (!tokenAddresses || tokenAddresses.length === 0) {
    return {};
  }

  const results: Record<string, BatchTokenData> = {};
  const lowerCaseAddresses = tokenAddresses.map((addr) => addr.toLowerCase());

  // Process addresses in batches
  for (let i = 0; i < lowerCaseAddresses.length; i += MAX_ADDRESSES_PER_CALL) {
    const batchAddresses = lowerCaseAddresses.slice(i, i + MAX_ADDRESSES_PER_CALL);

    // Fetch data for the current batch
    const batchData = await fetchDexScreenerBatch(batchAddresses);

    // Process the fetched batch data
    if (batchData.length > 0) {
      // Group pairs by base token address for efficient lookup
      const pairsByToken: Record<string, DexScreenerPair[]> = {};
      for (const pair of batchData) {
        const baseTokenAddress = pair.baseToken.address.toLowerCase();
        if (batchAddresses.includes(baseTokenAddress)) {
          if (!pairsByToken[baseTokenAddress]) {
            pairsByToken[baseTokenAddress] = [];
          }
          pairsByToken[baseTokenAddress].push(pair);
        }
      }

      // Find the best pair and logo for each token in the batch
      for (const tokenAddress of batchAddresses) {
        const pairs = pairsByToken[tokenAddress];
        if (!pairs || pairs.length === 0) {
          // If no pairs found via DexScreener, still try fallback logo
          results[tokenAddress] = {
            logoURI: TokenListCache.getLogo(tokenAddress),
          };
          continue;
        }

        // Sort pairs by USD liquidity descending
        const bestPair = pairs.sort((a, b) => (b.liquidity?.usd ?? 0) - (a.liquidity?.usd ?? 0))[0];

        // Get logo: DexScreener first, then fallback cache
        const finalLogoURI = bestPair.info?.imageUrl || TokenListCache.getLogo(tokenAddress);

        results[tokenAddress] = {
          priceUsd: bestPair.priceUsd,
          logoURI: finalLogoURI,
        };
      }
    } else {
      // If batch fetch failed or returned empty, try fallback logos for all addresses in this batch
      for (const tokenAddress of batchAddresses) {
        results[tokenAddress] = { logoURI: TokenListCache.getLogo(tokenAddress) };
      }
    }

    // Optional delay between batches
    if (i + MAX_ADDRESSES_PER_CALL < lowerCaseAddresses.length) {
      await new Promise((resolve) => setTimeout(resolve, 200));
    }
  }

  return results;
};

/**
 * Transform DexScreener pairs to BondedTokenDetails format.
 * Selects the best pair for each token based on liquidity.
 */
const transformPairsToBondedTokenDetails = (pairs: DexScreenerPair[]): BondedTokenDetails[] => {
  if (!pairs || pairs.length === 0) {
    console.log("[DexScreener] No pairs to transform");
    return [];
  }

  console.log(`[DexScreener] Transforming ${pairs.length} pairs`);

  // Group pairs by base token address
  const pairsByToken: Record<string, DexScreenerPair[]> = {};
  for (const pair of pairs) {
    const tokenAddress = pair.baseToken.address.toLowerCase();
    if (!pairsByToken[tokenAddress]) {
      pairsByToken[tokenAddress] = [];
    }
    pairsByToken[tokenAddress].push(pair);
  }

  // Transform each token's best pair to BondedTokenDetails
  const tokenDetails: BondedTokenDetails[] = [];
  for (const [tokenAddress, tokenPairs] of Object.entries(pairsByToken)) {
    // Select the pair with highest USD liquidity
    const bestPair = tokenPairs.sort((a, b) => (b.liquidity?.usd ?? 0) - (a.liquidity?.usd ?? 0))[0];

    if (bestPair) {
      const details: BondedTokenDetails = {
        address: tokenAddress,
        symbol: bestPair.baseToken.symbol,
        name: bestPair.baseToken.name,
        price: {
          usd: parseFloat(bestPair.priceUsd || "0"),
          native: parseFloat(bestPair.priceNative || "0"),
        },
        marketCap: {
          usd: bestPair.marketCap || 0,
          fdv: bestPair.fdv,
        },
        liquidity: {
          usd: bestPair.liquidity?.usd || 0,
          base: bestPair.liquidity?.base || 0,
          quote: bestPair.liquidity?.quote || 0,
        },
        volume: {
          h24: bestPair.volume?.h24 || 0,
          h6: bestPair.volume?.h6 || 0,
          h1: bestPair.volume?.h1 || 0,
          m5: bestPair.volume?.m5 || 0,
        },
        trades: {
          h24: (bestPair.txns?.h24?.buys || 0) + (bestPair.txns?.h24?.sells || 0),
          h6: (bestPair.txns?.h6?.buys || 0) + (bestPair.txns?.h6?.sells || 0),
          h1: (bestPair.txns?.h1?.buys || 0) + (bestPair.txns?.h1?.sells || 0),
          m5: (bestPair.txns?.m5?.buys || 0) + (bestPair.txns?.m5?.sells || 0),
        },
        priceChange: {
          h24: bestPair.priceChange?.h24 || 0,
          h6: bestPair.priceChange?.h6 || 0,
          h1: bestPair.priceChange?.h1 || 0,
          m5: bestPair.priceChange?.m5 || 0,
        },
        metadata: {
          imageUrl: bestPair.info?.imageUrl,
          websites: bestPair.info?.websites,
          socials: bestPair.info?.socials,
        },
        pairAddress: bestPair.pairAddress,
        dexId: bestPair.dexId,
        url: bestPair.url,
        pairCreatedAt: bestPair.pairCreatedAt,
        labels: bestPair.labels,
      };

      tokenDetails.push(details);
    }
  }

  console.log(`[DexScreener] Successfully transformed ${tokenDetails.length} tokens`);
  return tokenDetails;
};

/**
 * Get comprehensive token details for a single bonded token.
 * @param tokenAddress Token contract address.
 * @returns A promise that resolves with the token details or null if not found.
 * @throws Error If the API request fails or token address is invalid.
 */
export const getBondedTokenDetails = async (tokenAddress: string): Promise<BondedTokenDetails | null> => {
  if (!tokenAddress) {
    throw new Error("Token address is required to fetch bonded token details.");
  }

  try {
    const pairs = await fetchDexScreenerBatch([tokenAddress]);
    const tokenDetails = transformPairsToBondedTokenDetails(pairs);
    return tokenDetails.length > 0 ? tokenDetails[0] : null;
  } catch (error) {
    console.error("[DexScreener] Error fetching bonded token details:", error);
    throw error instanceof Error ? error : new Error("Failed to fetch bonded token details");
  }
};
