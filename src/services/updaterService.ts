export interface UpdaterConfig {
  interval: number; // in milliseconds
  enabled: boolean;
}

interface ActivePoller {
  id: string;
  type: "swaps" | "tokens" | "chart" | "latest-swaps" | "dexscreener" | "wallet-swaps" | "holders";
  tokenAddress?: string;
  walletAddress?: string;
  intervalId: NodeJS.Timeout;
  callback: () => Promise<void>;
}

export class UpdaterService {
  private static instance: UpdaterService;
  private activePollers: Map<string, ActivePoller> = new Map();
  private defaultConfig: UpdaterConfig = {
    interval: 5000, // 5 seconds
    enabled: true,
  };

  private constructor() {}

  public static getInstance(): UpdaterService {
    if (!UpdaterService.instance) {
      UpdaterService.instance = new UpdaterService();
    }
    return UpdaterService.instance;
  }

  /**
   * Start polling for token swaps
   */
  public startSwapsPolling(
    tokenAddress: string,
    callback: () => Promise<void>,
    config: Partial<UpdaterConfig> = {},
  ): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = `swaps-${tokenAddress}`;

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error(`[UpdaterService] Error in swaps polling for ${tokenAddress}:`, error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "swaps",
      tokenAddress,
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started swaps polling for ${tokenAddress} every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Start polling for token holders
   */
  public startHoldersPolling(
    tokenAddress: string,
    callback: () => Promise<void>,
    config: Partial<UpdaterConfig> = {},
  ): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = `holders-${tokenAddress}`;

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error(`[UpdaterService] Error in holders polling for ${tokenAddress}:`, error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "holders",
      tokenAddress,
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started holders polling for ${tokenAddress} every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Start polling for wallet swaps for a specific token
   */
  public startWalletSwapsPolling(
    walletAddress: string,
    tokenAddress: string,
    callback: () => Promise<void>,
    config: Partial<UpdaterConfig> = {},
  ): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = `wallet-swaps-${walletAddress}-${tokenAddress}`;

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error(`[UpdaterService] Error in wallet swaps polling for ${walletAddress}/${tokenAddress}:`, error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "wallet-swaps",
      walletAddress,
      tokenAddress,
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(
      `[UpdaterService] Started wallet swaps polling for ${walletAddress}/${tokenAddress} every ${finalConfig.interval}ms`,
    );

    return pollerId;
  }

  /**
   * Start polling for new tokens (home page)
   */
  public startTokensPolling(callback: () => Promise<void>, config: Partial<UpdaterConfig> = {}): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = "tokens-list";

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error("[UpdaterService] Error in tokens polling:", error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "tokens",
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started tokens polling every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Start polling for latest swaps across all tokens
   */
  public startLatestSwapsPolling(callback: () => Promise<void>, config: Partial<UpdaterConfig> = {}): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = "latest-swaps";

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error("[UpdaterService] Error in latest swaps polling:", error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "latest-swaps",
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started latest swaps polling every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Start polling for chart data
   */
  public startChartDataPolling(
    tokenAddress: string,
    callback: () => Promise<void>,
    config: Partial<UpdaterConfig> = {},
  ): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = `chart-${tokenAddress}`;

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error(`[UpdaterService] Error in chart polling for ${tokenAddress}:`, error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "chart",
      tokenAddress,
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started chart polling for ${tokenAddress} every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Start polling for DexScreener data (bonded tokens)
   */
  public startDexScreenerPolling(
    tokenAddress: string,
    callback: () => Promise<void>,
    config: Partial<UpdaterConfig> = {},
  ): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    const pollerId = `dexscreener-${tokenAddress}`;

    this.stopPolling(pollerId);

    if (!finalConfig.enabled) {
      return pollerId;
    }

    const intervalId = setInterval(async () => {
      try {
        await callback();
      } catch (error) {
        console.error(`[UpdaterService] Error in DexScreener polling for ${tokenAddress}:`, error);
      }
    }, finalConfig.interval);

    const poller: ActivePoller = {
      id: pollerId,
      type: "dexscreener",
      tokenAddress,
      intervalId,
      callback,
    };

    this.activePollers.set(pollerId, poller);
    console.log(`[UpdaterService] Started DexScreener polling for ${tokenAddress} every ${finalConfig.interval}ms`);

    return pollerId;
  }

  /**
   * Stop a specific poller
   */
  public stopPolling(pollerId: string): void {
    const poller = this.activePollers.get(pollerId);
    if (poller) {
      clearInterval(poller.intervalId);
      this.activePollers.delete(pollerId);
      console.log(`[UpdaterService] Stopped polling: ${pollerId}`);
    }
  }

  /**
   * Stop all active pollers
   */
  public stopAllPolling(): void {
    this.activePollers.forEach((poller) => {
      clearInterval(poller.intervalId);
    });
    this.activePollers.clear();
    console.log("[UpdaterService] Stopped all polling");
  }
}

// Export singleton instance
export const updaterService = UpdaterService.getInstance();
