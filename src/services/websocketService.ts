import ReconnectingWebSocket from "reconnecting-websocket";
import {
  WebSocketConfig,
  WebSocketStatus,
  Subscription,
  OHLCVData,
  WebSocketSwap,
  transformWebSocketSwapToApiSwap,
} from "@/types/websocket";
import { Swap } from "@/lib/api/types";
import { useTokensStore } from "@/stores/tokensStore";

// WebSocket configuration
const DEFAULT_CONFIG: WebSocketConfig = {
  url: "wss://ws.liquidlaunch.app/ws",
  reconnectAttempts: 5,
  reconnectDelay: 1000,
  timeout: 10000,
  autoConnect: true,
};

interface WebSocketMessage {
  type: string;
  channel: string;
  data?: unknown;
}

interface OHLCVChannelSubscription {
  channel: string;
  tokenAddress: string;
  type: "ohlcv";
  callbacks: Set<(data: OHLCVData) => void>;
}

interface SwapChannelSubscription {
  channel: string;
  tokenAddress: string;
  type: "swaps";
  callbacks: Set<(data: Swap) => void>;
}

type ChannelSubscription = OHLCVChannelSubscription | SwapChannelSubscription;

export class WebSocketService {
  private static instance: WebSocketService;
  private webSocket: ReconnectingWebSocket | null = null;
  private config: WebSocketConfig;
  private subscriptions: Map<string, Subscription> = new Map();
  private channelSubscriptions: Map<string, ChannelSubscription> = new Map();
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED;
  private statusCallbacks: Set<(status: WebSocketStatus, error?: string) => void> = new Set();
  private pendingSubscriptions: Array<{ channel: string; tokenAddress: string; type: "ohlcv" | "swaps" }> = [];
  private isReconnecting = false; // Track if we're in the middle of reconnecting
  private connectionPromise: Promise<void> | null = null; // Prevent multiple simultaneous connections

  private constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  public static getInstance(config?: Partial<WebSocketConfig>): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService(config);
    }
    return WebSocketService.instance;
  }

  /**
   * Reset the singleton instance (for development hot reloads)
   */
  public static resetInstance(): void {
    if (WebSocketService.instance) {
      WebSocketService.instance.disconnect();
      WebSocketService.instance = null as unknown as WebSocketService;
    }
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): Promise<void> {
    // If already connected, resolve immediately
    if (this.webSocket?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    // If connection is already in progress, return the existing promise
    if (this.connectionPromise) {
      console.log(`[WebSocketService] 🔄 Connection already in progress, returning existing promise`);
      return this.connectionPromise;
    }

    // Create new connection promise
    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        console.log(`[WebSocketService] 🔌 Connecting to: ${this.config.url}`);

        // Create ReconnectingWebSocket with optimized options
        this.webSocket = new ReconnectingWebSocket(this.config.url, [], {
          maxReconnectionDelay: 10000,
          minReconnectionDelay: 1000,
          reconnectionDelayGrowFactor: 1.3,
          connectionTimeout: this.config.timeout,
          maxRetries: this.config.reconnectAttempts,
          debug: process.env.NODE_ENV === "development",
        });

        // Set up event handlers
        this.webSocket.addEventListener("open", () => {
          console.log("[WebSocketService] ✅ Connected to WebSocket server");
          this.setStatus(WebSocketStatus.CONNECTED);

          // Set reconnecting flag to prevent duplicate subscriptions
          this.isReconnecting = true;

          // Store pending channels before processing them
          const pendingChannels = new Set(this.pendingSubscriptions.map((p) => p.channel));

          // Process pending subscriptions
          this.processPendingSubscriptions();

          // Re-establish existing subscriptions (excluding ones already handled)
          this.reestablishSubscriptions(pendingChannels);

          // Clear reconnecting flag
          this.isReconnecting = false;

          // Clear connection promise since we're now connected
          this.connectionPromise = null;

          resolve();
        });

        this.webSocket.addEventListener("close", (event) => {
          console.warn("[WebSocketService] 🔌 Disconnected:", {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
          });
          this.setStatus(WebSocketStatus.DISCONNECTED);
          // Clear connection promise on disconnect
          this.connectionPromise = null;
        });

        this.webSocket.addEventListener("error", (event) => {
          console.error("[WebSocketService] ❌ Connection error:", event);
          this.setStatus(WebSocketStatus.ERROR, "Connection error");
          // Clear connection promise on error
          this.connectionPromise = null;
          reject(new Error("WebSocket connection failed"));
        });

        this.webSocket.addEventListener("message", (event) => {
          this.handleMessage(event);
        });

        // Set initial status
        this.setStatus(WebSocketStatus.CONNECTING);
      } catch (error) {
        console.error("[WebSocketService] Failed to create WebSocket:", error);
        this.setStatus(WebSocketStatus.ERROR, error instanceof Error ? error.message : "Unknown error");
        // Clear connection promise on error
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data);

      // Handle channel messages
      if (message.channel) {
        console.log(`[WebSocketService] 📨 Message received for channel: ${message.channel}`, message);

        const channelSub = this.channelSubscriptions.get(message.channel);
        if (channelSub) {
          // Transform data based on type
          let transformedData = message.data || message;

          if (channelSub.type === "ohlcv") {
            // Transform OHLCV data to expected format
            const rawData = message.data || message;
            transformedData = {
              time: message.timestamp || rawData.timestamp || rawData.time,
              open: parseFloat(message.open || rawData.open || 0),
              high: parseFloat(message.high || rawData.high || 0),
              low: parseFloat(message.low || rawData.low || 0),
              close: parseFloat(message.close || rawData.close || 0),
              volume: parseFloat(message.volume || rawData.volume || 0),
            };

            // Validate the transformed data
            if (
              !transformedData.time ||
              isNaN(transformedData.open) ||
              isNaN(transformedData.high) ||
              isNaN(transformedData.low) ||
              isNaN(transformedData.close) ||
              isNaN(transformedData.volume)
            ) {
              console.error(`[WebSocketService] Invalid OHLCV data received:`, {
                original: message,
                transformed: transformedData,
              });
              return; // Skip invalid data
            }

            console.log(`[WebSocketService] 📊 OHLCV data for ${channelSub.tokenAddress}:`, transformedData);
          } else if (channelSub.type === "swaps") {
            // Transform WebSocket swap data to API Swap format
            try {
              const wsSwapData: WebSocketSwap = message.data || message;
              transformedData = transformWebSocketSwapToApiSwap(wsSwapData);

              // Update token data in the tokens store with the raw WebSocket data
              try {
                const tokensStore = useTokensStore.getState();
                tokensStore.updateTokenFromSwap(wsSwapData);
                console.log(`[WebSocketService] 📈 Updated token data in store for ${channelSub.tokenAddress}`);
              } catch (storeError) {
                console.error(`[WebSocketService] Error updating tokens store:`, storeError);
              }

              console.log(`[WebSocketService] 🔥 SWAP data transformed for ${channelSub.tokenAddress}:`, {
                original: wsSwapData,
                transformed: transformedData,
              });
            } catch (error) {
              console.error(`[WebSocketService] Error transforming swap data:`, error, message);
              return; // Skip this message if transformation fails
            }
          }

          // Call all callbacks for this channel
          channelSub.callbacks.forEach((callback) => {
            try {
              callback(transformedData);
            } catch (error) {
              console.error(`[WebSocketService] Error in ${channelSub.type} callback:`, error);
            }
          });
        } else {
          console.warn(`[WebSocketService] No subscription found for channel: ${message.channel}`);
        }
      } else {
        console.log("[WebSocketService] 📨 General message:", message);
      }
    } catch (error) {
      console.error("[WebSocketService] Error parsing message:", error, event.data);
    }
  }

  /**
   * Subscribe to OHLCV updates for a token
   */
  public subscribeToOHLCV(tokenAddress: string, callback: (data: OHLCVData) => void): string {
    const channel = `token_ohlcv_${tokenAddress.toLowerCase()}`;

    // If we're in the middle of reconnecting, don't create new subscriptions
    // The existing ones will be re-established automatically
    if (this.isReconnecting) {
      console.log(`[WebSocketService] ⏸️  Skipping subscription during reconnection for token: ${tokenAddress}`);
      // Return a temporary ID - this subscription will be handled by reestablishSubscriptions
      return `temp-ohlcv-${tokenAddress}-${Date.now()}`;
    }

    // Check if we already have a subscription for this token with the same callback
    const existingOHLCVSubs = Array.from(this.subscriptions.values()).filter(
      (s) => s.type === "ohlcv" && s.address === tokenAddress.toLowerCase(),
    );

    // Check if the exact same callback is already subscribed
    const existingChannelSub = this.channelSubscriptions.get(channel) as OHLCVChannelSubscription;
    if (existingChannelSub && existingChannelSub.callbacks.has(callback)) {
      console.log(`[WebSocketService] ⚠️  Callback already subscribed to OHLCV for token: ${tokenAddress}`);
      // Return the existing subscription ID for this callback
      const existingSub = existingOHLCVSubs.find((s) => s.callback === callback);
      if (existingSub) {
        console.log(`[WebSocketService] Returning existing subscription ID: ${existingSub.id}`);
        return existingSub.id;
      }
    }

    // In development mode, be more aggressive about preventing duplicates
    // This helps with React Strict Mode double-invocation
    if (process.env.NODE_ENV === "development" && existingOHLCVSubs.length > 0) {
      console.log(
        `[WebSocketService] 🚫 Development mode: Preventing duplicate OHLCV subscription for token: ${tokenAddress}`,
      );
      console.log(
        `[WebSocketService] Existing subscription IDs:`,
        existingOHLCVSubs.map((s) => s.id),
      );

      // Add the callback to the existing channel subscription
      if (existingChannelSub) {
        existingChannelSub.callbacks.add(callback);
        console.log(
          `[WebSocketService] 🔗 Added callback to existing OHLCV channel: ${channel} (${existingChannelSub.callbacks.size} total callbacks)`,
        );
      }

      // Return the first existing subscription ID
      return existingOHLCVSubs[0].id;
    }

    const subscriptionId = `ohlcv-${tokenAddress}-${Date.now()}`;

    const subscription: Subscription = {
      id: subscriptionId,
      type: "ohlcv",
      address: tokenAddress.toLowerCase(),
      callback,
      createdAt: new Date(),
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Add to channel subscriptions
    if (!this.channelSubscriptions.has(channel)) {
      this.channelSubscriptions.set(channel, {
        channel,
        tokenAddress: tokenAddress.toLowerCase(),
        type: "ohlcv",
        callbacks: new Set(),
      } as OHLCVChannelSubscription);
    }
    const channelSub = this.channelSubscriptions.get(channel) as OHLCVChannelSubscription;
    channelSub.callbacks.add(callback);

    // Subscribe to channel (only if this is the first subscription for this channel)
    if (channelSub.callbacks.size === 1) {
      this.subscribeToChannel(channel, tokenAddress, "ohlcv");
    } else {
      console.log(`[WebSocketService] Channel ${channel} already subscribed, adding callback only`);
    }

    console.log(`[WebSocketService] 📊 Subscribed to OHLCV for token: ${tokenAddress} (ID: ${subscriptionId})`);
    console.log(
      `[WebSocketService] Total OHLCV subscriptions: ${Array.from(this.subscriptions.values()).filter((s) => s.type === "ohlcv").length}`,
    );

    return subscriptionId;
  }

  /**
   * Subscribe to swap events for a token
   */
  public subscribeToSwaps(tokenAddress: string, callback: (swap: Swap) => void): string {
    const channel = `token_swaps_${tokenAddress.toLowerCase()}`;

    // If we're in the middle of reconnecting, don't create new subscriptions
    // The existing ones will be re-established automatically
    if (this.isReconnecting) {
      console.log(`[WebSocketService] ⏸️  Skipping subscription during reconnection for token: ${tokenAddress}`);
      // Return a temporary ID - this subscription will be handled by reestablishSubscriptions
      return `temp-swaps-${tokenAddress}-${Date.now()}`;
    }

    // Check if we already have a subscription for this token with the same callback
    const existingSwapSubs = Array.from(this.subscriptions.values()).filter(
      (s) => s.type === "swaps" && s.address === tokenAddress.toLowerCase(),
    );

    // Check if the exact same callback is already subscribed
    const existingChannelSub = this.channelSubscriptions.get(channel) as SwapChannelSubscription;
    if (existingChannelSub && existingChannelSub.callbacks.has(callback)) {
      console.log(`[WebSocketService] ⚠️  Callback already subscribed to swaps for token: ${tokenAddress}`);
      // Return the existing subscription ID for this callback
      const existingSub = existingSwapSubs.find((s) => s.callback === callback);
      if (existingSub) {
        console.log(`[WebSocketService] Returning existing subscription ID: ${existingSub.id}`);
        return existingSub.id;
      }
    }

    // In development mode, be more aggressive about preventing duplicates
    // This helps with React Strict Mode double-invocation
    if (process.env.NODE_ENV === "development" && existingSwapSubs.length > 0) {
      console.log(
        `[WebSocketService] 🚫 Development mode: Preventing duplicate swap subscription for token: ${tokenAddress}`,
      );
      console.log(
        `[WebSocketService] Existing subscription IDs:`,
        existingSwapSubs.map((s) => s.id),
      );

      // Add the callback to the existing channel subscription
      if (existingChannelSub) {
        existingChannelSub.callbacks.add(callback);
        console.log(
          `[WebSocketService] 🔗 Added callback to existing swap channel: ${channel} (${existingChannelSub.callbacks.size} total callbacks)`,
        );
      }

      // Return the first existing subscription ID
      return existingSwapSubs[0].id;
    }

    const subscriptionId = `swaps-${tokenAddress}-${Date.now()}`;

    console.log(`[WebSocketService] 🔥 Creating swap subscription for token: ${tokenAddress}`);
    console.log(`[WebSocketService] Existing swap subscriptions for this token: ${existingSwapSubs.length}`);

    if (existingSwapSubs.length > 0) {
      console.log(`[WebSocketService] ⚠️  Multiple swap subscriptions detected for token: ${tokenAddress}`);
      console.log(
        `[WebSocketService] Existing subscription IDs:`,
        existingSwapSubs.map((s) => s.id),
      );
      // Add stack trace to help debug where the duplicate subscription is coming from
      console.trace(`[WebSocketService] Stack trace for duplicate subscription:`);
    }

    const subscription: Subscription = {
      id: subscriptionId,
      type: "swaps",
      address: tokenAddress.toLowerCase(),
      callback,
      createdAt: new Date(),
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Add to channel subscriptions
    if (!this.channelSubscriptions.has(channel)) {
      this.channelSubscriptions.set(channel, {
        channel,
        tokenAddress: tokenAddress.toLowerCase(),
        type: "swaps",
        callbacks: new Set(),
      } as SwapChannelSubscription);
    }
    const channelSub = this.channelSubscriptions.get(channel) as SwapChannelSubscription;
    channelSub.callbacks.add(callback);

    // Subscribe to channel (only if this is the first subscription for this channel)
    if (channelSub.callbacks.size === 1) {
      this.subscribeToChannel(channel, tokenAddress, "swaps");
    } else {
      console.log(`[WebSocketService] Channel ${channel} already subscribed, adding callback only`);
    }

    console.log(`[WebSocketService] 🔥 Subscribed to swaps for token: ${tokenAddress} (ID: ${subscriptionId})`);
    console.log(
      `[WebSocketService] Total swap subscriptions: ${Array.from(this.subscriptions.values()).filter((s) => s.type === "swaps").length}`,
    );

    return subscriptionId;
  }

  /**
   * Subscribe to a channel
   */
  private subscribeToChannel(channel: string, tokenAddress: string, type: "ohlcv" | "swaps"): void {
    if (this.webSocket?.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type: "subscribe",
        channel: channel,
      };

      console.log(`[WebSocketService] 📡 Subscribing to channel: ${channel}`);
      this.webSocket.send(JSON.stringify(message));
    } else {
      // Queue for when connection is established
      console.log(`[WebSocketService] 📋 Queueing subscription for channel: ${channel}`);
      this.pendingSubscriptions.push({ channel, tokenAddress, type });

      // Ensure we're connected
      if (this.status === WebSocketStatus.DISCONNECTED) {
        this.connect().catch((error) => {
          console.error(`[WebSocketService] Failed to connect for subscription:`, error);
        });
      }
    }
  }

  /**
   * Unsubscribe from updates
   */
  public unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);

    if (!subscription) {
      console.warn(`[WebSocketService] Subscription not found: ${subscriptionId}`);
      return;
    }

    const channel =
      subscription.type === "ohlcv" ? `token_ohlcv_${subscription.address}` : `token_swaps_${subscription.address}`;

    // Remove callback from channel subscription
    const channelSub = this.channelSubscriptions.get(channel);
    if (channelSub) {
      if (channelSub.type === "ohlcv" && subscription.type === "ohlcv") {
        (channelSub as OHLCVChannelSubscription).callbacks.delete(subscription.callback);
      } else if (channelSub.type === "swaps" && subscription.type === "swaps") {
        (channelSub as SwapChannelSubscription).callbacks.delete(subscription.callback);
      }

      // If no more callbacks for this channel, unsubscribe from server
      if (channelSub.callbacks.size === 0) {
        this.unsubscribeFromChannel(channel);
        this.channelSubscriptions.delete(channel);
        console.log(`[WebSocketService] 📡 Unsubscribed from server channel: ${channel} (no more callbacks)`);
      } else {
        console.log(
          `[WebSocketService] 🔗 Keeping server channel: ${channel} (${channelSub.callbacks.size} callbacks remaining)`,
        );
      }
    }

    this.subscriptions.delete(subscriptionId);
    console.log(`[WebSocketService] ❌ Unsubscribed: ${subscriptionId}`);
  }

  /**
   * Unsubscribe from a channel
   */
  private unsubscribeFromChannel(channel: string): void {
    if (this.webSocket?.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type: "unsubscribe",
        channel: channel,
      };

      console.log(`[WebSocketService] 📡 Unsubscribing from channel: ${channel}`);
      this.webSocket.send(JSON.stringify(message));
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    console.log("[WebSocketService] 🔌 Disconnecting...");

    if (this.webSocket) {
      this.webSocket.close();
      this.webSocket = null;
    }

    this.setStatus(WebSocketStatus.DISCONNECTED);
    this.subscriptions.clear();
    this.channelSubscriptions.clear();
    this.pendingSubscriptions = [];
    // Clear connection promise on manual disconnect
    this.connectionPromise = null;
  }

  /**
   * Get current connection status
   */
  public getStatus(): WebSocketStatus {
    return this.status;
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.status === WebSocketStatus.CONNECTED && this.webSocket?.readyState === WebSocket.OPEN;
  }

  /**
   * Add status change callback
   */
  public onStatusChange(callback: (status: WebSocketStatus, error?: string) => void): () => void {
    this.statusCallbacks.add(callback);

    // Return cleanup function
    return () => {
      this.statusCallbacks.delete(callback);
    };
  }

  /**
   * Get active subscriptions count
   */
  public getSubscriptionsCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Get detailed subscription information for debugging
   */
  public getSubscriptionDetails(): {
    total: number;
    byType: { ohlcv: number; swaps: number };
    byToken: Record<string, { ohlcv: number; swaps: number }>;
    subscriptions: Array<{ id: string; type: string; address: string; createdAt: Date }>;
  } {
    const subscriptions = Array.from(this.subscriptions.values());
    const byToken: Record<string, { ohlcv: number; swaps: number }> = {};

    subscriptions.forEach((sub) => {
      if (!byToken[sub.address]) {
        byToken[sub.address] = { ohlcv: 0, swaps: 0 };
      }
      byToken[sub.address][sub.type]++;
    });

    return {
      total: subscriptions.length,
      byType: {
        ohlcv: subscriptions.filter((s) => s.type === "ohlcv").length,
        swaps: subscriptions.filter((s) => s.type === "swaps").length,
      },
      byToken,
      subscriptions: subscriptions.map((s) => ({
        id: s.id,
        type: s.type,
        address: s.address,
        createdAt: s.createdAt,
      })),
    };
  }

  /**
   * Clear all subscriptions (for testing/debugging)
   */
  public clearAllSubscriptions(): void {
    console.log(`[WebSocketService] 🧹 Clearing all ${this.subscriptions.size} subscriptions`);

    // Unsubscribe from all channels on the server
    this.channelSubscriptions.forEach((_, channel) => {
      this.unsubscribeFromChannel(channel);
    });

    // Clear local state
    this.subscriptions.clear();
    this.channelSubscriptions.clear();
    this.pendingSubscriptions = [];

    console.log(`[WebSocketService] ✅ All subscriptions cleared`);
  }

  /**
   * Set connection status and notify callbacks
   */
  private setStatus(status: WebSocketStatus, error?: string): void {
    const previousStatus = this.status;
    this.status = status;

    // Log status changes
    if (previousStatus !== status) {
      console.log(`[WebSocketService] 🔄 Status changed: ${previousStatus} → ${status}${error ? ` (${error})` : ""}`);
    }

    // Notify all status callbacks
    this.statusCallbacks.forEach((callback) => {
      try {
        callback(status, error);
      } catch (err) {
        console.error("[WebSocketService] Error in status callback:", err);
      }
    });
  }

  /**
   * Process pending subscriptions
   */
  private processPendingSubscriptions(): void {
    if (this.pendingSubscriptions.length === 0) return;

    console.log(`[WebSocketService] 📋 Processing ${this.pendingSubscriptions.length} pending subscriptions`);

    this.pendingSubscriptions.forEach(({ channel }) => {
      const message: WebSocketMessage = {
        type: "subscribe",
        channel: channel,
      };

      if (this.webSocket?.readyState === WebSocket.OPEN) {
        this.webSocket.send(JSON.stringify(message));
      }
    });

    this.pendingSubscriptions = [];
  }

  /**
   * Re-establish all active subscriptions after reconnection
   */
  private reestablishSubscriptions(pendingChannels: Set<string>): void {
    if (this.channelSubscriptions.size === 0) return;

    console.log(`[WebSocketService] 🔄 Re-establishing ${this.channelSubscriptions.size} subscriptions`);

    this.channelSubscriptions.forEach((channelSub, channel) => {
      // Skip channels that were already handled by processPendingSubscriptions
      if (pendingChannels.has(channel)) {
        console.log(`[WebSocketService] ⏭️  Skipping ${channel} - already handled by pending subscriptions`);
        return;
      }

      const message: WebSocketMessage = {
        type: "subscribe",
        channel: channel,
      };

      if (this.webSocket?.readyState === WebSocket.OPEN) {
        console.log(`[WebSocketService] 📡 Re-subscribing to channel: ${channel}`);
        this.webSocket.send(JSON.stringify(message));
      }
    });
  }
}

// Make WebSocket service persistent across hot reloads in development
declare global {
  interface Window {
    __websocketService?: WebSocketService;
    debugWebSocket?: () => void;
  }
}

// Export singleton instance with hot reload persistence
export const websocketService = (() => {
  if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
    // In development, persist the service across hot reloads
    if (!window.__websocketService) {
      window.__websocketService = WebSocketService.getInstance();
    }

    // Add debug function to window for easy console access
    window.debugWebSocket = () => {
      const details = window.__websocketService?.getSubscriptionDetails();
      console.log("🔍 WebSocket Subscription Debug Info:");
      console.log("Total subscriptions:", details?.total);
      console.log("By type:", details?.byType);
      console.log("By token:", details?.byToken);
      console.log("All subscriptions:", details?.subscriptions);
      console.log("💡 Available debug commands:");
      console.log("  - debugWebSocket() - Show this info");
      console.log("  - window.__websocketService.clearAllSubscriptions() - Clear all subscriptions");
      return details;
    };

    return window.__websocketService;
  }
  // In production, use normal singleton
  return WebSocketService.getInstance();
})();
