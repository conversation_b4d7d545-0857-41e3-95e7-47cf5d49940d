{"name": "liquidlaunch-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo 'No tests specified yet' && exit 0"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.0", "@rainbow-me/rainbowkit": "^2.2.4", "@tanstack/react-query": "^5.74.11", "@tanstack/react-table": "^8.21.3", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-fetch": "^4.1.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "framer-motion": "^12.14.0", "immer": "^10.1.1", "lucide-react": "^0.503.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "reconnecting-websocket": "^4.4.0", "siwe": "^3.0.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "viem": "2.x", "wagmi": "^2.15.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/recharts": "^2.0.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.1.3", "pino-pretty": "^13.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tw-animate-css": "^1.2.8", "typescript": "^5"}}