const CACHE_NAME = "liquidlaunch-v1";
const STATIC_CACHE = "liquidlaunch-static-v1";

// Only cache essential static assets - be very selective
const ESSENTIAL_ASSETS = ["/offline", "/manifest.json"];

// Install event - only cache truly essential assets
self.addEventListener("install", (event) => {
  console.log("Service Worker: Installing...");
  event.waitUntil(
    caches
      .open(STATIC_CACHE)
      .then((cache) => {
        console.log("Service Worker: Caching essential assets");
        return cache.addAll(ESSENTIAL_ASSETS);
      })
      .then(() => {
        console.log("Service Worker: Essential assets cached");
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error("Service Worker: Failed to cache essential assets", error);
      }),
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  console.log("Service Worker: Activating...");
  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE) {
              console.log("Service Worker: Deleting old cache", cacheName);
              return caches.delete(cacheName);
            }
          }),
        );
      })
      .then(() => {
        console.log("Service Worker: Activated");
        // Notify clients of the update after activation
        return self.clients.matchAll().then((clients) => {
          clients.forEach((client) => {
            client.postMessage({
              type: "UPDATE_AVAILABLE",
            });
          });
        });
      })
      .then(() => self.clients.claim()),
  );
});

// Fetch event - minimal caching for real-time app
self.addEventListener("fetch", (event) => {
  const { request } = event;

  // Skip unsupported schemes (chrome-extension, moz-extension, etc.)
  if (!request.url.startsWith("http://") && !request.url.startsWith("https://")) {
    return;
  }

  const url = new URL(request.url);

  // Only handle GET requests
  if (request.method !== "GET") {
    return;
  }

  // Skip ALL cross-origin requests - let the browser handle them naturally
  if (url.origin !== location.origin) {
    return;
  }

  // Handle navigation requests - provide offline fallback only
  if (request.mode === "navigate") {
    event.respondWith(
      fetch(request).catch(() => {
        // Only fall back to offline page for navigation failures
        return (
          caches.match("/offline") ||
          new Response("Offline", {
            status: 503,
            statusText: "Service Unavailable",
            headers: { "Content-Type": "text/html" },
          })
        );
      }),
    );
    return;
  }

  // DON'T cache same-origin API requests - always fetch fresh for real-time data
  if (url.pathname.startsWith("/api/")) {
    event.respondWith(fetch(request));
    return;
  }

  // DON'T cache WebSocket connections
  if (request.headers.get("upgrade") === "websocket") {
    return;
  }

  // Only cache specific static assets that rarely change
  const shouldCache =
    request.destination === "manifest" || url.pathname === "/manifest.json" || url.pathname === "/offline";

  if (shouldCache) {
    event.respondWith(
      caches.match(request).then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(request).then((response) => {
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(STATIC_CACHE).then((cache) => {
              cache.put(request, responseClone).catch((error) => {
                console.warn("Service Worker: Failed to cache request", request.url, error);
              });
            });
          }
          return response;
        });
      }),
    );
    return;
  }

  // For everything else - just fetch (no caching)
  // This ensures real-time data like prices, charts, etc. are always fresh
});

// Handle service worker updates
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
});
