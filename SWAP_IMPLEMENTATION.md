# Bonded Token Swap Implementation

This document describes the comprehensive swap implementation for bonded tokens that uses the existing DEX aggregator infrastructure instead of the bonding curve.

## Overview

When a token completes its bonding phase (`isBonded: true`), users can swap using regular DEX liquidity instead of the bonding curve mechanism. This implementation leverages your existing comprehensive DEX aggregator with full ERC20 token support, approval handling, and real-time pricing.

### Key Features

- **Automatic Token Approvals**: Handles ERC20 approvals for sell transactions
- **Real-time Balance Fetching**: Direct chain calls for accurate bonded token balances
- **Zero-downtime Quotes**: Continuous quote refreshing with stored quote fallbacks
- **Smart Button States**: Clear indication of transaction types (Approve vs Sell)
- **Native HYPE Integration**: Proper handling of WHYPE/Native HYPE address differences

## Architecture

### 1. Hook: `useBondedTokenSwap`

**Location**: `src/hooks/contracts/useBondedTokenSwap.ts`

A comprehensive React hook that handles all aspects of bonded token swaps:

#### Core Features

- **Quote Management**: Real-time quotes with 10-second refresh intervals
- **Approval Handling**: Automatic ERC20 allowance checking and approval
- **Balance Fetching**: Direct chain calls for bonded token balances
- **Transaction Execution**: Proper token path construction and value sending
- **State Management**: Comprehensive loading and error states

#### Token Configuration

```typescript
// Quote API uses WHYPE addresses for consistency
const inputToken = isBuy ? TOKENS.WHYPE_ADDRESS : tokenAddress;
const outputToken = isBuy ? tokenAddress : TOKENS.WHYPE_ADDRESS;

// Transaction paths use dead address for native HYPE on sells
if (isBuy) {
  tokenPath = [WHYPE_ADDRESS, tokenAddress];
} else {
  tokenPath = [tokenAddress, DEAD_ADDRESS]; // Native HYPE representation
}
```

#### Approval System

```typescript
// Automatic approval checking
const checkApprovalNeeded = async (amount: bigint) => {
  const currentAllowance = await publicClient.readContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: "allowance",
    args: [userAddress, MULTIHOP_ROUTER_ADDRESS],
  });
  return currentAllowance < amount;
};

// Smart button text based on approval status
if (needsApproval) swapButtonText = `Approve ${displayTargetSymbol}`;
else swapButtonText = `Sell ${displayTargetSymbol}`;
```

### 2. API Integration: `/api/swaps/quote`

**Endpoint**: Your existing comprehensive DEX aggregator route

The implementation uses human-readable amounts (not wei) for API consistency:

```typescript
GET /api/swaps/quote?inputToken=0x5555...&outputToken=0x3af7...&amount=1
```

**Key Integration Points**:

- **Amount Format**: Human-readable (e.g., "1" for 1 HYPE)
- **Token Addresses**: WHYPE addresses for both buy/sell quotes
- **Response Handling**: Supports both decimal and integer response formats

### 3. Balance Management

#### Direct Chain Calls for Bonded Tokens

```typescript
// Fetch bonded token balance directly from contract
const fetchBondedTokenBalance = async () => {
  const balance = await publicClient.readContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: "balanceOf",
    args: [userAddress],
  });
  setBondedTokenBalance(balance);
};
```

**Why Direct Calls**: The existing `/tokens/not-launched/balances` endpoint only returns balances from the launchpad contract. Bonded tokens have moved to DEXs, so we need direct ERC20 balance calls.

### 4. Quote System with Zero-Downtime

#### Interval-Based Refreshing

```typescript
// Initial quote fetch
fetchQuote();

// Refresh every 10 seconds
const quoteInterval = setInterval(() => {
  fetchQuote().catch((error) => {
    // Don't clear estimate on interval errors - keep showing last good quote
  });
}, 10000);
```

#### Stored Quote Execution

```typescript
// Use stored quote for instant execution
const handleSwap = async () => {
  let quote: SwapQuote;

  if (lastSuccessfulQuote?.success) {
    quote = lastSuccessfulQuote; // Instant execution
  } else {
    quote = await getSwapQuote(amountToSwap); // Fallback
  }

  await executeSwap(quote);
};
```

### 5. Transaction Execution

#### Value Sending Logic

```typescript
// Send native HYPE value even when using WHYPE addresses in token path
const shouldSendNativeValue =
  isBuy && (inputToken === TOKENS.WHYPE_ADDRESS || inputToken === TOKENS.NATIVE_HYPE_ADDRESS);
const valueToSend = shouldSendNativeValue ? totalAmountIn : BigInt(0);
```

#### Approval + Swap Flow

```typescript
// For sell transactions
if (!isBuy) {
  await ensureTokenApproval(tokenAddress, MULTIHOP_ROUTER_ADDRESS, totalAmountIn);
  // Shows success toast: "TOKEN approval successful!"
}

// Execute swap
await walletClient.writeContract({
  address: MULTIHOP_ROUTER_ADDRESS,
  abi: MULTIHOP_ROUTER_ABI,
  functionName: "executeMultiHopSwap",
  args: [tokenPath, totalAmountIn, minAmountOut, hopSwaps],
  value: valueToSend,
});
```

## User Experience Flow

### Buy Flow (Native HYPE → Token)

1. **Enter Amount** → Real-time quote updates
2. **Click "Buy TOKEN"** → Transaction popup immediately
3. **Sign Transaction** → Button shows "Processing..."
4. **Transaction Confirms** → Button shows "Confirming..."
5. **Success** → Balances update, success toast

### Sell Flow (Token → Native HYPE)

1. **Enter Amount** → Real-time quote updates
2. **Button State**:
   - **No Approval**: Shows "Approve TOKEN"
   - **Has Approval**: Shows "Sell TOKEN"
3. **Approval Phase** (if needed):
   - Click "Approve TOKEN" → Button shows "Approving..."
   - Sign approval → Success toast: "TOKEN approval successful!"
   - Button changes to "Sell TOKEN"
4. **Swap Phase**:
   - Click "Sell TOKEN" → Button shows "Processing..."
   - Sign transaction → Button shows "Confirming..."
   - Success → Balances update, success toast

### Button States

```typescript
// Comprehensive button text logic
if (!isConnected) swapButtonText = "Connect Wallet";
else if (!isAmountValid) swapButtonText = "Enter an Amount";
else if (isConfirming) swapButtonText = "Confirming...";
else if (isApproving) swapButtonText = "Approving...";
else if (isProcessing && !isConfirming && !isApproving) swapButtonText = "Processing...";
else if (!lastSuccessfulQuote && isLoadingEstimate) swapButtonText = "Getting Quote...";
else if (isBuy) swapButtonText = `Buy ${displayTargetSymbol}`;
else if (needsApproval) swapButtonText = `Approve ${displayTargetSymbol}`;
else swapButtonText = `Sell ${displayTargetSymbol}`;
```

### Loading Spinner Logic

```typescript
// Show spinner for any loading state
{swapButtonText.endsWith("...") ? (
  <LoadingSpinner /> {swapButtonText}
) : (
  swapButtonText
)}
```

## Configuration

### Environment Variables

```env
# Multihop router contract address
NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS=******************************************
```

### Token Constants

```typescript
WHYPE_ADDRESS: "0x5555555555555555555555555555555555555555";
NATIVE_HYPE_ADDRESS: "0x000000000000000000000000000000000000dEaD";
```

## Implementation Status

### ✅ Completed Features

- **ERC20 Approval System**: Automatic allowance checking and approval requests
- **Direct Balance Fetching**: Chain calls for accurate bonded token balances
- **Zero-downtime Quotes**: 10-second interval refreshing with stored quote fallbacks
- **Smart Button States**: Clear "Approve TOKEN" vs "Sell TOKEN" indication
- **Transaction Value Logic**: Proper native HYPE value sending with WHYPE addresses
- **Success Feedback**: Toast notifications for successful approvals and swaps
- **Loading States**: Comprehensive spinner logic for all loading states
- **Error Handling**: User-friendly error messages and retry logic
- **Amount Parsing**: Proper wei ↔ human-readable conversions for API and transactions

### 🔧 Technical Improvements

- **API Format**: Uses human-readable amounts (e.g., "1") instead of wei
- **Token Path Logic**: WHYPE for quotes, dead address for native HYPE transactions
- **Balance Source**: Direct ERC20 calls instead of launchpad API for bonded tokens
- **Quote Storage**: Reuses successful quotes for instant swap execution
- **Approval State**: Real-time allowance checking and status updates

## Integration Benefits

- **Seamless UX**: Users see clear transaction types and loading states
- **Accurate Balances**: Direct chain calls ensure correct bonded token balances
- **Fast Execution**: Stored quotes enable instant swap button responses
- **Proper Approvals**: Automatic ERC20 approval handling for sell transactions
- **Real-time Pricing**: Continuous quote updates with fallback mechanisms
- **Production Ready**: Comprehensive error handling and user feedback

## Code Integration

### SwapWidget Integration

```typescript
// Conditional swap handler selection
const dexSwap = useBondedTokenSwap({
  // ... all props including isBonded
  isBonded,
});

const {
  handleSwap,
  swapButtonText,
  bondedTokenBalance, // Direct chain balance
  // ... other properties
} = isBonded ? dexSwap : bondingCurveSwap;

// Use bonded balance for bonded tokens
const effectiveTargetTokenBalance = isBonded ? dexSwap.bondedTokenBalance : targetTokenBalanceBigInt;
```

### Hook Usage

```typescript
const swapHandler = useBondedTokenSwap({
  inputAmountRaw: "1.5",
  isBuy: false,
  tokenAddress: "0x3af7...",
  hypeTokenDecimals: 18,
  targetTokenDecimals: 18,
  hypeBalance: hypeBalanceBigInt,
  targetTokenBalance: targetTokenBalanceBigInt,
  displayTargetSymbol: "HUFF",
  slippagePercent: 0.5,
  onTransactionSuccess: handleSuccessToast,
  isBonded: true,
});

// Handler automatically manages approvals and swaps
await swapHandler.handleSwap();
```

## Future Enhancements

- **Gas Estimation**: Integrate gas estimation for approval and swap transactions
- **Approval Optimization**: Consider unlimited approvals for power users
- **Quote Aggregation**: Compare quotes across multiple intervals for best pricing
- **Advanced Slippage**: Dynamic slippage based on market conditions
- **Batch Operations**: Combine approval + swap in single transaction where possible

This implementation provides a production-ready, user-friendly bonded token swap system that leverages existing DEX infrastructure while handling all the complexities of ERC20 token interactions.
