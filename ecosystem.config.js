// Load environment variables from .env file
require("dotenv").config({ path: "/home/<USER>/.env.production" });

module.exports = {
  apps: [
    {
      name: "liquidlaunch-blue",
      script: "/home/<USER>/.bun/bin/bun",
      args: "start",
      cwd: "/home/<USER>/app/blue",
      instances: 2, // Start with 2 instances, can be scaled up
      exec_mode: "cluster",
      interpreter: "none",
      env: {
        NODE_ENV: "production",
        PORT: 7500, // Base port - PM2 will increment for each instance
        HOSTNAME: "0.0.0.0",
        PATH:
          "/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/.bun/bin:" +
          (process.env.PATH || ""),
        // Environment variables used in the application
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
        EXTERNAL_SIGNATURE_API_URL: process.env.EXTERNAL_SIGNATURE_API_URL,
        NEXT_PUBLIC_RPC_URL: process.env.NEXT_PUBLIC_RPC_URL,
        NEXT_PUBLIC_BLOCK_EXPLORER_URL: process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL,
        NEXT_PUBLIC_CHAT_WEBSOCKET_URL: process.env.NEXT_PUBLIC_CHAT_WEBSOCKET_URL,
        NEXT_PUBLIC_API_CHAT_SERVER_URL: process.env.NEXT_PUBLIC_API_CHAT_SERVER_URL,
        NEXT_PUBLIC_PRIVY_APP_ID: process.env.NEXT_PUBLIC_PRIVY_APP_ID,
        NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS: process.env.NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS,
        LIQD_AG_API_URL: process.env.LIQD_AG_API_URL,
        DEPLOYMENT_COLOR: process.env.DEPLOYMENT_COLOR,
        NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
        QUOTE_API_BASE_URL: process.env.QUOTE_API_BASE_URL,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        API_LIQUID_LAUNCH_URL: process.env.API_LIQUID_LAUNCH_URL,
      },
      error_file: "/home/<USER>/logs/blue-error.log",
      out_file: "/home/<USER>/logs/blue-out.log",
      log_file: "/home/<USER>/logs/blue-combined.log",
      time: true,
      max_memory_restart: "1G",
      node_args: "--max-old-space-size=1024",
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: "10s",
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      health_check_grace_period: 3000,
      // Cluster-specific settings
      instance_var: "INSTANCE_ID",
      increment_var: "PORT",
      merge_logs: true,
      autorestart: true,
      watch: false,
      ignore_watch: ["node_modules", "logs"],
    },
    {
      name: "liquidlaunch-green",
      script: "/home/<USER>/.bun/bin/bun",
      args: "start",
      cwd: "/home/<USER>/app/green",
      instances: 2, // Start with 2 instances, can be scaled up
      exec_mode: "cluster",
      interpreter: "none",
      env: {
        NODE_ENV: "production",
        PORT: 7520, // Different base port range for green deployment
        HOSTNAME: "0.0.0.0",
        PATH:
          "/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/.bun/bin:" +
          (process.env.PATH || ""),
        // Environment variables used in the application
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
        EXTERNAL_SIGNATURE_API_URL: process.env.EXTERNAL_SIGNATURE_API_URL,
        NEXT_PUBLIC_RPC_URL: process.env.NEXT_PUBLIC_RPC_URL,
        NEXT_PUBLIC_BLOCK_EXPLORER_URL: process.env.NEXT_PUBLIC_BLOCK_EXPLORER_URL,
        NEXT_PUBLIC_CHAT_WEBSOCKET_URL: process.env.NEXT_PUBLIC_CHAT_WEBSOCKET_URL,
        NEXT_PUBLIC_API_CHAT_SERVER_URL: process.env.NEXT_PUBLIC_API_CHAT_SERVER_URL,
        NEXT_PUBLIC_PRIVY_APP_ID: process.env.NEXT_PUBLIC_PRIVY_APP_ID,
        NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS: process.env.NEXT_PUBLIC_MULTIHOP_ROUTER_ADDRESS,
        LIQD_AG_API_URL: process.env.LIQD_AG_API_URL,
        DEPLOYMENT_COLOR: process.env.DEPLOYMENT_COLOR,
        NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
        QUOTE_API_BASE_URL: process.env.QUOTE_API_BASE_URL,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        API_LIQUID_LAUNCH_URL: process.env.API_LIQUID_LAUNCH_URL,
      },
      error_file: "/home/<USER>/logs/green-error.log",
      out_file: "/home/<USER>/logs/green-out.log",
      log_file: "/home/<USER>/logs/green-combined.log",
      time: true,
      max_memory_restart: "1G",
      node_args: "--max-old-space-size=1024",
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: "10s",
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 8000,
      health_check_grace_period: 3000,
      // Cluster-specific settings
      instance_var: "INSTANCE_ID",
      increment_var: "PORT",
      merge_logs: true,
      autorestart: true,
      watch: false,
      ignore_watch: ["node_modules", "logs"],
    },
  ],

  // Development environment configuration
  deploy: {
    development: {
      user: "liquidlaunch-web-dev",
      host: "ssh.web.seipex.fi",
      ref: "origin/dev",
      repo: "**************:your-username/liquidlaunch-frontend.git",
      path: "/home/<USER>/app",
      "pre-deploy-local": "",
      "post-deploy": "bun install --production && bun run build && pm2 reload ecosystem.config.js --env development",
      "pre-setup": "",
      env: {
        NODE_ENV: "development",
        PORT: 3001,
      },
    },

    // Production environment configuration
    production: {
      user: "liquidlaunch-web-prod",
      host: "ssh.web.seipex.fi",
      ref: "origin/release",
      repo: "**************:your-username/liquidlaunch-frontend.git",
      path: "/home/<USER>/app",
      "pre-deploy-local": "",
      "post-deploy": "bun install --production && bun run build && pm2 reload ecosystem.config.js --env production",
      "pre-setup": "",
      env: {
        NODE_ENV: "production",
      },
    },
  },
};
