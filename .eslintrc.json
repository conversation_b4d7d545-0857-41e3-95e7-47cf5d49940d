{"extends": ["next/core-web-vitals", "next/typescript"], "ignorePatterns": ["public/static/charting_library/**/*", "public/static/datafeeds/**/*", "node_modules/**/*", ".next/**/*", "out/**/*", "build/**/*"], "rules": {"@next/next/no-img-element": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "react/no-unescaped-entities": "warn", "prefer-const": "warn"}}